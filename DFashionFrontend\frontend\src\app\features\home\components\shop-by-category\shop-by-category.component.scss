.shop-categories-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;
}

// Header Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    flex: 1;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #6c5ce7;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

// Loading States
.loading-container {
  .loading-grid {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .loading-category-card {
    flex: 0 0 180px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 16px;
    overflow: hidden;
    
    .loading-image {
      width: 100%;
      height: 120px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    .loading-content {
      padding: 16px;
      
      .loading-line {
        height: 12px;
        border-radius: 6px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-bottom: 8px;
        
        &.short { width: 60%; }
        &.medium { width: 80%; }
        &.long { width: 100%; }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
  }
  
  .error-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
  }
  
  .retry-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    }
  }
}

// Categories Slider Styles
.categories-slider-container {
  position: relative;
  margin: 0 -20px;
  
  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }
    
    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
    
    ion-icon {
      font-size: 18px;
    }
    
    &.prev-btn {
      left: -20px;
    }
    
    &.next-btn {
      right: -20px;
    }
  }
}

.categories-slider-wrapper {
  overflow: hidden;
  padding: 0 20px;
}

.categories-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 20px;
  
  .category-card {
    flex: 0 0 180px;
    width: 180px;
  }
}

// Category Card Styles
.category-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    
    .category-overlay {
      opacity: 1;
    }
    
    .category-image {
      transform: scale(1.1);
    }
  }
  
  &.trending {
    border: 2px solid #ff6b6b;
    
    .category-name {
      color: #ff6b6b;
    }
  }
}

.category-image-container {
  position: relative;
  height: 120px;
  overflow: hidden;
  
  .category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .trending-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    
    ion-icon {
      font-size: 12px;
    }
  }
  
  .discount-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
  }
  
  .category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    .explore-icon {
      font-size: 24px;
      color: white;
    }
  }
}

.category-info {
  padding: 16px;
  
  .category-name {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 6px 0;
    line-height: 1.2;
  }
  
  .category-description {
    font-size: 12px;
    color: #666;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }
  
  .product-count {
    font-size: 11px;
    color: #6c5ce7;
    font-weight: 600;
    margin: 0 0 12px 0;
  }
}

.subcategories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  
  .subcategory-tag {
    font-size: 9px;
    background: rgba(108, 92, 231, 0.1);
    color: #6c5ce7;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
  }
  
  .more-subcategories {
    font-size: 9px;
    color: #999;
    font-weight: 500;
  }
}

// Empty State
.empty-state, .empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #666;
    margin: 0 0 8px 0;
  }
  
  .empty-message {
    color: #999;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .categories-slider {
    .category-card {
      flex: 0 0 170px;
      width: 170px;
    }
  }
}

// Desktop/Laptop Responsiveness (768px and above)
@media (min-width: 769px) {
  .categories-slider {
    .category-card {
      // Responsive card width based on screen size
      flex: 0 0 calc(25% - 15px); // 4 cards per row on medium screens
      width: calc(25% - 15px);
      max-width: 200px;
    }
  }
}

@media (min-width: 1024px) {
  .categories-slider {
    .category-card {
      flex: 0 0 calc(20% - 16px); // 5 cards per row on large screens
      width: calc(20% - 16px);
      max-width: 190px;
    }
  }
}

@media (min-width: 1200px) {
  .categories-slider {
    .category-card {
      flex: 0 0 calc(16.666% - 17px); // 6 cards per row on extra large screens
      width: calc(16.666% - 17px);
      max-width: 180px;
    }
  }
}

@media (min-width: 1440px) {
  .categories-slider {
    .category-card {
      flex: 0 0 calc(14.285% - 18px); // 7 cards per row on very large screens
      width: calc(14.285% - 18px);
      max-width: 170px;
    }
  }
}

// Mobile/Tablet Responsiveness (768px and below) - Keep existing
@media (max-width: 768px) {
  .categories-slider-container {
    margin: 0 -10px;
    
    .slider-nav {
      width: 35px;
      height: 35px;
      
      &.prev-btn {
        left: -15px;
      }
      
      &.next-btn {
        right: -15px;
      }
      
      ion-icon {
        font-size: 16px;
      }
    }
  }
  
  .categories-slider-wrapper {
    padding: 0 10px;
  }
  
  .categories-slider {
    gap: 15px;
    
    .category-card {
      flex: 0 0 160px;
      width: 160px;
    }
  }
  
  .category-image-container {
    height: 100px;
  }
  
  .category-info {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .categories-slider {
    .category-card {
      flex: 0 0 150px;
      width: 150px;
    }
  }
}
