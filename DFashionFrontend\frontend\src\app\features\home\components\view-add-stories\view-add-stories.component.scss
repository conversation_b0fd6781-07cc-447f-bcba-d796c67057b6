// Instagram-style Stories Container - Fully Responsive
.stories-container {
  background: white;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;

  // Mobile responsive (up to 768px)
  @media (max-width: 768px) {
    padding: 12px 8px;
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #efefef;
    background: #ffffff;
    box-shadow: none;
    margin-bottom: 0;

    // Ensure touch interactions work
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }

  // Web responsive design (769px and up)
  @media (min-width: 769px) {
    background: #ffffff;
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    // Make it prominent in the marked area
    position: relative;
    z-index: 10;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 150px; // Ensure minimum height
  }
}

// Stories Header for Web
.stories-header {
  margin-bottom: 20px;

  @media (min-width: 769px) {
    border-bottom: 1px solid #efefef;
    padding-bottom: 16px;
    margin-bottom: 24px;
  }
}

.stories-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 4px 0;

  @media (min-width: 769px) {
    font-size: 20px;
    font-weight: 700;
  }
}

.stories-subtitle {
  font-size: 14px;
  color: #8e8e8e;
  margin: 0;

  @media (min-width: 769px) {
    font-size: 15px;
  }
}

// Stories Section Layout - Enhanced for Web Responsive
.stories-section {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 0;

  // Web responsive design
  @media (min-width: 769px) {
    padding: 0;
    gap: 20px;
    align-items: center;
    min-height: 120px;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    padding: 0 16px;
    gap: 16px;
  }
}

// Static Add Story Button (Left Side)
.add-story-static {
  flex-shrink: 0;
  width: 82px; // Fixed width to match story item
}

// Stories Slider Container (Right Side) - Mobile Responsive
.stories-slider-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;

  // Mobile responsive
  @media (max-width: 768px) {
    max-width: calc(100% - 70px); // Account for smaller add story button on mobile

    // Enable touch scrolling
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x;
  }

  // Desktop
  @media (min-width: 769px) {
    max-width: calc(100% - 98px); // Account for add story button + gap
  }

  // Gradient overlay to indicate scrollable content (desktop only)
  @media (min-width: 769px) {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 100%;
      background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
      pointer-events: none;
      z-index: 5;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &.has-overflow::after {
      opacity: 1;
    }
  }
}

// Owl Carousel Stories Container - Mobile Responsive
.stories-slider-container {
  position: relative;
  width: 100%;
  overflow: visible; /* allows children to overflow */

  // Mobile responsive
  @media (max-width: 768px) {
    // Ensure touch interactions work properly
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }

  // Owl Carousel Custom Styles
  ::ng-deep {
    .owl-carousel {
      .owl-stage-outer {
        padding: 0;

        // Mobile touch optimization
        @media (max-width: 768px) {
          overflow: hidden;
          touch-action: pan-x;
        }
      }

      .owl-stage {
        display: flex;
        align-items: flex-start;

        // Mobile optimization
        @media (max-width: 768px) {
          touch-action: pan-x;
          will-change: transform;
        }
      }

      .owl-item {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        min-height: 120px;

        // Mobile responsive height
        @media (max-width: 768px) {
          min-height: 90px;
        }
      }

      // Custom Navigation Arrows
      .owl-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        pointer-events: none;

        .owl-prev,
        .owl-next {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: rgba(0, 0, 0, 0.5); /* Dark background */
          color: #fff; /* White arrow color */
          border: none;
          display: none; /* Temporarily hidden */
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          transition: all 0.2s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          pointer-events: all;

          &:hover {
            background: rgba(0, 0, 0, 0.7);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateY(-50%) scale(1.1);
          }

          &:active {
            transform: translateY(-50%) scale(0.95);
          }

          i, .fas {
            font-size: 14px;
            color: #fff;
          }
        }

        .owl-prev {
          left: -20px; /* Position outside container */
        }

        .owl-next {
          right: -20px; /* Position outside container */
        }
      }

      // Hide navigation on mobile
      @media (max-width: 768px) {
        .owl-nav {
          display: none;
        }
      }

      // Auto-play indicator (subtle animation)
      &.owl-loaded {
        .owl-stage-outer {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);
            opacity: 0.3;
            animation: autoSlideIndicator 4s infinite linear;
          }
        }
      }
    }
  }
}

// Auto-slide indicator animation
@keyframes autoSlideIndicator {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Individual Story Slide
.story-slide {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  width: 66px;

  &:hover {
    transform: scale(1.05);
    // Pause auto sliding on hover
    animation-play-state: paused;
  }
}

// Navigation Arrows for Stories Slider
.slider-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-50%) scale(1.1);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }

  i {
    font-size: 12px;
    color: #262626;
  }
}

.slider-nav-left {
  left: -16px;
}

.slider-nav-right {
  right: -16px;
}

.story-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.3s ease;
  width: 82px; // Fixed width for consistent layout
  min-width: 82px;
  position: relative;

  // Enhanced web responsive design
  @media (min-width: 769px) {
    width: 90px;
    min-width: 90px;
    padding: 8px;
    border-radius: 12px;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      transform: scale(1.08);
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    width: 76px;
    min-width: 76px;
  }

  &:hover {
    transform: scale(1.05);

    .story-ring {
      animation-duration: 1s; // Faster animation on hover
    }

    .story-username {
      color: #0095f6;
      font-weight: 600;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

// Enhanced story-slide styles (inherits from story-item)
.story-slide {
  @extend .story-item;

  &.active {
    .story-ring {
      background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
      animation: pulse 2s infinite;
    }

    .story-username {
      color: #405de6;
      font-weight: 600;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.story-avatar-container {
  position: relative;
  margin-bottom: 8px;

  // Mobile responsive
  @media (max-width: 768px) {
    margin-bottom: 6px;
  }
}

.story-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;

  // Enhanced web responsive design
  @media (min-width: 769px) {
    width: 74px;
    height: 74px;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    border: 1.5px solid #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.story-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  z-index: 1;
  animation: pulse 2s infinite;

  // Enhanced web responsive design
  @media (min-width: 769px) {
    top: -3px;
    left: -3px;
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    box-shadow: 0 0 20px rgba(240, 148, 51, 0.3);
  }

  // Mobile responsive
  @media (max-width: 768px) {
    width: 64px;
    height: 64px;
    top: -2px;
    left: -2px;
  }

  // Viewed state (gray ring)
  &.viewed {
    background: #c7c7c7;
    animation: none;
  }

  // Active state (enhanced gradient with faster animation)
  &.active {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    animation: pulse 1.5s infinite;
    box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.story-username {
  font-size: 12px;
  color: #262626;
  font-weight: 400;
  max-width: 74px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  margin-top: 8px;

  // Enhanced web responsive design
  @media (min-width: 769px) {
    font-size: 13px;
    font-weight: 500;
    max-width: 90px;
    margin-top: 10px;
    color: #262626;

    // Better text rendering for web
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    font-size: 11px;
    max-width: 70px;
    font-weight: 500;
    margin-top: 6px;
  }
}

// Add Story Button Styles
.add-story-item {
  .story-username {
    font-weight: 600;
    color: #262626;
  }
}

.add-story-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  position: relative;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.add-story-icon {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background: #0095f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  z-index: 3;

  i {
    color: white;
    font-size: 10px;
    font-weight: bold;
  }
}

.current-user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid white;
}

// Loading State for Stories
.stories-loading {
  display: flex;
  gap: 16px;
  padding: 0 16px;
}

.story-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-name {
  width: 60px;
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.story-bar__user {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &.bounce {
    animation: bounce 0.3s ease;
  }
}

.story-bar__user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    z-index: -1;
  }
}

.story-bar__user-name {
  margin-top: 4px;
  font-size: 12px;
  color: #262626;
  text-align: center;
  max-width: 64px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Stories Viewer
.stories-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 9999;
  perspective: 400px;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  
  &.is-open {
    opacity: 1;
    visibility: visible;
  }
}

.stories {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: translateZ(-50vw);
  transition: transform 0.25s ease-out;
  
  &.is-closed {
    opacity: 0;
    transform: scale(0.1);
  }
}

// Story Progress Bars
.story-progress {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  gap: 2px;
  z-index: 100;
}

.story-progress__bar {
  flex: 1;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
  overflow: hidden;
  
  &.completed .story-progress__fill {
    width: 100%;
  }
  
  &.active .story-progress__fill {
    animation: progress 15s linear;
  }
}

.story-progress__fill {
  height: 100%;
  background: #fff;
  width: 0%;
  transition: width 0.1s ease;
}

@keyframes progress {
  from { width: 0%; }
  to { width: 100%; }
}

// Individual Story
.story {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  user-select: none;
}

.story__top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 48px 16px 16px;
  background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.story__details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.story__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid #fff;
}

.story__user {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

.story__time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.story__views {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  margin-left: 8px;
}

.story__close {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

// Story Content
.story__content {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.story__video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story__image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

// Story Caption
.story__caption {
  position: absolute;
  bottom: 120px;
  left: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 1.4;
  backdrop-filter: blur(10px);
  z-index: 5;
}

// Product Tags
.story__product-tags {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  z-index: 6;
}

.product-tag {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 8px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;

  &:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.product-tag-icon {
  font-size: 16px;
}

.product-tag-info {
  flex: 1;
}

.product-tag-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.2;
}

.product-tag-price {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

// Story Bottom Actions
.story__bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);
  z-index: 10;
}

.story__actions {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.story__action-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }
}

// E-commerce Actions
.story__ecommerce-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.ecommerce-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &.buy-now-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }
  }

  &.wishlist-btn {
    background: linear-gradient(45deg, #ff9ff3, #f368e0);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);
    }
  }

  &.cart-btn {
    background: linear-gradient(45deg, #54a0ff, #2e86de);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);
    }
  }
}

// Navigation Areas (Invisible)
.story__nav-area {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 33%;
  z-index: 5;
  cursor: pointer;

  &.story__nav-prev {
    left: 0;
  }

  &.story__nav-next {
    right: 0;
    width: 67%;
  }
}

// Feed Cover
.feed__cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: -1;

  &.is-hidden {
    opacity: 0;
  }
}

// Touch Indicators (Mobile)
.touch-indicators {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 101;
  pointer-events: none;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
}

.touch-indicator {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  animation: fadeInOut 3s infinite;

  &.left {
    left: 16px;
  }

  &.right {
    right: 16px;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(0.8); }
}

// Enhanced Mobile Optimization
@media (max-width: 1024px) {
  .story-bar {
    padding: 12px 16px;
    gap: 10px;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .stories-wrapper {
    touch-action: pan-y;
  }

  .story {
    touch-action: manipulation;
  }

  .stories-section {
    gap: 12px;
    padding: 0 12px;
  }

  .add-story-static {
    width: 70px;
  }

  .stories-slider-wrapper {
    max-width: calc(100% - 82px);
  }

  .story-item {
    width: 70px;
    min-width: 70px;
  }

  .stories-list {
    gap: 12px;
  }

  // Hide navigation arrows on tablet
  .slider-nav-btn {
    display: none;
  }
}

@media (max-width: 768px) {
  .story-bar {
    padding: 8px 12px;
    gap: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .stories-section {
    gap: 10px;
    padding: 0 8px;
  }

  .add-story-static {
    width: 60px;
  }

  .stories-slider-wrapper {
    max-width: calc(100% - 70px);
  }

  .story-item {
    width: 60px;
    min-width: 60px;
  }

  .stories-list {
    gap: 10px;
  }

  // Hide navigation arrows on mobile
  .slider-nav-btn {
    display: none;
  }

  .story-avatar {
    width: 56px;
    height: 56px;
  }

  .story-ring {
    width: 60px;
    height: 60px;
    top: -2px;
    left: -2px;
  }

  .add-story-avatar {
    width: 56px;
    height: 56px;
  }

  .current-user-avatar {
    width: 50px;
    height: 50px;
  }

  .story-username {
    font-size: 11px;
    max-width: 60px;
  }

  .story-bar__user-avatar {
    width: 48px;
    height: 48px;

    &::before {
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
    }
  }

  .story-bar__user-name {
    font-size: 11px;
    max-width: 56px;
  }

  .story__top {
    padding: 40px 12px 12px;
  }

  .story__bottom {
    padding: 12px;
  }

  .story__ecommerce-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: space-between;
  }

  .ecommerce-btn {
    padding: 8px 12px;
    font-size: 11px;
    flex: 1;
    min-width: 80px;

    i {
      font-size: 12px;
    }
  }

  .story__actions {
    gap: 12px;
    margin-bottom: 8px;
  }

  .story__action-btn {
    font-size: 18px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .story-bar {
    padding: 6px 8px;
    gap: 6px;
  }

  .story-bar__user-avatar {
    width: 40px;
    height: 40px;

    &::before {
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
    }
  }

  .story-bar__user-name {
    font-size: 10px;
    max-width: 48px;
  }

  .story__top {
    padding: 32px 8px 8px;
  }

  .story__bottom {
    padding: 8px;
  }

  .story__ecommerce-actions {
    flex-direction: column;
    gap: 4px;
  }

  .ecommerce-btn {
    padding: 6px 8px;
    font-size: 10px;

    i {
      font-size: 10px;
    }
  }

  .story__actions {
    gap: 8px;
    margin-bottom: 6px;
  }

  .story__action-btn {
    font-size: 16px;
    padding: 4px;
  }

  .story__user {
    font-size: 12px;
  }

  .story__time {
    font-size: 10px;
  }

  .story__avatar {
    width: 28px;
    height: 28px;
  }
}

// Enhanced Touch Interactions
@media (hover: none) and (pointer: coarse) {
  .story-bar__user {
    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  .ecommerce-btn {
    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  .story__action-btn {
    &:active {
      transform: scale(0.9);
      transition: transform 0.1s ease;
    }
  }

  .story__close {
    &:active {
      transform: scale(0.9);
      transition: transform 0.1s ease;
    }
  }
}

// Landscape Mobile Optimization
@media (max-width: 896px) and (orientation: landscape) {
  .story__top {
    padding: 24px 12px 8px;
  }

  .story__bottom {
    padding: 8px 12px;
  }

  .story__ecommerce-actions {
    flex-direction: row;
    gap: 8px;
  }

  .ecommerce-btn {
    padding: 6px 10px;
    font-size: 10px;
  }
}

/* Middle Navigation Button */
.middle-navigation {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;

  .middle-nav-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 16px;
    }

    span {
      font-size: 14px;
    }
  }
}

// High DPI Displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .story-bar__user-avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .story__avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

// Mobile responsive for middle navigation
@media (max-width: 768px) {
  .middle-navigation {
    bottom: 80px;

    .middle-nav-btn {
      padding: 10px 20px;
      font-size: 12px;

      i {
        font-size: 14px;
      }

      span {
        font-size: 12px;
      }
    }
  }
}

// ===== E-COMMERCE SPECIFIC STYLES =====

// Brand Badge for Brand Stories
.brand-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ffd700, #ffb300);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  z-index: 10;

  i {
    font-size: 10px;
    color: white;
  }

  @media (min-width: 769px) {
    width: 24px;
    height: 24px;

    i {
      font-size: 12px;
    }
  }
}

// New Product Badge
.new-product-badge {
  position: absolute;
  top: -4px;
  left: -4px;
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  font-size: 8px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid white;
  z-index: 10;

  span {
    font-size: 8px;
    letter-spacing: 0.5px;
  }

  @media (min-width: 769px) {
    font-size: 9px;
    padding: 3px 7px;

    span {
      font-size: 9px;
    }
  }
}

// Brand Ring for Brand Stories
.story-ring.brand-ring {
  background: linear-gradient(45deg, #ffd700, #ffb300, #ff6b6b, #4ecdc4);
  background-size: 300% 300%;
  animation: brandGradient 3s ease infinite;
}

@keyframes brandGradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

// Enhanced Add Story Slide
.add-story-slide {
  .story-avatar-container {
    position: relative;

    .add-story-avatar {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #dbdbdb;

      @media (min-width: 769px) {
        width: 74px;
        height: 74px;
        border: 3px solid #dbdbdb;
      }

      .current-user-avatar {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-color: #f5f5f5;
      }

      .add-story-icon {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 24px;
        height: 24px;
        background: #0095f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        z-index: 10;

        i {
          font-size: 12px;
          color: white;
        }

        @media (min-width: 769px) {
          width: 28px;
          height: 28px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Brand Story Slide Enhancements
.brand-story-slide {
  .story-avatar-container {
    position: relative;

    .story-avatar {
      position: relative;
      overflow: visible; // Allow badges to show outside
    }
  }
}
