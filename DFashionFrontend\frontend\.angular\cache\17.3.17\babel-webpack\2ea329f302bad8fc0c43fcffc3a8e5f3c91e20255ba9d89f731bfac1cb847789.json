{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../shop-by-category/shop-by-category.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    console.log('🏠 SidebarComponent: ngOnInit called');\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n    console.log('🏠 SidebarComponent: Initialization complete');\n  }\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [{\n      id: '1',\n      username: 'fashionista_maya',\n      fullName: 'Maya Chen',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      followedBy: 'Followed by 12 others',\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_guru_alex',\n      fullName: 'Alex Rodriguez',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      followedBy: 'Followed by 8 others',\n      isFollowing: false\n    }];\n  }\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [{\n      id: '1',\n      username: 'fashion_queen',\n      fullName: 'Priya Sharma',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n      followersCount: 25000,\n      postsCount: 156,\n      engagement: 8.5,\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_maven',\n      fullName: 'Kavya Reddy',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n      followersCount: 18000,\n      postsCount: 89,\n      engagement: 12.3,\n      isFollowing: true\n    }];\n  }\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [{\n      id: '1',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n    }, {\n      id: '2',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n    }, {\n      id: '3',\n      name: 'Accessories',\n      slug: 'accessories',\n      image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n    }, {\n      id: '4',\n      name: 'Footwear',\n      slug: 'footwear',\n      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 27,\n      vars: 0,\n      consts: [[1, \"sidebar\"], [2, \"background\", \"#333\", \"color\", \"white\", \"padding\", \"10px\", \"margin\", \"10px 0\", \"text-align\", \"center\", \"font-weight\", \"bold\", \"border-radius\", \"5px\"], [2, \"border\", \"2px solid cyan\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"cyan\", \"color\", \"black\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [2, \"border\", \"2px solid red\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"red\", \"color\", \"white\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [2, \"border\", \"2px solid green\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"green\", \"color\", \"white\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [2, \"border\", \"2px solid blue\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"blue\", \"color\", \"white\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [2, \"border\", \"2px solid purple\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"purple\", \"color\", \"white\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [2, \"border\", \"2px solid orange\", \"margin\", \"5px 0\", \"padding\", \"5px\"], [2, \"background\", \"orange\", \"color\", \"white\", \"padding\", \"5px\", \"font-size\", \"12px\", \"font-weight\", \"bold\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0)(1, \"div\", 1);\n          i0.ɵɵtext(2, \" \\uD83C\\uDFE0 SIDEBAR COMPONENT LOADED - COMPONENTS SHOULD APPEAR BELOW \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵtext(5, \"TRENDING PRODUCTS:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"app-trending-products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5);\n          i0.ɵɵtext(9, \"FEATURED BRANDS:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"app-featured-brands\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7);\n          i0.ɵɵtext(13, \"NEW ARRIVALS:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"app-new-arrivals\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9);\n          i0.ɵɵtext(17, \"SUGGESTED FOR YOU:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"app-suggested-for-you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11);\n          i0.ɵɵtext(21, \"TOP FASHION INFLUENCERS:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"app-top-fashion-influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"div\", 13);\n          i0.ɵɵtext(25, \"SHOP BY CATEGORY:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"app-shop-by-category\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule, RouterModule, IonicModule, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent, ShopByCategoryComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\napp-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%], app-suggested-for-you[_ngcontent-%COMP%], app-top-fashion-influencers[_ngcontent-%COMP%], app-shop-by-category[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 16px;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container, app-featured-brands[_ngcontent-%COMP%]     .component-container, app-new-arrivals[_ngcontent-%COMP%]     .component-container, app-suggested-for-you[_ngcontent-%COMP%]     .component-container, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container, app-shop-by-category[_ngcontent-%COMP%]     .component-container {\\n  background: #ffffff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header {\\n  padding: 16px;\\n  border-bottom: 1px solid #efefef;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header h3, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header h3, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header h3, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header h3, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header h3, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header h3 {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all {\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  text-decoration: none;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover {\\n  text-decoration: underline;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-content, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-content, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-content, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-content, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-content, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-content {\\n  padding: 16px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  background: #f8f9fa;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(232, 67, 147, 0.3);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  text-decoration: none;\\n  color: inherit;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 3px solid #f8f9fa;\\n  transition: transform 0.3s ease;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  text-align: center;\\n}\\n\\n@media (max-width: 768px) {\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n    font-size: 24px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .category-list[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin-bottom: 0;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "ShopByCategoryComponent", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "topInfluencers", "categories", "ngOnInit", "console", "log", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "id", "username", "fullName", "avatar", "<PERSON><PERSON><PERSON>", "isFollowing", "followersCount", "postsCount", "engagement", "name", "slug", "image", "formatFollowerCount", "count", "toFixed", "toString", "followUser", "userId", "user", "find", "u", "followInfluencer", "influencerId", "influencer", "i", "quickBuy", "productId", "browseCategory", "categorySlug", "navigate", "i0", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../shop-by-category/shop-by-category.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent,\n    ShopByCategoryComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    console.log('🏠 SidebarComponent: ngOnInit called');\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n    console.log('🏠 SidebarComponent: Initialization complete');\n  }\n\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [\n      {\n        id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followedBy: 'Followed by 12 others',\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followedBy: 'Followed by 8 others',\n        isFollowing: false\n      }\n    ];\n  }\n\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [\n      {\n        id: '1',\n        username: 'fashion_queen',\n        fullName: 'Priya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        followersCount: 25000,\n        postsCount: 156,\n        engagement: 8.5,\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_maven',\n        fullName: 'Kavya Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 18000,\n        postsCount: 89,\n        engagement: 12.3,\n        isFollowing: true\n      }\n    ];\n  }\n\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [\n      {\n        id: '1',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n      },\n      {\n        id: '2',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n      },\n      {\n        id: '3',\n        name: 'Accessories',\n        slug: 'accessories',\n        image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n      },\n      {\n        id: '4',\n        name: 'Footwear',\n        slug: 'footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- DEBUG: Sidebar Loading -->\n  <div style=\"background: #333; color: white; padding: 10px; margin: 10px 0; text-align: center; font-weight: bold; border-radius: 5px;\">\n    🏠 SIDEBAR COMPONENT LOADED - COMPONENTS SHOULD APPEAR BELOW\n  </div>\n\n  <!-- Trending Products Section -->\n  <div style=\"border: 2px solid cyan; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: cyan; color: black; padding: 5px; font-size: 12px; font-weight: bold;\">TRENDING PRODUCTS:</div>\n    <app-trending-products></app-trending-products>\n  </div>\n\n  <!-- Featured Brands Section -->\n  <div style=\"border: 2px solid red; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: red; color: white; padding: 5px; font-size: 12px; font-weight: bold;\">FEATURED BRANDS:</div>\n    <app-featured-brands></app-featured-brands>\n  </div>\n\n  <!-- New Arrivals Section -->\n  <div style=\"border: 2px solid green; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: green; color: white; padding: 5px; font-size: 12px; font-weight: bold;\">NEW ARRIVALS:</div>\n    <app-new-arrivals></app-new-arrivals>\n  </div>\n\n  <!-- Suggested for you -->\n  <div style=\"border: 2px solid blue; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: blue; color: white; padding: 5px; font-size: 12px; font-weight: bold;\">SUGGESTED FOR YOU:</div>\n    <app-suggested-for-you></app-suggested-for-you>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div style=\"border: 2px solid purple; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: purple; color: white; padding: 5px; font-size: 12px; font-weight: bold;\">TOP FASHION INFLUENCERS:</div>\n    <app-top-fashion-influencers></app-top-fashion-influencers>\n  </div>\n\n  <!-- Shop by Category -->\n  <div style=\"border: 2px solid orange; margin: 5px 0; padding: 5px;\">\n    <div style=\"background: orange; color: white; padding: 5px; font-size: 12px; font-weight: bold;\">SHOP BY CATEGORY:</div>\n    <app-shop-by-category></app-shop-by-category>\n  </div>\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,8BAA8B,QAAQ,8DAA8D;AAC7G,SAASC,uBAAuB,QAAQ,gDAAgD;;;;AAmBxF,OAAM,MAAOC,gBAAgB;EAM3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,UAAU,GAAU,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;IACrBL,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEAC,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACP,cAAc,GAAG,CACpB;MACEW,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,kBAAkB;MAC5BC,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,oEAAoE;MAC5EC,UAAU,EAAE,uBAAuB;MACnCC,WAAW,EAAE;KACd,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,iBAAiB;MAC3BC,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE,oEAAoE;MAC5EC,UAAU,EAAE,sBAAsB;MAClCC,WAAW,EAAE;KACd,CACF;EACH;EAEAR,oBAAoBA,CAAA;IAClB,IAAI,CAACP,gBAAgB,GAAG,EAAE;EAC5B;EAEAQ,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACP,cAAc,GAAG,CACpB;MACES,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE,cAAc;MACxBC,MAAM,EAAE,oEAAoE;MAC5EG,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfH,WAAW,EAAE;KACd,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE,aAAa;MACvBC,MAAM,EAAE,iEAAiE;MACzEG,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,IAAI;MAChBH,WAAW,EAAE;KACd,CACF;EACH;EAEAN,cAAcA,CAAA;IACZ;IACA,IAAI,CAACP,UAAU,GAAG,CAChB;MACEQ,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;EACH;EAEAC,mBAAmBA,CAACC,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAC,UAAUA,CAACC,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKiB,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACb,WAAW,GAAG,CAACa,IAAI,CAACb,WAAW;;EAExC;EAEAgB,gBAAgBA,CAACC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAChC,cAAc,CAAC4B,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACxB,EAAE,KAAKsB,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAClB,WAAW,GAAG,CAACkB,UAAU,CAAClB,WAAW;;EAEpD;EAEAoB,QAAQA,CAACC,SAAiB;IACxBhC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+B,SAAS,CAAC;IAC5C;EACF;EAEAC,cAAcA,CAACC,YAAoB;IACjClC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiC,YAAY,CAAC;IAC7C,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBAnIW3C,gBAAgB,EAAA6C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBlD,gBAAgB;MAAAmD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7B3Bf,EAFF,CAAAiB,cAAA,eAAuB,aAEkH;UACrIjB,EAAA,CAAAkB,MAAA,+EACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,aAAkE,aAC+B;UAAAjB,EAAA,CAAAkB,MAAA,yBAAkB;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UACvHnB,EAAA,CAAAoB,SAAA,4BAA+C;UACjDpB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,aAAiE,aAC+B;UAAAjB,EAAA,CAAAkB,MAAA,uBAAgB;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UACpHnB,EAAA,CAAAoB,SAAA,2BAA2C;UAC7CpB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,cAAmE,cAC+B;UAAAjB,EAAA,CAAAkB,MAAA,qBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UACnHnB,EAAA,CAAAoB,SAAA,wBAAqC;UACvCpB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,cAAkE,cAC+B;UAAAjB,EAAA,CAAAkB,MAAA,0BAAkB;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UACvHnB,EAAA,CAAAoB,SAAA,6BAA+C;UACjDpB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,eAAoE,eAC+B;UAAAjB,EAAA,CAAAkB,MAAA,gCAAwB;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UAC/HnB,EAAA,CAAAoB,SAAA,mCAA2D;UAC7DpB,EAAA,CAAAmB,YAAA,EAAM;UAIJnB,EADF,CAAAiB,cAAA,eAAoE,eAC+B;UAAAjB,EAAA,CAAAkB,MAAA,yBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UACxHnB,EAAA,CAAAoB,SAAA,4BAA6C;UAEjDpB,EADE,CAAAmB,YAAA,EAAM,EACA;;;qBDvBJzE,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B,EAC9BC,uBAAuB;MAAAmE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}