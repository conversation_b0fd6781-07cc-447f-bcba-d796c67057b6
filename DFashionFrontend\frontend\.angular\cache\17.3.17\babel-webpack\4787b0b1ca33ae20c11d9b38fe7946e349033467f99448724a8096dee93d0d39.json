{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_6_div_1_Template, 3, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"span\");\n    i0.ɵɵtext(2, \"NEW\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 30);\n    i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template, 2, 0, \"div\", 31)(4, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.user.isBrand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasNewProducts);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed)(\"brand-ring\", story_r5.user.isBrand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template, 8, 9, \"ng-template\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_7_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 23);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"div\", 25)(12, \"owl-carousel-o\", 26);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_7_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_7_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_7_ng_container_13_Template, 2, 0, \"ng-container\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 76);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 77)(4, \"div\", 78);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_8_div_21_div_1_Template, 8, 2, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 85);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 59);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 21);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38, 0)(3, \"div\", 39);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_8_div_4_Template, 2, 4, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_8_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_8_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_8_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"div\", 43);\n    i0.ɵɵelement(8, \"div\", 44);\n    i0.ɵɵelementStart(9, \"div\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 46);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 47);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 50);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_8_video_18_Template, 1, 1, \"video\", 51)(19, ViewAddStoriesComponent_div_8_div_19_Template, 1, 2, \"div\", 52)(20, ViewAddStoriesComponent_div_8_div_20_Template, 2, 1, \"div\", 53)(21, ViewAddStoriesComponent_div_8_div_21_Template, 2, 1, \"div\", 54)(22, ViewAddStoriesComponent_div_8_div_22_Template, 5, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 56)(24, \"div\", 57)(25, \"button\", 58);\n    i0.ɵɵelement(26, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 60);\n    i0.ɵɵelement(28, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 62);\n    i0.ɵɵelement(30, \"i\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_8_div_31_Template, 13, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 65)(33, \"div\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 67, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵelement(2, \"i\", 90);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 92);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    // Mobile detection\n    this.isMobile = false;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // E-commerce Optimized Carousel Options - Mobile Responsive\n    this.customOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: true,\n      dots: false,\n      nav: false,\n      navSpeed: 500,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      margin: 8,\n      stagePadding: 10,\n      autoplay: false,\n      autoplayHoverPause: true,\n      slideBy: 1,\n      freeDrag: true,\n      responsive: {\n        0: {\n          items: 3,\n          nav: false,\n          margin: 6,\n          stagePadding: 15,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        400: {\n          items: 4,\n          nav: false,\n          margin: 8,\n          stagePadding: 12,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        600: {\n          items: 5,\n          nav: false,\n          margin: 10,\n          stagePadding: 10\n        },\n        768: {\n          items: 6,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        },\n        940: {\n          items: 7,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        }\n      }\n    };\n    // Web Layout Carousel Options\n    this.carouselOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: true,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false,\n          margin: 8\n        },\n        600: {\n          items: 4,\n          nav: true,\n          margin: 10\n        },\n        768: {\n          items: 5,\n          nav: true,\n          margin: 12\n        }\n      }\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: 'user1',\n        username: 'zara',\n        fullName: 'Zara Official',\n        avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'New Summer Collection 🌞',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod1',\n        name: 'Summer Dress',\n        price: 89.99,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '2',\n      user: {\n        _id: 'user2',\n        username: 'nike',\n        fullName: 'Nike',\n        avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Just Do It ✨',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 2340,\n      hasNewProducts: false,\n      products: [{\n        _id: 'prod2',\n        name: 'Air Max Sneakers',\n        price: 129.99,\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '3',\n      user: {\n        _id: 'user3',\n        username: 'adidas',\n        fullName: 'Adidas',\n        avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Impossible is Nothing 🔥',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 1890,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod3',\n        name: 'Ultraboost Shoes',\n        price: 159.99,\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '4',\n      user: {\n        _id: 'user4',\n        username: 'hm',\n        fullName: 'H&M',\n        avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Fashion for Everyone 💫',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 3420,\n      isActive: true,\n      isViewed: false\n    }];\n    this.isLoadingStories = false;\n  }\n  // Removed fallback stories - only use database data\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  // Mobile detection method\n  checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n  onResize() {\n    this.checkScreenSize();\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument)(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        stories: \"stories\",\n        showAddStory: \"showAddStory\",\n        currentUser: \"currentUser\"\n      },\n      outputs: {\n        storyClick: \"storyClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"stories-subtitle\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-slide\", \"add-story-slide\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", \"brand-story-slide\", 3, \"click\"], [1, \"story-avatar\"], [\"class\", \"brand-badge\", 4, \"ngIf\"], [\"class\", \"new-product-badge\", 4, \"ngIf\"], [1, \"story-ring\"], [1, \"brand-badge\"], [1, \"fas\", \"fa-crown\"], [1, \"new-product-badge\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h3\", 4);\n          i0.ɵɵtext(3, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 5);\n          i0.ɵɵtext(5, \"Watch stories from people you follow\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_6_Template, 2, 2, \"div\", 6)(7, ViewAddStoriesComponent_div_7_Template, 14, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_8_Template, 36, 17, \"div\", 8)(9, ViewAddStoriesComponent_div_9_Template, 9, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n  width: 100%;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 12px 8px;\\n    border: none;\\n    border-radius: 0;\\n    border-bottom: 1px solid #efefef;\\n    background: #ffffff;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    border: 1px solid #dbdbdb;\\n    border-radius: 8px;\\n    padding: 24px;\\n    margin-bottom: 24px;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n    position: relative;\\n    z-index: 10;\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    min-height: 150px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n@media (min-width: 769px) {\\n  .stories-header[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #efefef;\\n    padding-bottom: 16px;\\n    margin-bottom: 24px;\\n  }\\n}\\n\\n.stories-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 4px 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    font-weight: 700;\\n  }\\n}\\n\\n.stories-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  margin: 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-subtitle[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n}\\n\\n.web-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  min-height: 300px;\\n}\\n@media (min-width: 769px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.post-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: 2px solid #000;\\n  border-radius: 8px;\\n  padding: 20px;\\n  background: #fff;\\n}\\n.post-section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #262626;\\n  text-align: center;\\n}\\n.post-section[_ngcontent-%COMP%]   .stories-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 20px;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: 2px solid #000;\\n  border-radius: 8px;\\n  padding: 20px;\\n  background: #fff;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #262626;\\n  text-align: center;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n}\\n\\n@media (min-width: 769px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 0;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 16px;\\n  }\\n}\\n\\n.add-story-static[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 82px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n    -webkit-overflow-scrolling: touch;\\n    touch-action: pan-x;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 98px);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%]::after {\\n    content: \\\"\\\";\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    width: 20px;\\n    height: 100%;\\n    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);\\n    pointer-events: none;\\n    z-index: 5;\\n    opacity: 0;\\n    transition: opacity 0.3s ease;\\n  }\\n  .stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]::after {\\n    opacity: 1;\\n  }\\n}\\n\\n.stories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow: visible; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%] {\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n    overflow: hidden;\\n    touch-action: pan-x;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n    touch-action: pan-x;\\n    will-change: transform;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  min-height: 120px;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n    min-height: 90px;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5); \\n\\n  color: #fff; \\n\\n  border: none;\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  pointer-events: all;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas {\\n  font-size: 14px;\\n  color: #fff;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -20px; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -20px; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n    display: none;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer {\\n  position: relative;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_autoSlideIndicator 4s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_autoSlideIndicator {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 66px;\\n}\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  animation-play-state: paused;\\n}\\n\\n.slider-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.slider-nav-btn.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n}\\n\\n.slider-nav-left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n\\n.slider-nav-right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n\\n.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  width: 82px;\\n  min-width: 82px;\\n  position: relative;\\n}\\n@media (min-width: 769px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    min-width: 90px;\\n    padding: 8px;\\n    border-radius: 12px;\\n  }\\n  .story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n    background: rgba(0, 0, 0, 0.05);\\n    transform: scale(1.08);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 76px;\\n    min-width: 76px;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%] {\\n  animation-duration: 1s;\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n}\\n.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #405de6;\\n  font-weight: 600;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-container[_ngcontent-%COMP%] {\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 2;\\n  transition: all 0.3s ease;\\n}\\n@media (min-width: 769px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 74px;\\n    height: 74px;\\n    border: 3px solid #fff;\\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  }\\n  .story-avatar[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.05);\\n    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    border: 1.5px solid #fff;\\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n@media (min-width: 769px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    top: -3px;\\n    left: -3px;\\n    width: 80px;\\n    height: 80px;\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n    box-shadow: 0 0 20px rgba(240, 148, 51, 0.3);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n}\\n.story-ring.viewed[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  animation: none;\\n}\\n.story-ring.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n  box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  margin-top: 8px;\\n}\\n@media (min-width: 769px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    font-weight: 500;\\n    max-width: 90px;\\n    margin-top: 10px;\\n    color: #262626;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n    font-weight: 500;\\n    margin-top: 6px;\\n  }\\n}\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  position: relative;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 3;\\n}\\n.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n  font-weight: bold;\\n}\\n\\n.current-user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 12px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 82px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    min-width: 70px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 0 8px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    min-width: 60px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n  .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .current-user-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n.middle-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 4;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .middle-navigation[_ngcontent-%COMP%] {\\n    bottom: 80px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 12px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n.brand-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: linear-gradient(135deg, #ffd700, #ffb300);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 10;\\n}\\n.brand-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: white;\\n}\\n@media (min-width: 769px) {\\n  .brand-badge[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .brand-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n.new-product-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -4px;\\n  left: -4px;\\n  background: linear-gradient(135deg, #ff4757, #ff3742);\\n  color: white;\\n  font-size: 8px;\\n  font-weight: 700;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  border: 1px solid white;\\n  z-index: 10;\\n}\\n.new-product-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n  letter-spacing: 0.5px;\\n}\\n@media (min-width: 769px) {\\n  .new-product-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 3px 7px;\\n  }\\n  .new-product-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n\\n.story-ring.brand-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ffd700, #ffb300, #ff6b6b, #4ecdc4);\\n  background-size: 300% 300%;\\n  animation: _ngcontent-%COMP%_brandGradient 3s ease infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_brandGradient {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n@media (min-width: 769px) {\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 74px;\\n    height: 74px;\\n    border: 3px solid #dbdbdb;\\n  }\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .current-user-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-color: #f5f5f5;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 24px;\\n  height: 24px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 10;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n@media (min-width: 769px) {\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.brand-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.brand-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_6_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵtext", "ɵɵlistener", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "i_r4", "ɵɵnextContext", "index", "ctx_r1", "ɵɵresetView", "openStories", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template", "ɵɵstyleProp", "story_r5", "user", "avatar", "isBrand", "hasNewProducts", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "username", "ɵɵelementContainerStart", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template", "ViewAddStoriesComponent_div_7_Template_div_click_2_listener", "_r1", "openAddStoryModal", "ViewAddStoriesComponent_div_7_Template_owl_carousel_o_initialized_12_listener", "$event", "onInitialized", "ViewAddStoriesComponent_div_7_Template_owl_carousel_o_changed_12_listener", "onSlideChanged", "ViewAddStoriesComponent_div_7_ng_container_13_Template", "getCurrentUserAvatar", "customOptions", "stories", "i_r7", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "ViewAddStoriesComponent_div_8_div_21_div_1_Template_div_click_0_listener", "product_r9", "_r8", "$implicit", "viewProduct", "_id", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_8_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_8_div_22_Template_button_click_1_listener", "_r10", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_1_listener", "_r11", "buyNow", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_5_listener", "addToWishlist", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_9_listener", "addToCart", "ViewAddStoriesComponent_div_8_div_4_Template", "ViewAddStoriesComponent_div_8_Template_div_click_5_listener", "_r6", "onStoryClick", "ViewAddStoriesComponent_div_8_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_8_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_8_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_8_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_8_video_18_Template", "ViewAddStoriesComponent_div_8_div_19_Template", "ViewAddStoriesComponent_div_8_div_20_Template", "ViewAddStoriesComponent_div_8_div_21_Template", "ViewAddStoriesComponent_div_8_div_22_Template", "ViewAddStoriesComponent_div_8_div_31_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "isMobile", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "nav", "navSpeed", "navText", "margin", "stagePadding", "autoplay", "autoplayHoverPause", "slideBy", "freeDrag", "responsive", "items", "carouselOptions", "subscriptions", "ngOnInit", "checkScreenSize", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "isVerified", "Date", "now", "toISOString", "expiresAt", "products", "image", "isActive", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "num", "undefined", "toFixed", "toString", "showStory", "document", "body", "style", "overflow", "emit", "story", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "touches", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "toLocaleString", "viewProductDetails", "product", "console", "log", "navigate", "queryParams", "productId", "source", "trackProductClick", "viewCategory", "categoryId", "action", "subscribe", "next", "response", "success", "alert", "error", "startPosition", "updateSlideAnalytics", "currentStory", "toggleAutoPlay", "userAgent", "navigator", "isMobileUserAgent", "test", "onResize", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "CartService", "i4", "WishlistService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_div_6_Template", "ViewAddStoriesComponent_div_7_Template", "ViewAddStoriesComponent_div_8_Template", "ViewAddStoriesComponent_div_9_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mponent, OnInit, On<PERSON>estroy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\nimport { environment } from '../../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isBrand?: boolean; // E-commerce: Brand account indicator\n    isVerified?: boolean; // E-commerce: Verified account\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  hasNewProducts?: boolean; // E-commerce: Has new products\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n\n  // Mobile detection\n  isMobile = false;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // E-commerce Optimized Carousel Options - Mobile Responsive\n  customOptions: OwlOptions = {\n    loop: false, // Don't loop for better UX with Add Story first\n    mouseDrag: true,\n    touchDrag: true, // Essential for mobile\n    pullDrag: true, // Allow pull drag on mobile\n    dots: false,\n    nav: false, // Hide nav on mobile, show on desktop\n    navSpeed: 500,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    margin: 8, // Default margin\n    stagePadding: 10, // Add padding for better mobile UX\n    autoplay: false,\n    autoplayHoverPause: true,\n    slideBy: 1, // Slide one item at a time\n    freeDrag: true, // Allow free dragging on mobile\n    responsive: {\n      0: {\n        items: 3, // 3 stories visible on small mobile\n        nav: false,\n        margin: 6,\n        stagePadding: 15,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      400: {\n        items: 4, // 4 stories on larger mobile\n        nav: false,\n        margin: 8,\n        stagePadding: 12,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      600: {\n        items: 5, // 5 stories on tablet\n        nav: false,\n        margin: 10,\n        stagePadding: 10\n      },\n      768: {\n        items: 6, // 6 stories on large tablet\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      },\n      940: {\n        items: 7, // 7 stories on desktop\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      }\n    }\n  };\n\n  // Web Layout Carousel Options\n  carouselOptions: OwlOptions = {\n    loop: false,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: true,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    responsive: {\n      0: {\n        items: 3,\n        nav: false,\n        margin: 8\n      },\n      600: {\n        items: 4,\n        nav: true,\n        margin: 10\n      },\n      768: {\n        items: 5,\n        nav: true,\n        margin: 12\n      }\n    }\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [\n      {\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(), // 22 hours from now\n        views: 1250,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod1',\n            name: 'Summer Dress',\n            price: 89.99,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(), // 20 hours from now\n        views: 2340,\n        hasNewProducts: false,\n        products: [\n          {\n            _id: 'prod2',\n            name: 'Air Max Sneakers',\n            price: 129.99,\n            image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now\n        views: 1890,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod3',\n            name: 'Ultraboost Shoes',\n            price: 159.99,\n            image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(), // 16 hours from now\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }\n    ];\n\n    this.isLoadingStories = false;\n  }\n\n  // Removed fallback stories - only use database data\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n\n  // Mobile detection method\n  private checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize() {\n    this.checkScreenSize();\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Web Stories Header (Desktop/Tablet Only) -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <p class=\"stories-subtitle\">Watch stories from people you follow</p>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- E-commerce Stories Section - Static Add Story + Slider -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Static Add Story (Outside Slider) -->\n    <div class=\"add-story-static\">\n      <div class=\"story-slide add-story-slide\" (click)=\"openAddStoryModal()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"add-story-avatar\">\n            <div class=\"add-story-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"current-user-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\"></div>\n          </div>\n        </div>\n        <div class=\"story-username\">Add Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider (Only Brand/User Stories) -->\n    <div class=\"stories-slider-wrapper\">\n      <div class=\"stories-slider-container\">\n        <owl-carousel-o\n          [options]=\"customOptions\"\n          (initialized)=\"onInitialized($event)\"\n          (changed)=\"onSlideChanged($event)\">\n\n          <!-- Brand/User Stories Only -->\n          <ng-container *ngFor=\"let story of stories; let i = index\">\n            <ng-template carouselSlide>\n              <div class=\"story-slide brand-story-slide\" (click)=\"openStories(i)\">\n                <div class=\"story-avatar-container\">\n                  <div class=\"story-avatar\"\n                       [style.background-image]=\"'url(' + story.user.avatar + ')'\">\n                    <!-- E-commerce Badge for Brand Stories -->\n                    <div class=\"brand-badge\" *ngIf=\"story.user.isBrand\">\n                      <i class=\"fas fa-crown\"></i>\n                    </div>\n                    <!-- New Product Badge -->\n                    <div class=\"new-product-badge\" *ngIf=\"story.hasNewProducts\">\n                      <span>NEW</span>\n                    </div>\n                  </div>\n                  <div class=\"story-ring\"\n                       [class.viewed]=\"story.isViewed\"\n                       [class.brand-ring]=\"story.user.isBrand\">\n                  </div>\n                </div>\n                <div class=\"story-username\">{{ story.user.username }}</div>\n              </div>\n            </ng-template>\n          </ng-container>\n        </owl-carousel-o>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product._id)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Middle Point Navigation Button -->\n        <div class=\"middle-navigation\" *ngIf=\"hasProducts()\">\n          <button class=\"middle-nav-btn\" (click)=\"viewProduct(getStoryProducts()[0]._id)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>Shop Now</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\" *ngIf=\"hasProducts()\">\n          <button class=\"ecommerce-btn buy-now-btn\" (click)=\"buyNow()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\" (click)=\"addToCart()\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2FA,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICG3DC,EAAA,CAAAC,cAAA,cAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,kBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;IAuCpBT,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGJH,EADF,CAAAC,cAAA,cAA4D,WACpD;IAAAD,EAAA,CAAAU,MAAA,UAAG;IACXV,EADW,CAAAG,YAAA,EAAO,EACZ;;;;;;IAXZH,EAAA,CAAAC,cAAA,cAAoE;IAAzBD,EAAA,CAAAW,UAAA,mBAAAC,0FAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAE/Df,EADF,CAAAC,cAAA,cAAoC,cAE+B;IAM/DD,EAJA,CAAAI,UAAA,IAAAiB,0EAAA,kBAAoD,IAAAC,0EAAA,kBAIQ;IAG9DtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAGM;IACRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IACvDV,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAhBGH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAuB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAEpC1B,EAAA,CAAAM,SAAA,EAAwB;IAAxBN,EAAA,CAAAO,UAAA,SAAAiB,QAAA,CAAAC,IAAA,CAAAE,OAAA,CAAwB;IAIlB3B,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAO,UAAA,SAAAiB,QAAA,CAAAI,cAAA,CAA0B;IAKvD5B,EAAA,CAAAM,SAAA,EAA+B;IAC/BN,EADA,CAAA6B,WAAA,WAAAL,QAAA,CAAAM,QAAA,CAA+B,eAAAN,QAAA,CAAAC,IAAA,CAAAE,OAAA,CACQ;IAGlB3B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA+B,iBAAA,CAAAP,QAAA,CAAAC,IAAA,CAAAO,QAAA,CAAyB;;;;;IApB3DhC,EAAA,CAAAiC,uBAAA,GAA2D;IACzDjC,EAAA,CAAAI,UAAA,IAAA8B,oEAAA,0BAA2B;;;;;;;IAvBjClC,EAHJ,CAAAC,cAAA,cAAuD,cAEvB,cAC2C;IAA9BD,EAAA,CAAAW,UAAA,mBAAAwB,4DAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmB,iBAAA,EAAmB;IAAA,EAAC;IAGhErC,EAFJ,CAAAC,cAAA,cAAoC,cACJ,cACA;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAwG;IAE5GF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,gBAAS;IAEzCV,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAoC,eACI,0BAIC;IAAnCD,EADA,CAAAW,UAAA,yBAAA2B,8EAAAC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAAeD,MAAA,CAAAsB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,qBAAAE,0EAAAF,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAC1BD,MAAA,CAAAwB,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAGlCvC,EAAA,CAAAI,UAAA,KAAAuC,sDAAA,2BAA2D;IA2BnE3C,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACF;;;;IA3CqCH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA0B,oBAAA,SAAgE;IAWnG5C,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA2B,aAAA,CAAyB;IAKO7C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA4B,OAAA,CAAY;;;;;IAoChD9C,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAA6B,WAAA,WAAAkB,IAAA,KAAA7B,MAAA,CAAA8B,YAAA,CAAmC,cAAAD,IAAA,GAAA7B,MAAA,CAAA8B,YAAA,CACC;;;;;IA6BpChD,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAW,MAAA,CAAA+B,eAAA,GAAAC,QAAA,EAAAlD,EAAA,CAAAmD,aAAA,CAAkC;;;;;IAQpCnD,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA+B,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItElD,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAoD,kBAAA,MAAAlC,MAAA,CAAA+B,eAAA,GAAAI,OAAA,MACF;;;;;;IAIErD,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAW,UAAA,mBAAA2C,yEAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAa,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAwC,WAAA,CAAAH,UAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IAClC3D,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAU,MAAA,yBAAG;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IAEnEV,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAA+B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACjB5D,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA2C,WAAA,CAAAN,UAAA,CAAAO,KAAA,EAAgC;;;;;IARrE9D,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAI,UAAA,IAAA2D,mDAAA,kBAGqC;IAOvC/D,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA8C,gBAAA,GAAqB;;;;;;IAa3ChE,EADF,CAAAC,cAAA,cAAqD,iBAC6B;IAAjDD,EAAA,CAAAW,UAAA,mBAAAsD,sEAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAqD,IAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAwC,WAAA,CAAYxC,MAAA,CAAA8C,gBAAA,EAAkB,CAAC,CAAC,EAAAL,GAAA,CAAM;IAAA,EAAC;IAC7E3D,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAElBV,EAFkB,CAAAG,YAAA,EAAO,EACd,EACL;;;;;;IAmBJH,EADF,CAAAC,cAAA,cAA4D,iBACG;IAAnBD,EAAA,CAAAW,UAAA,mBAAAwD,sEAAA;MAAAnE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmD,MAAA,EAAQ;IAAA,EAAC;IAC1DrE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,cAAO;IACfV,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA1BD,EAAA,CAAAW,UAAA,mBAAA2D,sEAAA;MAAAtE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAqD,aAAA,EAAe;IAAA,EAAC;IAClEvE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAChBV,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,iBAA6D;IAAtBD,EAAA,CAAAW,UAAA,mBAAA6D,sEAAA;MAAAxE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAuD,SAAA,EAAW;IAAA,EAAC;IAC1DzE,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAErBV,EAFqB,CAAAG,YAAA,EAAO,EACjB,EACL;;;;;;IA3GVH,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAsE,4CAAA,kBAIuC;IAGzC1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAW,UAAA,mBAAAgE,4DAAApC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA2D,YAAA,CAAAtC,MAAA,CAAoB;IAAA,EAAC,wBAAAuC,iEAAAvC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAChBD,MAAA,CAAA6D,YAAA,CAAAxC,MAAA,CAAoB;IAAA,EAAC,uBAAAyC,gEAAAzC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CACtBD,MAAA,CAAA+D,WAAA,CAAA1C,MAAA,CAAmB;IAAA,EAAC,sBAAA2C,+DAAA3C,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CACrBD,MAAA,CAAAiE,UAAA,CAAA5C,MAAA,CAAkB;IAAA,EAAC;IAIhCvC,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAqC;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,IAA6C;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAiD;IAC7EV,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAW,UAAA,mBAAAyE,gEAAA;MAAApF,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmE,YAAA,EAAc;IAAA,EAAC;IACnDrF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAuC1BD,EArCA,CAAAI,UAAA,KAAAkF,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP,KAAAC,6CAAA,kBAcF;IAMvD1F,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAuF,6CAAA,mBAA4D;IAc9D3F,EAAA,CAAAG,YAAA,EAAM;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IA1HuBH,EAAA,CAAA6B,WAAA,YAAAX,MAAA,CAAA0E,MAAA,CAAwB;IAM3B5F,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA4B,OAAA,CAAY;IAU7B9C,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA+B,eAAA,GAAAxB,IAAA,CAAAC,MAAA,OAAuE;IACzE1B,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA+B,eAAA,GAAAxB,IAAA,CAAAoE,QAAA,CAAqC;IACrC7F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA4E,UAAA,CAAA5E,MAAA,CAAA+B,eAAA,GAAA8C,SAAA,EAA6C;IAC5C/F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAoD,kBAAA,KAAAlC,MAAA,CAAA8E,YAAA,CAAA9E,MAAA,CAAA+B,eAAA,GAAAgD,KAAA,YAAiD;IAW1EjG,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAiD,SAAA,aAA6C;IAW7ClG,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAiD,SAAA,aAA6C;IAM1ClG,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAI,OAAA,CAA+B;IAK/BrD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAcOnG,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAuBZnG,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAuB5BnG,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAA6B,WAAA,cAAAX,MAAA,CAAA0E,MAAA,CAA0B;;;;;IAK9D5F,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IACtBV,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;AD5JN,OAAM,MAAOiG,uBAAuB;EAyHlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IA1HzB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAIP,KAAA5D,OAAO,GAAY,EAAE;IACrB,KAAA6D,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAIhH,YAAY,EAAmC;IAE1E,KAAAiH,gBAAgB,GAAG,IAAI;IAEvB,KAAA9D,YAAY,GAAG,CAAC;IAChB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAmB,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA9E,aAAa,GAAe;MAC1B+E,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;;;KAGnB;IAED;IACA,KAAAO,eAAe,GAAe;MAC5BhB,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXE,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFO,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE;;;KAGb;IAEO,KAAAS,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA,IAAI,CAAC,IAAI,CAACjG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkG,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACnC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACoC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACnC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAChE,OAAO,GAAG,CACb;MACEa,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,eAAe;QACzBnE,MAAM,EAAE,6FAA6F;QACrGC,OAAO,EAAE,IAAI;QACb6H,UAAU,EAAE;OACb;MACDtG,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAI0D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1D,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,IAAI;MACpBiI,QAAQ,EAAE,CACR;QACElG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE,KAAK;QACZgG,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdjI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,MAAM;QAChBnE,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACb6H,UAAU,EAAE;OACb;MACDtG,QAAQ,EAAE,gFAAgF;MAC1FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,cAAc;MACvB0C,SAAS,EAAE,IAAI0D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1D,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,KAAK;MACrBiI,QAAQ,EAAE,CACR;QACElG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,kBAAkB;QACxBE,KAAK,EAAE,MAAM;QACbgG,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdjI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,QAAQ;QAClB6D,QAAQ,EAAE,QAAQ;QAClBnE,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACb6H,UAAU,EAAE;OACb;MACDtG,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAI0D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1D,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,IAAI;MACpBiI,QAAQ,EAAE,CACR;QACElG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,kBAAkB;QACxBE,KAAK,EAAE,MAAM;QACbgG,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdjI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,IAAI;QACd6D,QAAQ,EAAE,KAAK;QACfnE,MAAM,EAAE;OACT;MACDwB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,yBAAyB;MAClC0C,SAAS,EAAE,IAAI0D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1D,KAAK,EAAE,IAAI;MACX8D,QAAQ,EAAE,IAAI;MACdjI,QAAQ,EAAE;KACX,CACF;IAED,IAAI,CAACgF,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EAEA7D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAgD,UAAUA,CAACkE,UAAkB;IAC3B,MAAMN,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMQ,IAAI,GAAG,IAAIR,IAAI,CAACO,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACV,GAAG,CAACW,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAvE,YAAYA,CAACwE,GAAW;IACtB,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;MAC7C,OAAO,GAAG;;IAEZ,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOF,GAAG,CAACG,QAAQ,EAAE;EACvB;EAEAvJ,WAAWA,CAACH,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC+B,YAAY,GAAG/B,KAAK;IACzB,IAAI,CAAC2E,MAAM,GAAG,IAAI;IAClB,IAAI,CAACgF,SAAS,CAAC3J,KAAK,CAAC;IACrB4J,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAAClI,OAAO,CAAC7B,KAAK,CAAC,EAAE;MACvB,IAAI,CAAC4F,UAAU,CAACoE,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACpI,OAAO,CAAC7B,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAoE,YAAYA,CAAA;IACV,IAAI,CAACO,MAAM,GAAG,KAAK;IACnB,IAAI,CAACuF,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAb,SAASA,CAAC3J,KAAa;IACrB,IAAI,CAAC+B,YAAY,GAAG/B,KAAK;IACzB,IAAI,CAACgG,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACmE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3I,YAAY,GAAG,IAAI,CAACF,OAAO,CAACkG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC9B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACvG,YAAY,EAAE;;EAEvB;EAEAwG,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7I,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACkE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACvG,YAAY,EAAE;;EAEvB;EAGAyG,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACnG,MAAM,EAAE;IAElB,QAAQmG,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACtG,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACkH,KAAiB;IAC5B,IAAI,IAAI,CAAChF,UAAU,EAAE;IAErB,MAAMkF,IAAI,GAAIF,KAAK,CAACG,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACV,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEA5G,YAAYA,CAACgH,KAAiB;IAC5B,IAAI,CAAC/E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG2E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC1C,IAAI,CAAChF,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEAnC,WAAWA,CAAC8G,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC/E,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAG0E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC5C,MAAMI,YAAY,GAAG,IAAI,CAACpF,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAMsF,WAAW,GAAGvC,IAAI,CAACwC,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAACpF,0BAA0B,EAAE;MACjD,IAAImF,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACZ,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAAC3E,UAAU,GAAG,KAAK;;EAE3B;EAEA7B,UAAUA,CAAC2H,MAAkB;IAC3B,IAAI,CAAC9F,UAAU,GAAG,KAAK;EACzB;EAEQkC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGM4B,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGlC,QAAQ,CAACmC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAAC3D,OAAO,CAAC6D,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQtB,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7E,UAAU,EAAE;IAEtB,MAAMoG,IAAI,GAAG,IAAI,CAACjG,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAIkG,IAAI,GAAG,GAAG;IAE1B,IAAIhD,IAAI,CAACwC,GAAG,CAACQ,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAAClG,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAACnE,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACmE,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAACnE,YAAY,EAAE;;MAGrB,IAAI,CAACkE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAACiE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,6BAA6B,IAAI,CAACzE,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnBqG,qBAAqB,CAAC,MAAM,IAAI,CAACxB,MAAM,EAAE,CAAC;;EAE9C;EAEAzF,WAAWA,CAAA;IACT,MAAM+E,KAAK,GAAG,IAAI,CAACjI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEiI,KAAK,EAAErB,QAAQ,IAAIqB,KAAK,CAACrB,QAAQ,CAACb,MAAM,GAAG,CAAC,CAAC;EACzD;EAEAhF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,eAAe,EAAE,CAAC4G,QAAQ,IAAI,EAAE;EAC9C;EAEAhG,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAEuJ,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACxC;IACA,IAAI,CAACjH,MAAM,CAACoH,QAAQ,CAAC,CAAC,WAAW,EAAEH,OAAO,CAAC5J,GAAG,CAAC,CAAC;EAClD;EAEAf,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAACgE,WAAW,EAAElF,MAAM,IAAI,mCAAmC;EACxE;EAEAW,iBAAiBA,CAAA;IACfmL,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAACnH,MAAM,CAACoH,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEArJ,MAAMA,CAAA;IACJ,MAAMwF,QAAQ,GAAG,IAAI,CAAC7F,gBAAgB,EAAE;IACxC,IAAI6F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B2D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvC;MACA,IAAI,CAACjH,MAAM,CAACoH,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,EAAE;UACXC,SAAS,EAAEL,OAAO,CAAC5J,GAAG;UACtBkK,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACAnK,WAAWA,CAACkK,SAAiB;IAC3B;IACA,IAAI,CAACE,iBAAiB,CAACF,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAACtH,MAAM,CAACoH,QAAQ,CAAC,CAAC,eAAe,EAAEE,SAAS,CAAC,CAAC;EACpD;EAEAG,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAAC1H,MAAM,CAACoH,QAAQ,CAAC,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACF,SAAiB,EAAEK,MAAc;IACzD;IACAT,OAAO,CAACC,GAAG,CAAC,iBAAiBQ,MAAM,WAAW,EAAEL,SAAS,CAAC;IAC1D;EACF;EAEArJ,aAAaA,CAAA;IACX,MAAMsF,QAAQ,GAAG,IAAI,CAAC7F,gBAAgB,EAAE;IACxC,IAAI6F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC;MAC3B2D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;MAE3C,IAAI,CAAC9G,eAAe,CAAClC,aAAa,CAACgJ,OAAO,CAAC5J,GAAG,CAAC,CAACuK,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDD,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEA7J,SAASA,CAAA;IACP,MAAMoF,QAAQ,GAAG,IAAI,CAAC7F,gBAAgB,EAAE;IACxC,IAAI6F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC;MAC3B2D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MAEvC,IAAI,CAAC/G,WAAW,CAAC/B,SAAS,CAAC8I,OAAO,CAAC5J,GAAG,EAAE,CAAC,EAAE8G,SAAS,EAAEA,SAAS,CAAC,CAACyD,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CD,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACA5L,cAAcA,CAACqJ,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACyC,aAAa,KAAK/D,SAAS,EAAE;MAC9C,IAAI,CAAC9C,iBAAiB,GAAGoE,KAAK,CAACyC,aAAa;MAE5C;MACAhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAAC9F,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAAC8G,oBAAoB,EAAE;;EAE/B;EAEAjM,aAAaA,CAACsK,MAAW;IACvB;IACA,IAAI,CAACrF,qBAAqB,GAAG,IAAI;IACjC+F,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQgB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAAC3L,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC6E,iBAAiB,CAAC,EAAE;MACxD,MAAM+G,YAAY,GAAG,IAAI,CAAC5L,OAAO,CAAC,IAAI,CAAC6E,iBAAiB,CAAC;MACzD6F,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,YAAY,CAACjN,IAAI,CAACO,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACA2M,cAAcA,CAAA;IACZ,IAAI,CAACjH,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACA8F,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAC/F,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;EAEA;EACQqB,eAAeA,CAAA;IACrB,MAAMwD,KAAK,GAAGK,MAAM,CAACC,UAAU;IAC/B,MAAM+B,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAAClI,QAAQ,GAAG6F,KAAK,IAAI,GAAG,IAAIuC,iBAAiB;EACnD;EAGAE,QAAQA,CAAA;IACN,IAAI,CAACjG,eAAe,EAAE;EACxB;;;uBApmBW3C,uBAAuB,EAAApG,EAAA,CAAAiP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnP,EAAA,CAAAiP,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAArP,EAAA,CAAAiP,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvP,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBrJ,uBAAuB;MAAAsJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB7P,EAAA,CAAAW,UAAA,qBAAAoP,mDAAAxN,MAAA;YAAA,OAAAuN,GAAA,CAAAhE,aAAA,CAAAvJ,MAAA,CAAqB;UAAA,UAAAvC,EAAA,CAAAgQ,iBAAA,CAAE,oBAAAC,kDAAA1N,MAAA;YAAA,OAAvBuN,GAAA,CAAAd,QAAA,CAAAzM,MAAA,CAAgB;UAAA,UAAAvC,EAAA,CAAAkQ,eAAA,CAAO;;;;;;;;;;;;;;;;;;UC/ChClQ,EAHJ,CAAAC,cAAA,aAA+B,aAED,YACA;UAAAD,EAAA,CAAAU,MAAA,cAAO;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,2CAAoC;UAClEV,EADkE,CAAAG,YAAA,EAAI,EAChE;UAWNH,EARA,CAAAI,UAAA,IAAA+P,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC;UAqDzDpQ,EAAA,CAAAG,YAAA,EAAM;UAgINH,EA7HA,CAAAI,UAAA,IAAAiQ,sCAAA,mBAAqE,IAAAC,sCAAA,iBA6HxB;;;UA7LrCtQ,EAAA,CAAAM,SAAA,GAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAuP,GAAA,CAAAhJ,gBAAA,CAAsB;UAQE9G,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAAuP,GAAA,CAAAhJ,gBAAA,CAAuB;UAwDA9G,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAuP,GAAA,CAAAlK,MAAA,CAAY;UA6HpC5F,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAuP,GAAA,CAAAlK,MAAA,CAAY;;;qBDvJ/B9F,YAAY,EAAAyQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1Q,cAAc,EAAA2Q,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}