import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { ProductService } from '../../../../core/services/product.service';

interface SuggestedUser {
  _id?: string;
  id?: string;
  username: string;
  fullName: string;
  avatar: string;
  followedBy?: string;
  bio?: string;
  isFollowing: boolean;
  isInfluencer?: boolean;
  isVerified?: boolean;
  followerCount?: number;
  followers?: number;
  following?: number;
  posts?: number;
  mutualFollowers?: number;
  category?: string;
}

@Component({
  selector: 'app-suggested-for-you',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './suggested-for-you.component.html',
  styleUrls: ['./suggested-for-you.component.scss']
})
export class SuggestedForYouComponent implements OnInit, OnDestroy {
  suggestedUsers: SuggestedUser[] = [];
  isLoading = true;
  error: string | null = null;
  private subscription: Subscription = new Subscription();

  // Slider properties
  currentSlide = 0;
  slideOffset = 0;
  cardWidth = 200; // Width of each user card including margin
  visibleCards = 4; // Number of cards visible at once
  maxSlide = 0;
  
  // Auto-sliding properties
  autoSlideInterval: any;
  autoSlideDelay = 5000; // 5 seconds for users
  isAutoSliding = true;
  isPaused = false;

  // Section interaction properties
  isSectionLiked = false;
  isSectionBookmarked = false;
  sectionLikes = 198;
  sectionComments = 67;
  isMobile = false;

  constructor(
    private router: Router,
    private productService: ProductService
  ) {}

  ngOnInit() {
    console.log('🔵 SuggestedForYouComponent: ngOnInit called');
    try {
      this.loadSuggestedUsers();
      this.updateResponsiveSettings();
      this.setupResizeListener();
      this.checkMobileDevice();
      console.log('🔵 SuggestedForYouComponent: Initialization complete');
    } catch (error) {
      console.error('🔵 SuggestedForYouComponent: Error initializing:', error);
      this.isLoading = false;
      this.error = 'Failed to initialize component';
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.stopAutoSlide();
  }

  private async loadSuggestedUsers() {
    try {
      this.isLoading = true;
      this.error = null;
      
      // Load real data from API
      this.subscription.add(
        this.productService.getSuggestedUsers().subscribe({
          next: (response) => {
            if (response.success && response.data) {
              this.suggestedUsers = response.data.map((user: any) => ({
                _id: user.id || user._id,
                id: user.id || user._id,
                username: user.username,
                fullName: user.fullName,
                avatar: user.avatar || '/assets/images/default-avatar.svg',
                followedBy: user.followedBy || '',
                bio: user.bio || '',
                isFollowing: user.isFollowing || false,
                isInfluencer: user.isInfluencer || false,
                isVerified: user.isVerified || false,
                followerCount: user.followerCount || user.followers || 0,
                followers: user.followers || user.followerCount || 0,
                following: user.following || 0,
                posts: user.posts || 0,
                mutualFollowers: user.mutualFollowers || 0,
                category: user.category || 'Fashion'
              }));
            } else {
              this.suggestedUsers = [];
            }
            this.isLoading = false;
            this.updateSliderOnUsersLoad();
          },
          error: (error) => {
            console.error('Error loading suggested users:', error);
            this.error = 'Failed to load suggested users';
            this.isLoading = false;
            this.suggestedUsers = [];
          }
        })
      );
    } catch (error) {
      console.error('Error loading suggested users:', error);
      this.error = 'Failed to load suggested users';
      this.isLoading = false;
    }
  }

  onUserClick(user: SuggestedUser) {
    this.router.navigate(['/profile', user.username]);
  }

  onFollowUser(user: SuggestedUser, event: Event) {
    event.stopPropagation();
    user.isFollowing = !user.isFollowing;

    if (user.isFollowing) {
      user.followerCount = (user.followerCount || 0) + 1;
    } else {
      user.followerCount = Math.max((user.followerCount || 0) - 1, 0);
    }
  }

  formatFollowerCount(count: number | undefined): string {
    if (!count || count === 0) {
      return '0';
    }
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  }

  onRetry() {
    this.loadSuggestedUsers();
  }

  trackByUserId(index: number, user: SuggestedUser): string {
    return user.id || user._id || index.toString();
  }

  // Auto-sliding methods
  private startAutoSlide() {
    if (!this.isAutoSliding || this.isPaused) return;
    
    this.stopAutoSlide();
    this.autoSlideInterval = setInterval(() => {
      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {
        this.autoSlideNext();
      }
    }, this.autoSlideDelay);
  }

  private stopAutoSlide() {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
      this.autoSlideInterval = null;
    }
  }

  private autoSlideNext() {
    if (this.currentSlide >= this.maxSlide) {
      this.currentSlide = 0;
    } else {
      this.currentSlide++;
    }
    this.updateSlideOffset();
  }

  pauseAutoSlide() {
    this.isPaused = true;
    this.stopAutoSlide();
  }

  resumeAutoSlide() {
    this.isPaused = false;
    this.startAutoSlide();
  }

  // Responsive methods
  private updateResponsiveSettings() {
    const width = window.innerWidth;
    if (width <= 480) {
      this.cardWidth = 180;
      this.visibleCards = 1;
    } else if (width <= 768) {
      this.cardWidth = 200;
      this.visibleCards = 2;
    } else if (width <= 1200) {
      this.cardWidth = 220;
      this.visibleCards = 3;
    } else {
      this.cardWidth = 220;
      this.visibleCards = 4;
    }
    this.updateSliderLimits();
    this.updateSlideOffset();
  }

  private setupResizeListener() {
    window.addEventListener('resize', () => {
      this.updateResponsiveSettings();
    });
  }

  // Slider methods
  updateSliderLimits() {
    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);
  }

  slidePrev() {
    if (this.currentSlide > 0) {
      this.currentSlide--;
      this.updateSlideOffset();
      this.restartAutoSlideAfterInteraction();
    }
  }

  slideNext() {
    if (this.currentSlide < this.maxSlide) {
      this.currentSlide++;
      this.updateSlideOffset();
      this.restartAutoSlideAfterInteraction();
    }
  }

  private updateSlideOffset() {
    this.slideOffset = -this.currentSlide * this.cardWidth;
  }

  private restartAutoSlideAfterInteraction() {
    this.stopAutoSlide();
    setTimeout(() => {
      this.startAutoSlide();
    }, 2000);
  }

  // Update slider when users load
  private updateSliderOnUsersLoad() {
    setTimeout(() => {
      this.updateSliderLimits();
      this.currentSlide = 0;
      this.slideOffset = 0;
      this.startAutoSlide();
    }, 100);
  }

  // Section interaction methods
  toggleSectionLike() {
    this.isSectionLiked = !this.isSectionLiked;
    if (this.isSectionLiked) {
      this.sectionLikes++;
    } else {
      this.sectionLikes--;
    }
  }

  toggleSectionBookmark() {
    this.isSectionBookmarked = !this.isSectionBookmarked;
  }

  openComments() {
    console.log('Opening comments for suggested users section');
  }

  shareSection() {
    if (navigator.share) {
      navigator.share({
        title: 'Suggested for You',
        text: 'Discover amazing fashion creators!',
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      console.log('Link copied to clipboard');
    }
  }

  openMusicPlayer() {
    console.log('Opening music player for suggested users');
  }

  formatCount(count: number): string {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  }

  private checkMobileDevice() {
    this.isMobile = window.innerWidth <= 768;
  }


}
