{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nlet ViewAddStoriesComponent = class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    // Mobile detection\n    this.isMobile = false;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // E-commerce Optimized Carousel Options - Mobile Responsive\n    this.customOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: true,\n      dots: false,\n      nav: false,\n      navSpeed: 500,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      margin: 8,\n      stagePadding: 10,\n      autoplay: false,\n      autoplayHoverPause: true,\n      slideBy: 1,\n      freeDrag: true,\n      responsive: {\n        0: {\n          items: 3,\n          nav: false,\n          margin: 6,\n          stagePadding: 15,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        400: {\n          items: 4,\n          nav: false,\n          margin: 8,\n          stagePadding: 12,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        600: {\n          items: 5,\n          nav: false,\n          margin: 10,\n          stagePadding: 10\n        },\n        768: {\n          items: 6,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        },\n        940: {\n          items: 7,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        }\n      }\n    };\n    // Web Layout Carousel Options\n    this.carouselOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: true,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false,\n          margin: 8\n        },\n        600: {\n          items: 4,\n          nav: true,\n          margin: 10\n        },\n        768: {\n          items: 5,\n          nav: true,\n          margin: 12\n        }\n      }\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: 'user1',\n        username: 'zara',\n        fullName: 'Zara Official',\n        avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'New Summer Collection 🌞',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod1',\n        name: 'Summer Dress',\n        price: 89.99,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '2',\n      user: {\n        _id: 'user2',\n        username: 'nike',\n        fullName: 'Nike',\n        avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Just Do It ✨',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 2340,\n      hasNewProducts: false,\n      products: [{\n        _id: 'prod2',\n        name: 'Air Max Sneakers',\n        price: 129.99,\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '3',\n      user: {\n        _id: 'user3',\n        username: 'adidas',\n        fullName: 'Adidas',\n        avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Impossible is Nothing 🔥',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 1890,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod3',\n        name: 'Ultraboost Shoes',\n        price: 159.99,\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '4',\n      user: {\n        _id: 'user4',\n        username: 'hm',\n        fullName: 'H&M',\n        avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Fashion for Everyone 💫',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 3420,\n      isActive: true,\n      isViewed: false\n    }];\n    this.isLoadingStories = false;\n  }\n  // Removed fallback stories - only use database data\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  // Mobile detection method\n  checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n  onResize() {\n    this.checkScreenSize();\n  }\n};\n__decorate([ViewChild('storiesContainer', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesContainer\", void 0);\n__decorate([ViewChild('feedCover', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"feedCover\", void 0);\n__decorate([ViewChild('storiesSlider', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesSlider\", void 0);\n__decorate([Input()], ViewAddStoriesComponent.prototype, \"stories\", void 0);\n__decorate([Input()], ViewAddStoriesComponent.prototype, \"showAddStory\", void 0);\n__decorate([Input()], ViewAddStoriesComponent.prototype, \"currentUser\", void 0);\n__decorate([Output()], ViewAddStoriesComponent.prototype, \"storyClick\", void 0);\n__decorate([HostListener('document:keydown', ['$event'])], ViewAddStoriesComponent.prototype, \"handleKeydown\", null);\n__decorate([HostListener('window:resize', ['$event'])], ViewAddStoriesComponent.prototype, \"onResize\", null);\nViewAddStoriesComponent = __decorate([Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})], ViewAddStoriesComponent);\nexport { ViewAddStoriesComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "HostListener", "Input", "Output", "EventEmitter", "CommonModule", "CarouselModule", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "isMobile", "stories", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "customOptions", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "nav", "navSpeed", "navText", "margin", "stagePadding", "autoplay", "autoplayHoverPause", "slideBy", "freeDrag", "responsive", "items", "carouselOptions", "subscriptions", "ngOnInit", "checkScreenSize", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "_id", "user", "username", "fullName", "avatar", "isBrand", "isVerified", "mediaUrl", "mediaType", "caption", "createdAt", "Date", "now", "toISOString", "expiresAt", "views", "hasNewProducts", "products", "name", "price", "image", "isActive", "<PERSON><PERSON><PERSON><PERSON>", "getCurrentStory", "getTimeAgo", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "undefined", "toFixed", "toString", "openStories", "index", "showStory", "document", "body", "style", "overflow", "emit", "story", "closeStories", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "onTouchStart", "touches", "onTouchMove", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "onTouchEnd", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "hasProducts", "getStoryProducts", "formatPrice", "toLocaleString", "viewProductDetails", "product", "console", "log", "navigate", "getCurrentUserAvatar", "openAddStoryModal", "buyNow", "queryParams", "productId", "source", "viewProduct", "trackProductClick", "viewCategory", "categoryId", "action", "addToWishlist", "subscribe", "next", "response", "success", "alert", "error", "addToCart", "onSlideChanged", "startPosition", "updateSlideAnalytics", "onInitialized", "currentStory", "toggleAutoPlay", "userAgent", "navigator", "isMobileUserAgent", "test", "onResize", "__decorate", "static", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts"], "sourcesContent": ["import { <PERSON>mponent, OnInit, On<PERSON>estroy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\nimport { environment } from '../../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isBrand?: boolean; // E-commerce: Brand account indicator\n    isVerified?: boolean; // E-commerce: Verified account\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  hasNewProducts?: boolean; // E-commerce: Has new products\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n\n  // Mobile detection\n  isMobile = false;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // E-commerce Optimized Carousel Options - Mobile Responsive\n  customOptions: OwlOptions = {\n    loop: false, // Don't loop for better UX with Add Story first\n    mouseDrag: true,\n    touchDrag: true, // Essential for mobile\n    pullDrag: true, // Allow pull drag on mobile\n    dots: false,\n    nav: false, // Hide nav on mobile, show on desktop\n    navSpeed: 500,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    margin: 8, // Default margin\n    stagePadding: 10, // Add padding for better mobile UX\n    autoplay: false,\n    autoplayHoverPause: true,\n    slideBy: 1, // Slide one item at a time\n    freeDrag: true, // Allow free dragging on mobile\n    responsive: {\n      0: {\n        items: 3, // 3 stories visible on small mobile\n        nav: false,\n        margin: 6,\n        stagePadding: 15,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      400: {\n        items: 4, // 4 stories on larger mobile\n        nav: false,\n        margin: 8,\n        stagePadding: 12,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      600: {\n        items: 5, // 5 stories on tablet\n        nav: false,\n        margin: 10,\n        stagePadding: 10\n      },\n      768: {\n        items: 6, // 6 stories on large tablet\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      },\n      940: {\n        items: 7, // 7 stories on desktop\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      }\n    }\n  };\n\n  // Web Layout Carousel Options\n  carouselOptions: OwlOptions = {\n    loop: false,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: true,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    responsive: {\n      0: {\n        items: 3,\n        nav: false,\n        margin: 8\n      },\n      600: {\n        items: 4,\n        nav: true,\n        margin: 10\n      },\n      768: {\n        items: 5,\n        nav: true,\n        margin: 12\n      }\n    }\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [\n      {\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(), // 22 hours from now\n        views: 1250,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod1',\n            name: 'Summer Dress',\n            price: 89.99,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(), // 20 hours from now\n        views: 2340,\n        hasNewProducts: false,\n        products: [\n          {\n            _id: 'prod2',\n            name: 'Air Max Sneakers',\n            price: 129.99,\n            image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now\n        views: 1890,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod3',\n            name: 'Ultraboost Shoes',\n            price: 159.99,\n            image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(), // 16 hours from now\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }\n    ];\n\n    this.isLoadingStories = false;\n  }\n\n  // Removed fallback stories - only use database data\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n\n  // Mobile detection method\n  private checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize() {\n    this.checkScreenSize();\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAiCC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;AA4CxD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAyHlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IA1HzB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAIP,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAIb,YAAY,EAAmC;IAE1E,KAAAc,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAAC,aAAa,GAAe;MAC1BC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;;;KAGnB;IAED;IACA,KAAAO,eAAe,GAAe;MAC5BhB,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXE,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFO,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE;;;KAGb;IAEO,KAAAS,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA,IAAI,CAAC,IAAI,CAACxC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACtC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACuC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACtC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACJ,OAAO,GAAG,CACb;MACEiD,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE;QACJD,GAAG,EAAE,OAAO;QACZE,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,eAAe;QACzBC,MAAM,EAAE,6FAA6F;QACrGC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;OACb;MACDC,QAAQ,EAAE,mFAAmF;MAC7FC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,0BAA0B;MACnCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,CACR;QACEjB,GAAG,EAAE,OAAO;QACZkB,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEtB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE;QACJD,GAAG,EAAE,OAAO;QACZE,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;OACb;MACDC,QAAQ,EAAE,gFAAgF;MAC1FC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,cAAc;MACvBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE,CACR;QACEjB,GAAG,EAAE,OAAO;QACZkB,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEtB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE;QACJD,GAAG,EAAE,OAAO;QACZE,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;OACb;MACDC,QAAQ,EAAE,mFAAmF;MAC7FC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,0BAA0B;MACnCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,CACR;QACEjB,GAAG,EAAE,OAAO;QACZkB,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEtB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE;QACJD,GAAG,EAAE,OAAO;QACZE,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE;OACT;MACDG,QAAQ,EAAE,mFAAmF;MAC7FC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,yBAAyB;MAClCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXM,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CACF;IAED,IAAI,CAACnE,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EAEAoE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACxE,OAAO,CAAC,IAAI,CAACK,YAAY,CAAC,IAAI,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAyE,UAAUA,CAACC,UAAkB;IAC3B,MAAMb,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMe,IAAI,GAAG,IAAIf,IAAI,CAACc,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACjB,GAAG,CAACkB,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAC,YAAYA,CAACC,GAAW;IACtB,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;MAC7C,OAAO,GAAG;;IAEZ,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOF,GAAG,CAACG,QAAQ,EAAE;EACvB;EAEAC,WAAWA,CAACC,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACnF,YAAY,GAAGmF,KAAK;IACzB,IAAI,CAAClF,MAAM,GAAG,IAAI;IAClB,IAAI,CAACmF,SAAS,CAACD,KAAK,CAAC;IACrBE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAAC7F,OAAO,CAACwF,KAAK,CAAC,EAAE;MACvB,IAAI,CAACrF,UAAU,CAAC2F,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAAC/F,OAAO,CAACwF,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAQ,YAAYA,CAAA;IACV,IAAI,CAAC1F,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC2F,cAAc,EAAE;IACrBP,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACK,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAd,SAASA,CAACD,KAAa;IACrB,IAAI,CAACnF,YAAY,GAAGmF,KAAK;IACzB,IAAI,CAAC/E,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACyF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACP,KAAK,CAACY,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACpG,YAAY,GAAG,IAAI,CAACL,OAAO,CAACyC,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC/B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmG,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACV,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAAA;IACX,IAAI,IAAI,CAACtG,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmG,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACV,YAAY,EAAE;;EAEvB;EAGAY,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACvG,MAAM,EAAE;IAElB,QAAQuG,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACT,YAAY,EAAE;QACnB;;EAEN;EAEAe,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAACtG,UAAU,EAAE;IAErB,MAAMyG,IAAI,GAAIH,KAAK,CAACI,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGN,KAAK,CAACO,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACX,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEAc,YAAYA,CAACV,KAAiB;IAC5B,IAAI,CAACrG,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGiG,KAAK,CAACW,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAACvG,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEA6G,WAAWA,CAACZ,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACrG,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAGgG,KAAK,CAACW,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,MAAMM,YAAY,GAAG,IAAI,CAAC7G,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAM+G,WAAW,GAAG9C,IAAI,CAAC+C,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAAC7G,0BAA0B,EAAE;MACjD,IAAI4G,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAACjG,UAAU,GAAG,KAAK;;EAE3B;EAEAuH,UAAUA,CAACC,MAAkB;IAC3B,IAAI,CAACxH,UAAU,GAAG,KAAK;EACzB;EAEQmC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGMiD,cAAcA,CAAA;IACpB,MAAMgC,MAAM,GAAGvC,QAAQ,CAACwC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACpF,OAAO,CAACsF,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQ1B,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnG,UAAU,EAAE;IAEtB,MAAM8H,IAAI,GAAG,IAAI,CAAC3H,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAI4H,IAAI,GAAG,GAAG;IAE1B,IAAIxD,IAAI,CAAC+C,GAAG,CAACS,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAAC5H,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAACN,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACM,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAACN,YAAY,EAAE;;MAGrB,IAAI,CAACK,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAACuF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACP,KAAK,CAACY,SAAS,GAAG,6BAA6B,IAAI,CAAC/F,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnB+H,qBAAqB,CAAC,MAAM,IAAI,CAAC5B,MAAM,EAAE,CAAC;;EAE9C;EAEA6B,WAAWA,CAAA;IACT,MAAMxC,KAAK,GAAG,IAAI,CAACvB,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEuB,KAAK,EAAE7B,QAAQ,IAAI6B,KAAK,CAAC7B,QAAQ,CAACzB,MAAM,GAAG,CAAC,CAAC;EACzD;EAEA+F,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAChE,eAAe,EAAE,CAACN,QAAQ,IAAI,EAAE;EAC9C;EAEAuE,WAAWA,CAACrE,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAEsE,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACxC;IACA,IAAI,CAACjJ,MAAM,CAACoJ,QAAQ,CAAC,CAAC,WAAW,EAAEH,OAAO,CAAC3F,GAAG,CAAC,CAAC;EAClD;EAEA+F,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAAC9I,WAAW,EAAEmD,MAAM,IAAI,mCAAmC;EACxE;EAEA4F,iBAAiBA,CAAA;IACfJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAACnJ,MAAM,CAACoJ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAG,MAAMA,CAAA;IACJ,MAAMhF,QAAQ,GAAG,IAAI,CAACsE,gBAAgB,EAAE;IACxC,IAAItE,QAAQ,CAACzB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMmG,OAAO,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B2E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvC;MACA,IAAI,CAACjJ,MAAM,CAACoJ,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCI,WAAW,EAAE;UACXC,SAAS,EAAER,OAAO,CAAC3F,GAAG;UACtBoG,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACAC,WAAWA,CAACF,SAAiB;IAC3B;IACA,IAAI,CAACG,iBAAiB,CAACH,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAACzJ,MAAM,CAACoJ,QAAQ,CAAC,CAAC,eAAe,EAAEK,SAAS,CAAC,CAAC;EACpD;EAEAI,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAAC9J,MAAM,CAACoJ,QAAQ,CAAC,CAAC,gBAAgB,EAAEU,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACH,SAAiB,EAAEM,MAAc;IACzD;IACAb,OAAO,CAACC,GAAG,CAAC,iBAAiBY,MAAM,WAAW,EAAEN,SAAS,CAAC;IAC1D;EACF;EAEAO,aAAaA,CAAA;IACX,MAAMzF,QAAQ,GAAG,IAAI,CAACsE,gBAAgB,EAAE;IACxC,IAAItE,QAAQ,CAACzB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMmG,OAAO,GAAG1E,QAAQ,CAAC,CAAC,CAAC;MAC3B2E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;MAE3C,IAAI,CAAC9I,eAAe,CAAC6J,aAAa,CAACf,OAAO,CAAC3F,GAAG,CAAC,CAAC2G,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDD,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEAE,SAASA,CAAA;IACP,MAAMhG,QAAQ,GAAG,IAAI,CAACsE,gBAAgB,EAAE;IACxC,IAAItE,QAAQ,CAACzB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMmG,OAAO,GAAG1E,QAAQ,CAAC,CAAC,CAAC;MAC3B2E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MAEvC,IAAI,CAAC/I,WAAW,CAACqK,SAAS,CAACtB,OAAO,CAAC3F,GAAG,EAAE,CAAC,EAAEmC,SAAS,EAAEA,SAAS,CAAC,CAACwE,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CD,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACAG,cAAcA,CAACtD,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACuD,aAAa,KAAKhF,SAAS,EAAE;MAC9C,IAAI,CAACjE,iBAAiB,GAAG0F,KAAK,CAACuD,aAAa;MAE5C;MACAvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAAC3H,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAACkJ,oBAAoB,EAAE;;EAE/B;EAEAC,aAAaA,CAACtC,MAAW;IACvB;IACA,IAAI,CAAC/G,qBAAqB,GAAG,IAAI;IACjC4H,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQuB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAACrK,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAACmB,iBAAiB,CAAC,EAAE;MACxD,MAAMoJ,YAAY,GAAG,IAAI,CAACvK,OAAO,CAAC,IAAI,CAACmB,iBAAiB,CAAC;MACzD0H,OAAO,CAACC,GAAG,CAAC,uBAAuByB,YAAY,CAACrH,IAAI,CAACC,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACAqH,cAAcA,CAAA;IACZ,IAAI,CAACtJ,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACA2H,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAC5H,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;EAEA;EACQsB,eAAeA,CAAA;IACrB,MAAM8E,KAAK,GAAGO,MAAM,CAACC,UAAU;IAC/B,MAAM2C,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAAC1K,QAAQ,GAAGuH,KAAK,IAAI,GAAG,IAAIqD,iBAAiB;EACnD;EAGAE,QAAQA,CAAA;IACN,IAAI,CAACrI,eAAe,EAAE;EACxB;CACD;AApmBmDsI,UAAA,EAAjD5L,SAAS,CAAC,kBAAkB,EAAE;EAAE6L,MAAM,EAAE;AAAK,CAAE,CAAC,C,gEAA+B;AAIrCD,UAAA,EAA1C5L,SAAS,CAAC,WAAW,EAAE;EAAE6L,MAAM,EAAE;AAAK,CAAE,CAAC,C,yDAAwB;AACnBD,UAAA,EAA9C5L,SAAS,CAAC,eAAe,EAAE;EAAE6L,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DAA4B;AAEjED,UAAA,EAAR1L,KAAK,EAAE,C,uDAAuB;AACtB0L,UAAA,EAAR1L,KAAK,EAAE,C,4DAA8B;AAC7B0L,UAAA,EAAR1L,KAAK,EAAE,C,2DAAwC;AACtC0L,UAAA,EAATzL,MAAM,EAAE,C,0DAAkE;AAqV3EyL,UAAA,EADC3L,YAAY,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,2DAe5C;AAoPD2L,UAAA,EADC3L,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,sDAGzC;AApmBUM,uBAAuB,GAAAqL,UAAA,EAPnC7L,SAAS,CAAC;EACT+L,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC3L,YAAY,EAAEC,cAAc,CAAC;EACvC2L,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACW3L,uBAAuB,CAqmBnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}