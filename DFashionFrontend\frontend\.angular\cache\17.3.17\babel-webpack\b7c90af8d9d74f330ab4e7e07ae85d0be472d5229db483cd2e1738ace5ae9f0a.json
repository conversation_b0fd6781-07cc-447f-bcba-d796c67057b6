{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nlet ShopByCategoryComponent = class ShopByCategoryComponent {\n  constructor(router) {\n    this.router = router;\n    this.categories = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each category card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4500; // 4.5 seconds for categories\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadCategories();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadCategories() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for shop categories\n        _this.categories = [{\n          id: '1',\n          name: 'Women\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n          productCount: 15420,\n          description: 'Trendy outfits for every occasion',\n          trending: true,\n          discount: 30,\n          subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n        }, {\n          id: '2',\n          name: 'Men\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n          productCount: 12850,\n          description: 'Stylish clothing for modern men',\n          trending: true,\n          discount: 25,\n          subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n        }, {\n          id: '3',\n          name: 'Footwear',\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n          productCount: 8960,\n          description: 'Step up your shoe game',\n          trending: false,\n          discount: 20,\n          subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n        }, {\n          id: '4',\n          name: 'Accessories',\n          image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n          productCount: 6750,\n          description: 'Complete your look',\n          trending: true,\n          subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n        }, {\n          id: '5',\n          name: 'Kids Fashion',\n          image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n          productCount: 4320,\n          description: 'Adorable styles for little ones',\n          trending: false,\n          discount: 35,\n          subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n        }, {\n          id: '6',\n          name: 'Sports & Fitness',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n          productCount: 5680,\n          description: 'Gear up for your workout',\n          trending: true,\n          subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n        }, {\n          id: '7',\n          name: 'Beauty & Personal Care',\n          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n          productCount: 7890,\n          description: 'Look and feel your best',\n          trending: false,\n          discount: 15,\n          subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n        }, {\n          id: '8',\n          name: 'Home & Living',\n          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n          productCount: 3450,\n          description: 'Style your space',\n          trending: false,\n          subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnCategoriesLoad();\n      } catch (error) {\n        console.error('Error loading categories:', error);\n        _this.error = 'Failed to load categories';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/category', category.id]);\n  }\n  formatProductCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadCategories();\n  }\n  trackByCategoryId(index, category) {\n    return category.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.categories.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 160;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 180;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 200;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when categories load\n  updateSliderOnCategoriesLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n};\nShopByCategoryComponent = __decorate([Component({\n  selector: 'app-shop-by-category',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './shop-by-category.component.html',\n  styleUrls: ['./shop-by-category.component.scss']\n})], ShopByCategoryComponent);\nexport { ShopByCategoryComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Subscription", "IonicModule", "ShopByCategoryComponent", "constructor", "router", "categories", "isLoading", "error", "subscription", "currentSlide", "slideOffset", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "maxSlide", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadCategories", "updateResponsiveSettings", "setupResizeListener", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "name", "image", "productCount", "description", "trending", "discount", "subcategories", "updateSliderOnCategoriesLoad", "console", "onCategoryClick", "category", "navigate", "formatProductCount", "count", "toFixed", "toString", "onRetry", "trackByCategoryId", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "pauseAutoSlide", "resumeAutoSlide", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "slidePrev", "restartAutoSlideAfterInteraction", "slideNext", "setTimeout", "__decorate", "selector", "standalone", "imports", "CarouselModule", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\shop-by-category\\shop-by-category.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\n\ninterface ShopCategory {\n  id: string;\n  name: string;\n  image: string;\n  productCount: number;\n  description: string;\n  trending: boolean;\n  discount?: number;\n  subcategories: string[];\n}\n\n@Component({\n  selector: 'app-shop-by-category',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './shop-by-category.component.html',\n  styleUrls: ['./shop-by-category.component.scss']\n})\nexport class ShopByCategoryComponent implements OnInit, OnDestroy {\n  categories: ShopCategory[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each category card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 4500; // 4.5 seconds for categories\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadCategories();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadCategories() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for shop categories\n      this.categories = [\n        {\n          id: '1',\n          name: 'Women\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n          productCount: 15420,\n          description: 'Trendy outfits for every occasion',\n          trending: true,\n          discount: 30,\n          subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n        },\n        {\n          id: '2',\n          name: 'Men\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n          productCount: 12850,\n          description: 'Stylish clothing for modern men',\n          trending: true,\n          discount: 25,\n          subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n        },\n        {\n          id: '3',\n          name: 'Footwear',\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n          productCount: 8960,\n          description: 'Step up your shoe game',\n          trending: false,\n          discount: 20,\n          subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n        },\n        {\n          id: '4',\n          name: 'Accessories',\n          image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n          productCount: 6750,\n          description: 'Complete your look',\n          trending: true,\n          subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n        },\n        {\n          id: '5',\n          name: 'Kids Fashion',\n          image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n          productCount: 4320,\n          description: 'Adorable styles for little ones',\n          trending: false,\n          discount: 35,\n          subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n        },\n        {\n          id: '6',\n          name: 'Sports & Fitness',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n          productCount: 5680,\n          description: 'Gear up for your workout',\n          trending: true,\n          subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n        },\n        {\n          id: '7',\n          name: 'Beauty & Personal Care',\n          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n          productCount: 7890,\n          description: 'Look and feel your best',\n          trending: false,\n          discount: 15,\n          subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n        },\n        {\n          id: '8',\n          name: 'Home & Living',\n          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n          productCount: 3450,\n          description: 'Style your space',\n          trending: false,\n          subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnCategoriesLoad();\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      this.error = 'Failed to load categories';\n      this.isLoading = false;\n    }\n  }\n\n  onCategoryClick(category: ShopCategory) {\n    this.router.navigate(['/category', category.id]);\n  }\n\n  formatProductCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadCategories();\n  }\n\n  trackByCategoryId(index: number, category: ShopCategory): string {\n    return category.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.categories.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 160;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 180;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 200;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when categories load\n  private updateSliderOnCategoriesLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAoBrC,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAmBlCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlB1B,KAAAC,UAAU,GAAmB,EAAE;IAC/B,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IACnB,KAAAC,YAAY,GAAiB,IAAIR,YAAY,EAAE;IAEvD;IACA,KAAAS,YAAY,GAAG,CAAC;IAChB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,QAAQ,GAAG,CAAC;IAIZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,YAAY,CAACc,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcL,cAAcA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAC1B,IAAI;QACFD,KAAI,CAAClB,SAAS,GAAG,IAAI;QACrBkB,KAAI,CAACjB,KAAK,GAAG,IAAI;QAEjB;QACAiB,KAAI,CAACnB,UAAU,GAAG,CAChB;UACEqB,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,kBAAkB;UACxBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,KAAK;UACnBC,WAAW,EAAE,mCAAmC;UAChDC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa;SAC5D,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,gBAAgB;UACtBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,KAAK;UACnBC,WAAW,EAAE,iCAAiC;UAC9CC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO;SACtD,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,gFAAgF;UACvFC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,wBAAwB;UACrCC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;SACzD,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,oBAAoB;UACjCC,QAAQ,EAAE,IAAI;UACdE,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;SACtD,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,cAAc;UACpBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,iCAAiC;UAC9CC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;SAChD,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,kBAAkB;UACxBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,0BAA0B;UACvCC,QAAQ,EAAE,IAAI;UACdE,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa;SAClE,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,wBAAwB;UAC9BC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,yBAAyB;UACtCC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW;SAC9D,EACD;UACEP,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,eAAe;UACrBC,KAAK,EAAE,mFAAmF;UAC1FC,YAAY,EAAE,IAAI;UAClBC,WAAW,EAAE,kBAAkB;UAC/BC,QAAQ,EAAE,KAAK;UACfE,aAAa,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS;SAC3D,CACF;QAEDT,KAAI,CAAClB,SAAS,GAAG,KAAK;QACtBkB,KAAI,CAACU,4BAA4B,EAAE;OACpC,CAAC,OAAO3B,KAAK,EAAE;QACd4B,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDiB,KAAI,CAACjB,KAAK,GAAG,2BAA2B;QACxCiB,KAAI,CAAClB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA8B,eAAeA,CAACC,QAAsB;IACpC,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,WAAW,EAAED,QAAQ,CAACX,EAAE,CAAC,CAAC;EAClD;EAEAa,kBAAkBA,CAACC,KAAa;IAC9B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACzB,cAAc,EAAE;EACvB;EAEA0B,iBAAiBA,CAACC,KAAa,EAAER,QAAsB;IACrD,OAAOA,QAAQ,CAACX,EAAE;EACpB;EAEA;EACQoB,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC/B,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACO,aAAa,EAAE;IACpB,IAAI,CAACwB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAAChC,QAAQ,IAAI,IAAI,CAACX,UAAU,CAAC4C,MAAM,GAAG,IAAI,CAACrC,YAAY,EAAE;QAChE,IAAI,CAACsC,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACpC,cAAc,CAAC;EACzB;EAEQS,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACwB,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACzC,YAAY,IAAI,IAAI,CAACI,QAAQ,EAAE;MACtC,IAAI,CAACJ,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC2C,iBAAiB,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACrC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEA+B,eAAeA,CAAA;IACb,IAAI,CAACtC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC8B,cAAc,EAAE;EACvB;EAEA;EACQ3B,wBAAwBA,CAAA;IAC9B,MAAMoC,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAC5C,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI2C,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAC5C,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI2C,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAC5C,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAAC8C,kBAAkB,EAAE;IACzB,IAAI,CAACN,iBAAiB,EAAE;EAC1B;EAEQhC,mBAAmBA,CAAA;IACzBoC,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACxC,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACAuC,kBAAkBA,CAAA;IAChB,IAAI,CAAC7C,QAAQ,GAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxD,UAAU,CAAC4C,MAAM,GAAG,IAAI,CAACrC,YAAY,CAAC;EACzE;EAEAkD,SAASA,CAAA;IACP,IAAI,IAAI,CAACrD,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC2C,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACvD,YAAY,GAAG,IAAI,CAACI,QAAQ,EAAE;MACrC,IAAI,CAACJ,YAAY,EAAE;MACnB,IAAI,CAAC2C,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEQX,iBAAiBA,CAAA;IACvB,IAAI,CAAC1C,WAAW,GAAG,CAAC,IAAI,CAACD,YAAY,GAAG,IAAI,CAACE,SAAS;EACxD;EAEQoD,gCAAgCA,CAAA;IACtC,IAAI,CAACxC,aAAa,EAAE;IACpB0C,UAAU,CAAC,MAAK;MACd,IAAI,CAACnB,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQZ,4BAA4BA,CAAA;IAClC+B,UAAU,CAAC,MAAK;MACd,IAAI,CAACP,kBAAkB,EAAE;MACzB,IAAI,CAACjD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACoC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;CAGD;AA7PY5C,uBAAuB,GAAAgE,UAAA,EAPnCpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtE,YAAY,EAAEE,WAAW,EAAEqE,cAAc,CAAC;EACpDC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACWtE,uBAAuB,CA6PnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}