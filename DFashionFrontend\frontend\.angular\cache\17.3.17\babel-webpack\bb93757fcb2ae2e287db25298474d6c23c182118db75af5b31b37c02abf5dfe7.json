{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_6_div_1_Template, 3, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"span\");\n    i0.ɵɵtext(2, \"NEW\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 30);\n    i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template, 2, 0, \"div\", 31)(4, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.user.isBrand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasNewProducts);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed)(\"brand-ring\", story_r5.user.isBrand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_7_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template, 8, 9, \"ng-template\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_7_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 23);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"div\", 25)(12, \"owl-carousel-o\", 26);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_7_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_7_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_7_ng_container_13_Template, 2, 0, \"ng-container\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 76);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 77)(4, \"div\", 78);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_8_div_21_div_1_Template, 8, 2, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 85);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 59);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 21);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38, 0)(3, \"div\", 39);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_8_div_4_Template, 2, 4, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_8_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_8_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_8_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"div\", 43);\n    i0.ɵɵelement(8, \"div\", 44);\n    i0.ɵɵelementStart(9, \"div\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 46);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 47);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 50);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_8_video_18_Template, 1, 1, \"video\", 51)(19, ViewAddStoriesComponent_div_8_div_19_Template, 1, 2, \"div\", 52)(20, ViewAddStoriesComponent_div_8_div_20_Template, 2, 1, \"div\", 53)(21, ViewAddStoriesComponent_div_8_div_21_Template, 2, 1, \"div\", 54)(22, ViewAddStoriesComponent_div_8_div_22_Template, 5, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 56)(24, \"div\", 57)(25, \"button\", 58);\n    i0.ɵɵelement(26, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 60);\n    i0.ɵɵelement(28, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 62);\n    i0.ɵɵelement(30, \"i\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_8_div_31_Template, 13, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 65)(33, \"div\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 67, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵelement(2, \"i\", 90);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 92);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    // Mobile detection\n    this.isMobile = false;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // E-commerce Optimized Carousel Options - Mobile Responsive\n    this.customOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: true,\n      dots: false,\n      nav: false,\n      navSpeed: 500,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      margin: 8,\n      stagePadding: 10,\n      autoplay: false,\n      autoplayHoverPause: true,\n      slideBy: 1,\n      freeDrag: true,\n      responsive: {\n        0: {\n          items: 3,\n          nav: false,\n          margin: 6,\n          stagePadding: 15,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        400: {\n          items: 4,\n          nav: false,\n          margin: 8,\n          stagePadding: 12,\n          touchDrag: true,\n          mouseDrag: true,\n          pullDrag: true\n        },\n        600: {\n          items: 5,\n          nav: false,\n          margin: 10,\n          stagePadding: 10\n        },\n        768: {\n          items: 6,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        },\n        940: {\n          items: 7,\n          nav: true,\n          margin: 12,\n          stagePadding: 0\n        }\n      }\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: 'user1',\n        username: 'zara',\n        fullName: 'Zara Official',\n        avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'New Summer Collection 🌞',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod1',\n        name: 'Summer Dress',\n        price: 89.99,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '2',\n      user: {\n        _id: 'user2',\n        username: 'nike',\n        fullName: 'Nike',\n        avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Just Do It ✨',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 2340,\n      hasNewProducts: false,\n      products: [{\n        _id: 'prod2',\n        name: 'Air Max Sneakers',\n        price: 129.99,\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '3',\n      user: {\n        _id: 'user3',\n        username: 'adidas',\n        fullName: 'Adidas',\n        avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n        isBrand: true,\n        isVerified: true\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Impossible is Nothing 🔥',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 1890,\n      hasNewProducts: true,\n      products: [{\n        _id: 'prod3',\n        name: 'Ultraboost Shoes',\n        price: 159.99,\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n      }],\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '4',\n      user: {\n        _id: 'user4',\n        username: 'hm',\n        fullName: 'H&M',\n        avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Fashion for Everyone 💫',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 3420,\n      isActive: true,\n      isViewed: false\n    }];\n    this.isLoadingStories = false;\n  }\n  // Removed fallback stories - only use database data\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  // Mobile detection method\n  checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n  onResize() {\n    this.checkScreenSize();\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument)(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        stories: \"stories\",\n        showAddStory: \"showAddStory\",\n        currentUser: \"currentUser\"\n      },\n      outputs: {\n        storyClick: \"storyClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"stories-subtitle\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-slide\", \"add-story-slide\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", \"brand-story-slide\", 3, \"click\"], [1, \"story-avatar\"], [\"class\", \"brand-badge\", 4, \"ngIf\"], [\"class\", \"new-product-badge\", 4, \"ngIf\"], [1, \"story-ring\"], [1, \"brand-badge\"], [1, \"fas\", \"fa-crown\"], [1, \"new-product-badge\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h3\", 4);\n          i0.ɵɵtext(3, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 5);\n          i0.ɵɵtext(5, \"Watch stories from people you follow\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_6_Template, 2, 2, \"div\", 6)(7, ViewAddStoriesComponent_div_7_Template, 14, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_8_Template, 36, 17, \"div\", 8)(9, ViewAddStoriesComponent_div_9_Template, 9, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n  width: 100%;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 12px 8px;\\n    border: none;\\n    border-radius: 0;\\n    border-bottom: 1px solid #efefef;\\n    background: #ffffff;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    border: 1px solid #dbdbdb;\\n    border-radius: 8px;\\n    padding: 24px;\\n    margin-bottom: 24px;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n    position: relative;\\n    z-index: 10;\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    min-height: 150px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n@media (min-width: 769px) {\\n  .stories-header[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #efefef;\\n    padding-bottom: 16px;\\n    margin-bottom: 24px;\\n  }\\n}\\n\\n.stories-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 4px 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    font-weight: 700;\\n  }\\n}\\n\\n.stories-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  margin: 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-subtitle[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 20px;\\n    align-items: center;\\n    min-height: 120px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 16px;\\n  }\\n}\\n\\n.add-story-static[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 82px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n    -webkit-overflow-scrolling: touch;\\n    touch-action: pan-x;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 98px);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%]::after {\\n    content: \\\"\\\";\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    width: 20px;\\n    height: 100%;\\n    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);\\n    pointer-events: none;\\n    z-index: 5;\\n    opacity: 0;\\n    transition: opacity 0.3s ease;\\n  }\\n  .stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]::after {\\n    opacity: 1;\\n  }\\n}\\n\\n.stories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow: visible; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%] {\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n    overflow: hidden;\\n    touch-action: pan-x;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n    touch-action: pan-x;\\n    will-change: transform;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  min-height: 120px;\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n    min-height: 90px;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5); \\n\\n  color: #fff; \\n\\n  border: none;\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  pointer-events: all;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas {\\n  font-size: 14px;\\n  color: #fff;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -20px; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -20px; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n    display: none;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer {\\n  position: relative;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_autoSlideIndicator 4s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_autoSlideIndicator {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 66px;\\n}\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  animation-play-state: paused;\\n}\\n\\n.slider-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.slider-nav-btn.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n}\\n\\n.slider-nav-left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n\\n.slider-nav-right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n\\n.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  width: 82px;\\n  min-width: 82px;\\n  position: relative;\\n}\\n@media (min-width: 769px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    min-width: 90px;\\n    padding: 8px;\\n    border-radius: 12px;\\n  }\\n  .story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n    background: rgba(0, 0, 0, 0.05);\\n    transform: scale(1.08);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 76px;\\n    min-width: 76px;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%] {\\n  animation-duration: 1s;\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n}\\n.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #405de6;\\n  font-weight: 600;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-container[_ngcontent-%COMP%] {\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 2;\\n  transition: all 0.3s ease;\\n}\\n@media (min-width: 769px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 74px;\\n    height: 74px;\\n    border: 3px solid #fff;\\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  }\\n  .story-avatar[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.05);\\n    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    border: 1.5px solid #fff;\\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n@media (min-width: 769px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    top: -3px;\\n    left: -3px;\\n    width: 80px;\\n    height: 80px;\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n    box-shadow: 0 0 20px rgba(240, 148, 51, 0.3);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n}\\n.story-ring.viewed[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  animation: none;\\n}\\n.story-ring.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n  box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  margin-top: 8px;\\n}\\n@media (min-width: 769px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    font-weight: 500;\\n    max-width: 90px;\\n    margin-top: 10px;\\n    color: #262626;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n    font-weight: 500;\\n    margin-top: 6px;\\n  }\\n}\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  position: relative;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 3;\\n}\\n.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n  font-weight: bold;\\n}\\n\\n.current-user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 12px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 82px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    min-width: 70px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 0 8px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    min-width: 60px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n  .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .current-user-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n.middle-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 4;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .middle-navigation[_ngcontent-%COMP%] {\\n    bottom: 80px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 12px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n.brand-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: linear-gradient(135deg, #ffd700, #ffb300);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 10;\\n}\\n.brand-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: white;\\n}\\n@media (min-width: 769px) {\\n  .brand-badge[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .brand-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n.new-product-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -4px;\\n  left: -4px;\\n  background: linear-gradient(135deg, #ff4757, #ff3742);\\n  color: white;\\n  font-size: 8px;\\n  font-weight: 700;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  border: 1px solid white;\\n  z-index: 10;\\n}\\n.new-product-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n  letter-spacing: 0.5px;\\n}\\n@media (min-width: 769px) {\\n  .new-product-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 3px 7px;\\n  }\\n  .new-product-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n\\n.story-ring.brand-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ffd700, #ffb300, #ff6b6b, #4ecdc4);\\n  background-size: 300% 300%;\\n  animation: _ngcontent-%COMP%_brandGradient 3s ease infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_brandGradient {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n@media (min-width: 769px) {\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 74px;\\n    height: 74px;\\n    border: 3px solid #dbdbdb;\\n  }\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .current-user-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-color: #f5f5f5;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 24px;\\n  height: 24px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 10;\\n}\\n.add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n@media (min-width: 769px) {\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .add-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.brand-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.brand-story-slide[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_6_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵtext", "ɵɵlistener", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "i_r4", "ɵɵnextContext", "index", "ctx_r1", "ɵɵresetView", "openStories", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_3_Template", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_div_4_Template", "ɵɵstyleProp", "story_r5", "user", "avatar", "isBrand", "hasNewProducts", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "username", "ɵɵelementContainerStart", "ViewAddStoriesComponent_div_7_ng_container_13_ng_template_1_Template", "ViewAddStoriesComponent_div_7_Template_div_click_2_listener", "_r1", "openAddStoryModal", "ViewAddStoriesComponent_div_7_Template_owl_carousel_o_initialized_12_listener", "$event", "onInitialized", "ViewAddStoriesComponent_div_7_Template_owl_carousel_o_changed_12_listener", "onSlideChanged", "ViewAddStoriesComponent_div_7_ng_container_13_Template", "getCurrentUserAvatar", "customOptions", "stories", "i_r7", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "ViewAddStoriesComponent_div_8_div_21_div_1_Template_div_click_0_listener", "product_r9", "_r8", "$implicit", "viewProduct", "_id", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_8_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_8_div_22_Template_button_click_1_listener", "_r10", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_1_listener", "_r11", "buyNow", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_5_listener", "addToWishlist", "ViewAddStoriesComponent_div_8_div_31_Template_button_click_9_listener", "addToCart", "ViewAddStoriesComponent_div_8_div_4_Template", "ViewAddStoriesComponent_div_8_Template_div_click_5_listener", "_r6", "onStoryClick", "ViewAddStoriesComponent_div_8_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_8_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_8_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_8_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_8_video_18_Template", "ViewAddStoriesComponent_div_8_div_19_Template", "ViewAddStoriesComponent_div_8_div_20_Template", "ViewAddStoriesComponent_div_8_div_21_Template", "ViewAddStoriesComponent_div_8_div_22_Template", "ViewAddStoriesComponent_div_8_div_31_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "isMobile", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "nav", "navSpeed", "navText", "margin", "stagePadding", "autoplay", "autoplayHoverPause", "slideBy", "freeDrag", "responsive", "items", "subscriptions", "ngOnInit", "checkScreenSize", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "isVerified", "Date", "now", "toISOString", "expiresAt", "products", "image", "isActive", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "num", "undefined", "toFixed", "toString", "showStory", "document", "body", "style", "overflow", "emit", "story", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "touches", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "toLocaleString", "viewProductDetails", "product", "console", "log", "navigate", "queryParams", "productId", "source", "trackProductClick", "viewCategory", "categoryId", "action", "subscribe", "next", "response", "success", "alert", "error", "startPosition", "updateSlideAnalytics", "currentStory", "toggleAutoPlay", "userAgent", "navigator", "isMobileUserAgent", "test", "onResize", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "CartService", "i4", "WishlistService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_div_6_Template", "ViewAddStoriesComponent_div_7_Template", "ViewAddStoriesComponent_div_8_Template", "ViewAddStoriesComponent_div_9_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mponent, OnInit, On<PERSON>estroy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\nimport { environment } from '../../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isBrand?: boolean; // E-commerce: Brand account indicator\n    isVerified?: boolean; // E-commerce: Verified account\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  hasNewProducts?: boolean; // E-commerce: Has new products\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n\n  // Mobile detection\n  isMobile = false;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // E-commerce Optimized Carousel Options - Mobile Responsive\n  customOptions: OwlOptions = {\n    loop: false, // Don't loop for better UX with Add Story first\n    mouseDrag: true,\n    touchDrag: true, // Essential for mobile\n    pullDrag: true, // Allow pull drag on mobile\n    dots: false,\n    nav: false, // Hide nav on mobile, show on desktop\n    navSpeed: 500,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    margin: 8, // Default margin\n    stagePadding: 10, // Add padding for better mobile UX\n    autoplay: false,\n    autoplayHoverPause: true,\n    slideBy: 1, // Slide one item at a time\n    freeDrag: true, // Allow free dragging on mobile\n    responsive: {\n      0: {\n        items: 3, // 3 stories visible on small mobile\n        nav: false,\n        margin: 6,\n        stagePadding: 15,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      400: {\n        items: 4, // 4 stories on larger mobile\n        nav: false,\n        margin: 8,\n        stagePadding: 12,\n        touchDrag: true,\n        mouseDrag: true,\n        pullDrag: true\n      },\n      600: {\n        items: 5, // 5 stories on tablet\n        nav: false,\n        margin: 10,\n        stagePadding: 10\n      },\n      768: {\n        items: 6, // 6 stories on large tablet\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      },\n      940: {\n        items: 7, // 7 stories on desktop\n        nav: true,\n        margin: 12,\n        stagePadding: 0\n      }\n    }\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Check screen size for mobile detection\n    this.checkScreenSize();\n\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [\n      {\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(), // 22 hours from now\n        views: 1250,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod1',\n            name: 'Summer Dress',\n            price: 89.99,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(), // 20 hours from now\n        views: 2340,\n        hasNewProducts: false,\n        products: [\n          {\n            _id: 'prod2',\n            name: 'Air Max Sneakers',\n            price: 129.99,\n            image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center',\n          isBrand: true,\n          isVerified: true\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now\n        views: 1890,\n        hasNewProducts: true,\n        products: [\n          {\n            _id: 'prod3',\n            name: 'Ultraboost Shoes',\n            price: 159.99,\n            image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=200&h=200&fit=crop'\n          }\n        ],\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(), // 16 hours from now\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }\n    ];\n\n    this.isLoadingStories = false;\n  }\n\n  // Removed fallback stories - only use database data\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n\n  // Mobile detection method\n  private checkScreenSize() {\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize() {\n    this.checkScreenSize();\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Web Stories Header (Desktop/Tablet Only) -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <p class=\"stories-subtitle\">Watch stories from people you follow</p>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- E-commerce Stories Section - Static Add Story + Slider -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Static Add Story (Outside Slider) -->\n    <div class=\"add-story-static\">\n      <div class=\"story-slide add-story-slide\" (click)=\"openAddStoryModal()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"add-story-avatar\">\n            <div class=\"add-story-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"current-user-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\"></div>\n          </div>\n        </div>\n        <div class=\"story-username\">Add Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider (Only Brand/User Stories) -->\n    <div class=\"stories-slider-wrapper\">\n      <div class=\"stories-slider-container\">\n        <owl-carousel-o\n          [options]=\"customOptions\"\n          (initialized)=\"onInitialized($event)\"\n          (changed)=\"onSlideChanged($event)\">\n\n          <!-- Brand/User Stories Only -->\n          <ng-container *ngFor=\"let story of stories; let i = index\">\n            <ng-template carouselSlide>\n              <div class=\"story-slide brand-story-slide\" (click)=\"openStories(i)\">\n                <div class=\"story-avatar-container\">\n                  <div class=\"story-avatar\"\n                       [style.background-image]=\"'url(' + story.user.avatar + ')'\">\n                    <!-- E-commerce Badge for Brand Stories -->\n                    <div class=\"brand-badge\" *ngIf=\"story.user.isBrand\">\n                      <i class=\"fas fa-crown\"></i>\n                    </div>\n                    <!-- New Product Badge -->\n                    <div class=\"new-product-badge\" *ngIf=\"story.hasNewProducts\">\n                      <span>NEW</span>\n                    </div>\n                  </div>\n                  <div class=\"story-ring\"\n                       [class.viewed]=\"story.isViewed\"\n                       [class.brand-ring]=\"story.user.isBrand\">\n                  </div>\n                </div>\n                <div class=\"story-username\">{{ story.user.username }}</div>\n              </div>\n            </ng-template>\n          </ng-container>\n        </owl-carousel-o>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product._id)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Middle Point Navigation Button -->\n        <div class=\"middle-navigation\" *ngIf=\"hasProducts()\">\n          <button class=\"middle-nav-btn\" (click)=\"viewProduct(getStoryProducts()[0]._id)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>Shop Now</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\" *ngIf=\"hasProducts()\">\n          <button class=\"ecommerce-btn buy-now-btn\" (click)=\"buyNow()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\" (click)=\"addToCart()\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2FA,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICG3DC,EAAA,CAAAC,cAAA,cAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,kBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;IAuCpBT,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGJH,EADF,CAAAC,cAAA,cAA4D,WACpD;IAAAD,EAAA,CAAAU,MAAA,UAAG;IACXV,EADW,CAAAG,YAAA,EAAO,EACZ;;;;;;IAXZH,EAAA,CAAAC,cAAA,cAAoE;IAAzBD,EAAA,CAAAW,UAAA,mBAAAC,0FAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAE/Df,EADF,CAAAC,cAAA,cAAoC,cAE+B;IAM/DD,EAJA,CAAAI,UAAA,IAAAiB,0EAAA,kBAAoD,IAAAC,0EAAA,kBAIQ;IAG9DtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAGM;IACRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IACvDV,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAhBGH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAuB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAEpC1B,EAAA,CAAAM,SAAA,EAAwB;IAAxBN,EAAA,CAAAO,UAAA,SAAAiB,QAAA,CAAAC,IAAA,CAAAE,OAAA,CAAwB;IAIlB3B,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAO,UAAA,SAAAiB,QAAA,CAAAI,cAAA,CAA0B;IAKvD5B,EAAA,CAAAM,SAAA,EAA+B;IAC/BN,EADA,CAAA6B,WAAA,WAAAL,QAAA,CAAAM,QAAA,CAA+B,eAAAN,QAAA,CAAAC,IAAA,CAAAE,OAAA,CACQ;IAGlB3B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA+B,iBAAA,CAAAP,QAAA,CAAAC,IAAA,CAAAO,QAAA,CAAyB;;;;;IApB3DhC,EAAA,CAAAiC,uBAAA,GAA2D;IACzDjC,EAAA,CAAAI,UAAA,IAAA8B,oEAAA,0BAA2B;;;;;;;IAvBjClC,EAHJ,CAAAC,cAAA,cAAuD,cAEvB,cAC2C;IAA9BD,EAAA,CAAAW,UAAA,mBAAAwB,4DAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmB,iBAAA,EAAmB;IAAA,EAAC;IAGhErC,EAFJ,CAAAC,cAAA,cAAoC,cACJ,cACA;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAwG;IAE5GF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,gBAAS;IAEzCV,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAoC,eACI,0BAIC;IAAnCD,EADA,CAAAW,UAAA,yBAAA2B,8EAAAC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAAeD,MAAA,CAAAsB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,qBAAAE,0EAAAF,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAlB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAC1BD,MAAA,CAAAwB,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAGlCvC,EAAA,CAAAI,UAAA,KAAAuC,sDAAA,2BAA2D;IA2BnE3C,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACF;;;;IA3CqCH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA0B,oBAAA,SAAgE;IAWnG5C,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA2B,aAAA,CAAyB;IAKO7C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA4B,OAAA,CAAY;;;;;IAoChD9C,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAA6B,WAAA,WAAAkB,IAAA,KAAA7B,MAAA,CAAA8B,YAAA,CAAmC,cAAAD,IAAA,GAAA7B,MAAA,CAAA8B,YAAA,CACC;;;;;IA6BpChD,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAW,MAAA,CAAA+B,eAAA,GAAAC,QAAA,EAAAlD,EAAA,CAAAmD,aAAA,CAAkC;;;;;IAQpCnD,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA+B,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItElD,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAoD,kBAAA,MAAAlC,MAAA,CAAA+B,eAAA,GAAAI,OAAA,MACF;;;;;;IAIErD,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAW,UAAA,mBAAA2C,yEAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAa,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAwC,WAAA,CAAAH,UAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IAClC3D,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAU,MAAA,yBAAG;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IAEnEV,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAA+B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACjB5D,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA2C,WAAA,CAAAN,UAAA,CAAAO,KAAA,EAAgC;;;;;IARrE9D,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAI,UAAA,IAAA2D,mDAAA,kBAGqC;IAOvC/D,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA8C,gBAAA,GAAqB;;;;;;IAa3ChE,EADF,CAAAC,cAAA,cAAqD,iBAC6B;IAAjDD,EAAA,CAAAW,UAAA,mBAAAsD,sEAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAqD,IAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAwC,WAAA,CAAYxC,MAAA,CAAA8C,gBAAA,EAAkB,CAAC,CAAC,EAAAL,GAAA,CAAM;IAAA,EAAC;IAC7E3D,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAElBV,EAFkB,CAAAG,YAAA,EAAO,EACd,EACL;;;;;;IAmBJH,EADF,CAAAC,cAAA,cAA4D,iBACG;IAAnBD,EAAA,CAAAW,UAAA,mBAAAwD,sEAAA;MAAAnE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmD,MAAA,EAAQ;IAAA,EAAC;IAC1DrE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,cAAO;IACfV,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA1BD,EAAA,CAAAW,UAAA,mBAAA2D,sEAAA;MAAAtE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAqD,aAAA,EAAe;IAAA,EAAC;IAClEvE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAChBV,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,iBAA6D;IAAtBD,EAAA,CAAAW,UAAA,mBAAA6D,sEAAA;MAAAxE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAuD,SAAA,EAAW;IAAA,EAAC;IAC1DzE,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAErBV,EAFqB,CAAAG,YAAA,EAAO,EACjB,EACL;;;;;;IA3GVH,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAsE,4CAAA,kBAIuC;IAGzC1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAW,UAAA,mBAAAgE,4DAAApC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA2D,YAAA,CAAAtC,MAAA,CAAoB;IAAA,EAAC,wBAAAuC,iEAAAvC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAChBD,MAAA,CAAA6D,YAAA,CAAAxC,MAAA,CAAoB;IAAA,EAAC,uBAAAyC,gEAAAzC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CACtBD,MAAA,CAAA+D,WAAA,CAAA1C,MAAA,CAAmB;IAAA,EAAC,sBAAA2C,+DAAA3C,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CACrBD,MAAA,CAAAiE,UAAA,CAAA5C,MAAA,CAAkB;IAAA,EAAC;IAIhCvC,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAqC;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,IAA6C;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAiD;IAC7EV,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAW,UAAA,mBAAAyE,gEAAA;MAAApF,EAAA,CAAAa,aAAA,CAAA+D,GAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmE,YAAA,EAAc;IAAA,EAAC;IACnDrF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAuC1BD,EArCA,CAAAI,UAAA,KAAAkF,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP,KAAAC,6CAAA,kBAcF;IAMvD1F,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAuF,6CAAA,mBAA4D;IAc9D3F,EAAA,CAAAG,YAAA,EAAM;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IA1HuBH,EAAA,CAAA6B,WAAA,YAAAX,MAAA,CAAA0E,MAAA,CAAwB;IAM3B5F,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA4B,OAAA,CAAY;IAU7B9C,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAuB,WAAA,8BAAAL,MAAA,CAAA+B,eAAA,GAAAxB,IAAA,CAAAC,MAAA,OAAuE;IACzE1B,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA+B,eAAA,GAAAxB,IAAA,CAAAoE,QAAA,CAAqC;IACrC7F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA4E,UAAA,CAAA5E,MAAA,CAAA+B,eAAA,GAAA8C,SAAA,EAA6C;IAC5C/F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAoD,kBAAA,KAAAlC,MAAA,CAAA8E,YAAA,CAAA9E,MAAA,CAAA+B,eAAA,GAAAgD,KAAA,YAAiD;IAW1EjG,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAiD,SAAA,aAA6C;IAW7ClG,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAiD,SAAA,aAA6C;IAM1ClG,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAA+B,eAAA,GAAAI,OAAA,CAA+B;IAK/BrD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAcOnG,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAuBZnG,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAW,MAAA,CAAAiF,WAAA,GAAmB;IAuB5BnG,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAA6B,WAAA,cAAAX,MAAA,CAAA0E,MAAA,CAA0B;;;;;IAK9D5F,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IACtBV,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;AD5JN,OAAM,MAAOiG,uBAAuB;EA6FlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IA9FzB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAIP,KAAA5D,OAAO,GAAY,EAAE;IACrB,KAAA6D,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAIhH,YAAY,EAAmC;IAE1E,KAAAiH,gBAAgB,GAAG,IAAI;IAEvB,KAAA9D,YAAY,GAAG,CAAC;IAChB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAmB,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA9E,aAAa,GAAe;MAC1B+E,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,EAAE;UAChBP,SAAS,EAAE,IAAI;UACfD,SAAS,EAAE,IAAI;UACfE,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHY,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,KAAK;UACVG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHM,KAAK,EAAE,CAAC;UACRV,GAAG,EAAE,IAAI;UACTG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE;;;KAGnB;IAEO,KAAAO,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA,IAAI,CAAC,IAAI,CAAChG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACiG,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAAClC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACmC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAAClC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAChE,OAAO,GAAG,CACb;MACEa,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,eAAe;QACzBnE,MAAM,EAAE,6FAA6F;QACrGC,OAAO,EAAE,IAAI;QACb4H,UAAU,EAAE;OACb;MACDrG,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzD,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,IAAI;MACpBgI,QAAQ,EAAE,CACR;QACEjG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE,KAAK;QACZ+F,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdhI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,MAAM;QAChBnE,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACb4H,UAAU,EAAE;OACb;MACDrG,QAAQ,EAAE,gFAAgF;MAC1FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,cAAc;MACvB0C,SAAS,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzD,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,KAAK;MACrBgI,QAAQ,EAAE,CACR;QACEjG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,kBAAkB;QACxBE,KAAK,EAAE,MAAM;QACb+F,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdhI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,QAAQ;QAClB6D,QAAQ,EAAE,QAAQ;QAClBnE,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,IAAI;QACb4H,UAAU,EAAE;OACb;MACDrG,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzD,KAAK,EAAE,IAAI;MACXrE,cAAc,EAAE,IAAI;MACpBgI,QAAQ,EAAE,CACR;QACEjG,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,kBAAkB;QACxBE,KAAK,EAAE,MAAM;QACb+F,KAAK,EAAE;OACR,CACF;MACDC,QAAQ,EAAE,IAAI;MACdhI,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRlC,IAAI,EAAE;QACJkC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,IAAI;QACd6D,QAAQ,EAAE,KAAK;QACfnE,MAAM,EAAE;OACT;MACDwB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,yBAAyB;MAClC0C,SAAS,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzD,KAAK,EAAE,IAAI;MACX6D,QAAQ,EAAE,IAAI;MACdhI,QAAQ,EAAE;KACX,CACF;IAED,IAAI,CAACgF,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EAEA7D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAgD,UAAUA,CAACiE,UAAkB;IAC3B,MAAMN,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMQ,IAAI,GAAG,IAAIR,IAAI,CAACO,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACV,GAAG,CAACW,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAtE,YAAYA,CAACuE,GAAW;IACtB,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;MAC7C,OAAO,GAAG;;IAEZ,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOF,GAAG,CAACG,QAAQ,EAAE;EACvB;EAEAtJ,WAAWA,CAACH,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC+B,YAAY,GAAG/B,KAAK;IACzB,IAAI,CAAC2E,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC+E,SAAS,CAAC1J,KAAK,CAAC;IACrB2J,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAACjI,OAAO,CAAC7B,KAAK,CAAC,EAAE;MACvB,IAAI,CAAC4F,UAAU,CAACmE,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACnI,OAAO,CAAC7B,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAoE,YAAYA,CAAA;IACV,IAAI,CAACO,MAAM,GAAG,KAAK;IACnB,IAAI,CAACsF,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAb,SAASA,CAAC1J,KAAa;IACrB,IAAI,CAAC+B,YAAY,GAAG/B,KAAK;IACzB,IAAI,CAACgG,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACkE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1I,YAAY,GAAG,IAAI,CAACF,OAAO,CAACiG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC7B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtG,YAAY,EAAE;;EAEvB;EAEAuG,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5I,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACkE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtG,YAAY,EAAE;;EAEvB;EAGAwG,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAClG,MAAM,EAAE;IAElB,QAAQkG,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACrG,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACiH,KAAiB;IAC5B,IAAI,IAAI,CAAC/E,UAAU,EAAE;IAErB,MAAMiF,IAAI,GAAIF,KAAK,CAACG,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACV,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEA3G,YAAYA,CAAC+G,KAAiB;IAC5B,IAAI,CAAC9E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG0E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC1C,IAAI,CAAC/E,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEAnC,WAAWA,CAAC6G,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC9E,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAGyE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC5C,MAAMI,YAAY,GAAG,IAAI,CAACnF,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAMqF,WAAW,GAAGvC,IAAI,CAACwC,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAACnF,0BAA0B,EAAE;MACjD,IAAIkF,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACZ,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAAC1E,UAAU,GAAG,KAAK;;EAE3B;EAEA7B,UAAUA,CAAC0H,MAAkB;IAC3B,IAAI,CAAC7F,UAAU,GAAG,KAAK;EACzB;EAEQiC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGM4B,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGlC,QAAQ,CAACmC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAAC3D,OAAO,CAAC6D,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQtB,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC5E,UAAU,EAAE;IAEtB,MAAMmG,IAAI,GAAG,IAAI,CAAChG,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAIiG,IAAI,GAAG,GAAG;IAE1B,IAAIhD,IAAI,CAACwC,GAAG,CAACQ,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAACjG,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAACnE,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACmE,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAACnE,YAAY,EAAE;;MAGrB,IAAI,CAACkE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAACgE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,6BAA6B,IAAI,CAACxE,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnBoG,qBAAqB,CAAC,MAAM,IAAI,CAACxB,MAAM,EAAE,CAAC;;EAE9C;EAEAxF,WAAWA,CAAA;IACT,MAAM8E,KAAK,GAAG,IAAI,CAAChI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEgI,KAAK,EAAErB,QAAQ,IAAIqB,KAAK,CAACrB,QAAQ,CAACb,MAAM,GAAG,CAAC,CAAC;EACzD;EAEA/E,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,eAAe,EAAE,CAAC2G,QAAQ,IAAI,EAAE;EAC9C;EAEA/F,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAEsJ,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACxC;IACA,IAAI,CAAChH,MAAM,CAACmH,QAAQ,CAAC,CAAC,WAAW,EAAEH,OAAO,CAAC3J,GAAG,CAAC,CAAC;EAClD;EAEAf,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAACgE,WAAW,EAAElF,MAAM,IAAI,mCAAmC;EACxE;EAEAW,iBAAiBA,CAAA;IACfkL,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAAClH,MAAM,CAACmH,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEApJ,MAAMA,CAAA;IACJ,MAAMuF,QAAQ,GAAG,IAAI,CAAC5F,gBAAgB,EAAE;IACxC,IAAI4F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B2D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvC;MACA,IAAI,CAAChH,MAAM,CAACmH,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,EAAE;UACXC,SAAS,EAAEL,OAAO,CAAC3J,GAAG;UACtBiK,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACAlK,WAAWA,CAACiK,SAAiB;IAC3B;IACA,IAAI,CAACE,iBAAiB,CAACF,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAACrH,MAAM,CAACmH,QAAQ,CAAC,CAAC,eAAe,EAAEE,SAAS,CAAC,CAAC;EACpD;EAEAG,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAACzH,MAAM,CAACmH,QAAQ,CAAC,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACF,SAAiB,EAAEK,MAAc;IACzD;IACAT,OAAO,CAACC,GAAG,CAAC,iBAAiBQ,MAAM,WAAW,EAAEL,SAAS,CAAC;IAC1D;EACF;EAEApJ,aAAaA,CAAA;IACX,MAAMqF,QAAQ,GAAG,IAAI,CAAC5F,gBAAgB,EAAE;IACxC,IAAI4F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC;MAC3B2D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;MAE3C,IAAI,CAAC7G,eAAe,CAAClC,aAAa,CAAC+I,OAAO,CAAC3J,GAAG,CAAC,CAACsK,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDD,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEA5J,SAASA,CAAA;IACP,MAAMmF,QAAQ,GAAG,IAAI,CAAC5F,gBAAgB,EAAE;IACxC,IAAI4F,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMuE,OAAO,GAAG1D,QAAQ,CAAC,CAAC,CAAC;MAC3B2D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MAEvC,IAAI,CAAC9G,WAAW,CAAC/B,SAAS,CAAC6I,OAAO,CAAC3J,GAAG,EAAE,CAAC,EAAE6G,SAAS,EAAEA,SAAS,CAAC,CAACyD,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CD,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACA3L,cAAcA,CAACoJ,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACyC,aAAa,KAAK/D,SAAS,EAAE;MAC9C,IAAI,CAAC7C,iBAAiB,GAAGmE,KAAK,CAACyC,aAAa;MAE5C;MACAhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAAC7F,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAAC6G,oBAAoB,EAAE;;EAE/B;EAEAhM,aAAaA,CAACqK,MAAW;IACvB;IACA,IAAI,CAACpF,qBAAqB,GAAG,IAAI;IACjC8F,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQgB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAAC1L,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC6E,iBAAiB,CAAC,EAAE;MACxD,MAAM8G,YAAY,GAAG,IAAI,CAAC3L,OAAO,CAAC,IAAI,CAAC6E,iBAAiB,CAAC;MACzD4F,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,YAAY,CAAChN,IAAI,CAACO,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACA0M,cAAcA,CAAA;IACZ,IAAI,CAAChH,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACA6F,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAC9F,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;EAEA;EACQoB,eAAeA,CAAA;IACrB,MAAMwD,KAAK,GAAGK,MAAM,CAACC,UAAU;IAC/B,MAAM+B,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAACjI,QAAQ,GAAG4F,KAAK,IAAI,GAAG,IAAIuC,iBAAiB;EACnD;EAGAE,QAAQA,CAAA;IACN,IAAI,CAACjG,eAAe,EAAE;EACxB;;;uBAxkBW1C,uBAAuB,EAAApG,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtP,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBpJ,uBAAuB;MAAAqJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB5P,EAAA,CAAAW,UAAA,qBAAAmP,mDAAAvN,MAAA;YAAA,OAAAsN,GAAA,CAAAhE,aAAA,CAAAtJ,MAAA,CAAqB;UAAA,UAAAvC,EAAA,CAAA+P,iBAAA,CAAE,oBAAAC,kDAAAzN,MAAA;YAAA,OAAvBsN,GAAA,CAAAd,QAAA,CAAAxM,MAAA,CAAgB;UAAA,UAAAvC,EAAA,CAAAiQ,eAAA,CAAO;;;;;;;;;;;;;;;;;;UC/ChCjQ,EAHJ,CAAAC,cAAA,aAA+B,aAED,YACA;UAAAD,EAAA,CAAAU,MAAA,cAAO;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,2CAAoC;UAClEV,EADkE,CAAAG,YAAA,EAAI,EAChE;UAWNH,EARA,CAAAI,UAAA,IAAA8P,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC;UAqDzDnQ,EAAA,CAAAG,YAAA,EAAM;UAgINH,EA7HA,CAAAI,UAAA,IAAAgQ,sCAAA,mBAAqE,IAAAC,sCAAA,iBA6HxB;;;UA7LrCrQ,EAAA,CAAAM,SAAA,GAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAsP,GAAA,CAAA/I,gBAAA,CAAsB;UAQE9G,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAAsP,GAAA,CAAA/I,gBAAA,CAAuB;UAwDA9G,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAsP,GAAA,CAAAjK,MAAA,CAAY;UA6HpC5F,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAsP,GAAA,CAAAjK,MAAA,CAAY;;;qBDvJ/B9F,YAAY,EAAAwQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEzQ,cAAc,EAAA0Q,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}