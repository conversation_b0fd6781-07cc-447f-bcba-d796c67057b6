{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nlet SuggestedForYouComponent = class SuggestedForYouComponent {\n  constructor(router) {\n    this.router = router;\n    this.suggestedUsers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each user card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 5000; // 5 seconds for users\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 198;\n    this.sectionComments = 67;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadSuggestedUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for suggested users\n        _this.suggestedUsers = [{\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        }, {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        }, {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        }, {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        }, {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        }, {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnUsersLoad();\n      } catch (error) {\n        console.error('Error loading suggested users:', error);\n        _this.error = 'Failed to load suggested users';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onUserClick(user) {\n    this.router.navigate(['/profile', user.username]);\n  }\n  onFollowUser(user, event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n  trackByUserId(index, user) {\n    return user.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when users load\n  updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for suggested users section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Suggested for You',\n        text: 'Discover amazing fashion creators!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for suggested users');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n};\nSuggestedForYouComponent = __decorate([Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})], SuggestedForYouComponent);\nexport { SuggestedForYouComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Subscription", "IonicModule", "SuggestedForYouComponent", "constructor", "router", "suggestedUsers", "isLoading", "error", "subscription", "currentSlide", "slideOffset", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "maxSlide", "autoSlideDelay", "isAutoSliding", "isPaused", "isSectionLiked", "isSectionBookmarked", "sectionLikes", "sectionComments", "isMobile", "ngOnInit", "loadSuggestedUsers", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "username", "fullName", "avatar", "<PERSON><PERSON><PERSON>", "isFollowing", "isInfluencer", "followerCount", "category", "updateSliderOnUsersLoad", "console", "onUserClick", "user", "navigate", "onFollowUser", "event", "stopPropagation", "formatFollowerCount", "count", "toFixed", "toString", "onRetry", "trackByUserId", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "pauseAutoSlide", "resumeAutoSlide", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "slidePrev", "restartAutoSlideAfterInteraction", "slideNext", "setTimeout", "toggleSectionLike", "toggleSectionBookmark", "openComments", "log", "shareSection", "navigator", "share", "title", "text", "url", "location", "href", "clipboard", "writeText", "openMusicPlayer", "formatCount", "__decorate", "selector", "standalone", "imports", "CarouselModule", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\n\ninterface SuggestedUser {\n  _id?: string;\n  id?: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followedBy?: string;\n  bio?: string;\n  isFollowing: boolean;\n  isInfluencer?: boolean;\n  isVerified?: boolean;\n  followerCount?: number;\n  followers?: number;\n  following?: number;\n  posts?: number;\n  mutualFollowers?: number;\n  category?: string;\n}\n\n@Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})\nexport class SuggestedForYouComponent implements OnInit, OnDestroy {\n  suggestedUsers: SuggestedUser[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each user card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 5000; // 5 seconds for users\n  isAutoSliding = true;\n  isPaused = false;\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 198;\n  sectionComments = 67;\n  isMobile = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadSuggestedUsers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for suggested users\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        },\n        {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        },\n        {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        },\n        {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        },\n        {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        },\n        {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnUsersLoad();\n    } catch (error) {\n      console.error('Error loading suggested users:', error);\n      this.error = 'Failed to load suggested users';\n      this.isLoading = false;\n    }\n  }\n\n  onUserClick(user: SuggestedUser) {\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  onFollowUser(user: SuggestedUser, event: Event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    \n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n\n  trackByUserId(index: number, user: SuggestedUser): string {\n    return user.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when users load\n  private updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for suggested users section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Suggested for You',\n        text: 'Discover amazing fashion creators!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for suggested users');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n\n\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AA4BrC,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EA0BnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAzB1B,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IACnB,KAAAC,YAAY,GAAiB,IAAIR,YAAY,EAAE;IAEvD;IACA,KAAAS,YAAY,GAAG,CAAC;IAChB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,QAAQ,GAAG,CAAC;IAIZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnB,YAAY,CAACoB,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcN,kBAAkBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACxB,SAAS,GAAG,IAAI;QACrBwB,KAAI,CAACvB,KAAK,GAAG,IAAI;QAEjB;QACAuB,KAAI,CAACzB,cAAc,GAAG,CACpB;UACE2B,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,YAAY;UACtBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,oCAAoC;UAChDC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,gBAAgB;UAC1BC,QAAQ,EAAE,WAAW;UACrBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,aAAa;UACvBC,QAAQ,EAAE,cAAc;UACxBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,KAAK;UACnBC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,cAAc;UACxBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE,0FAA0F;UAClGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,mBAAmB;UAC7BC,QAAQ,EAAE,WAAW;UACrBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,KAAK;UACnBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,YAAY;UACtBC,QAAQ,EAAE,oBAAoB;UAC9BC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,CACF;QAEDV,KAAI,CAACxB,SAAS,GAAG,KAAK;QACtBwB,KAAI,CAACW,uBAAuB,EAAE;OAC/B,CAAC,OAAOlC,KAAK,EAAE;QACdmC,OAAO,CAACnC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDuB,KAAI,CAACvB,KAAK,GAAG,gCAAgC;QAC7CuB,KAAI,CAACxB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAqC,WAAWA,CAACC,IAAmB;IAC7B,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,UAAU,EAAED,IAAI,CAACX,QAAQ,CAAC,CAAC;EACnD;EAEAa,YAAYA,CAACF,IAAmB,EAAEG,KAAY;IAC5CA,KAAK,CAACC,eAAe,EAAE;IACvBJ,IAAI,CAACP,WAAW,GAAG,CAACO,IAAI,CAACP,WAAW;IAEpC,IAAIO,IAAI,CAACP,WAAW,EAAE;MACpBO,IAAI,CAACL,aAAa,EAAE;KACrB,MAAM;MACLK,IAAI,CAACL,aAAa,EAAE;;EAExB;EAEAU,mBAAmBA,CAACC,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC9B,kBAAkB,EAAE;EAC3B;EAEA+B,aAAaA,CAACC,KAAa,EAAEX,IAAmB;IAC9C,OAAOA,IAAI,CAACZ,EAAE;EAChB;EAEA;EACQwB,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzC,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACa,aAAa,EAAE;IACpB,IAAI,CAAC4B,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAAC1C,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACsD,MAAM,GAAG,IAAI,CAAC/C,YAAY,EAAE;QACpE,IAAI,CAACgD,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC9C,cAAc,CAAC;EACzB;EAEQe,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC4B,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACnD,YAAY,IAAI,IAAI,CAACI,QAAQ,EAAE;MACtC,IAAI,CAACJ,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAACqD,iBAAiB,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC/C,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACa,aAAa,EAAE;EACtB;EAEAmC,eAAeA,CAAA;IACb,IAAI,CAAChD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACwC,cAAc,EAAE;EACvB;EAEA;EACQhC,wBAAwBA,CAAA;IAC9B,MAAMyC,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAACtD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIqD,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAACtD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIqD,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAACtD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACwD,kBAAkB,EAAE;IACzB,IAAI,CAACN,iBAAiB,EAAE;EAC1B;EAEQrC,mBAAmBA,CAAA;IACzByC,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC7C,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA4C,kBAAkBA,CAAA;IAChB,IAAI,CAACvD,QAAQ,GAAGyD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClE,cAAc,CAACsD,MAAM,GAAG,IAAI,CAAC/C,YAAY,CAAC;EAC7E;EAEA4D,SAASA,CAAA;IACP,IAAI,IAAI,CAAC/D,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACqD,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACjE,YAAY,GAAG,IAAI,CAACI,QAAQ,EAAE;MACrC,IAAI,CAACJ,YAAY,EAAE;MACnB,IAAI,CAACqD,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEQX,iBAAiBA,CAAA;IACvB,IAAI,CAACpD,WAAW,GAAG,CAAC,IAAI,CAACD,YAAY,GAAG,IAAI,CAACE,SAAS;EACxD;EAEQ8D,gCAAgCA,CAAA;IACtC,IAAI,CAAC5C,aAAa,EAAE;IACpB8C,UAAU,CAAC,MAAK;MACd,IAAI,CAACnB,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQf,uBAAuBA,CAAA;IAC7BkC,UAAU,CAAC,MAAK;MACd,IAAI,CAACP,kBAAkB,EAAE;MACzB,IAAI,CAAC3D,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC8C,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAoB,iBAAiBA,CAAA;IACf,IAAI,CAAC3D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACE,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEA0D,qBAAqBA,CAAA;IACnB,IAAI,CAAC3D,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA4D,YAAYA,CAAA;IACVpC,OAAO,CAACqC,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEAC,YAAYA,CAAA;IACV,IAAIC,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,oCAAoC;QAC1CC,GAAG,EAAEnB,MAAM,CAACoB,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACLN,SAAS,CAACO,SAAS,CAACC,SAAS,CAACvB,MAAM,CAACoB,QAAQ,CAACC,IAAI,CAAC;MACnD7C,OAAO,CAACqC,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAW,eAAeA,CAAA;IACbhD,OAAO,CAACqC,GAAG,CAAC,0CAA0C,CAAC;EACzD;EAEAY,WAAWA,CAACzC,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEQ1B,iBAAiBA,CAAA;IACvB,IAAI,CAACL,QAAQ,GAAG6C,MAAM,CAACC,UAAU,IAAI,GAAG;EAC1C;CAGD;AAvTYjE,wBAAwB,GAAA0F,UAAA,EAPpC9F,SAAS,CAAC;EACT+F,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChG,YAAY,EAAEE,WAAW,EAAE+F,cAAc,CAAC;EACpDC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACWhG,wBAAwB,CAuTpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}