{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction NewArrivalsComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 15);\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 18);\n    i0.ɵɵelementStart(7, \"span\", 16);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_6_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 20);\n    i0.ɵɵelementStart(11, \"span\", 21);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_6_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_6_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 24);\n    i0.ɵɵelementStart(17, \"span\", 21);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵelement(3, \"div\", 31)(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_17_div_2_Template, 6, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_18_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction NewArrivalsComponent_div_19_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r6), \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_19_div_7_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction NewArrivalsComponent_div_19_div_7_ion_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 15);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_19_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵelement(4, \"ion-icon\", 51);\n    i0.ɵɵtext(5, \" New \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_19_div_7_div_8_Template, 2, 1, \"div\", 53);\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_div_7_Template_button_click_10_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_div_7_Template_button_click_12_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(13, \"ion-icon\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 56)(15, \"div\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h3\", 58);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 59)(20, \"span\", 60);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_19_div_7_span_22_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 62)(24, \"div\", 63);\n    i0.ɵɵtemplate(25, NewArrivalsComponent_div_19_div_7_ion_icon_25_Template, 1, 3, \"ion-icon\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 65);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 66)(29, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_div_7_Template_button_click_29_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r6, $event));\n    });\n    i0.ɵɵelement(30, \"ion-icon\", 68);\n    i0.ɵɵtext(31, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_div_7_Template_button_click_32_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r6, $event));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 70);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.cardWidth, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDaysAgo(product_r6.createdAt), \" days ago \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r6) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(17, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_19_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"ion-icon\", 74);\n    i0.ɵɵelementStart(2, \"h3\", 75);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 76);\n    i0.ɵɵtext(5, \"Check back soon for fresh new styles\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"mouseenter\", function NewArrivalsComponent_div_19_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function NewArrivalsComponent_div_19_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevSlide());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_19_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextSlide());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"div\", 44);\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_19_div_7_Template, 34, 18, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_19_div_8_Template, 6, 0, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoPrev);\n    i0.ɵɵattribute(\"aria-label\", \"Previous products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoNext);\n    i0.ɵɵattribute(\"aria-label\", \"Next products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(-\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && !ctx_r1.error && ctx_r1.newArrivals.length === 0);\n  }\n}\nexport class NewArrivalsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.newArrivals = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280;\n    this.visibleCards = 4;\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 421;\n    this.sectionComments = 156;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n    this.checkMobileDevice();\n    // Add fallback mock data if no data loads after 3 seconds\n    setTimeout(() => {\n      if (this.newArrivals.length === 0 && !this.isLoading && !this.error) {\n        console.log('NewArrivals: Adding mock data as fallback');\n        this.addMockData();\n      }\n    }, 3000);\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeNewArrivals() {\n    this.subscription.add(this.trendingService.newArrivals$.subscribe(products => {\n      this.newArrivals = products;\n      this.isLoading = false;\n      this.calculateMaxSlide();\n      this.currentSlide = 0;\n      this.updateSlidePosition();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadNewArrivals() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadNewArrivals(1, 6);\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n        _this.error = 'Failed to load new arrivals';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  getDaysAgo(createdAt) {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n  onRetry() {\n    this.loadNewArrivals();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'new-arrivals'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider methods\n  initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n  updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n  calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n  updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n  startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n  get canGoPrev() {\n    return this.currentSlide > 0;\n  }\n  get canGoNext() {\n    return this.currentSlide < this.maxSlide;\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for new arrivals section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'New Arrivals',\n        text: 'Check out these fresh new fashion arrivals!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for new arrivals');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function NewArrivalsComponent_Factory(t) {\n      return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewArrivalsComponent,\n      selectors: [[\"app-new-arrivals\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 8,\n      consts: [[1, \"new-arrivals-container\"], [2, \"background\", \"#e3f2fd\", \"padding\", \"8px\", \"margin-bottom\", \"10px\", \"font-size\", \"11px\", \"border-radius\", \"4px\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"sparkles\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"width\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [\"name\", \"sparkles\"], [1, \"days-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"sparkles-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function NewArrivalsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"strong\");\n          i0.ɵɵtext(3, \"New Arrivals Component\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"br\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, NewArrivalsComponent_div_6_Template, 19, 8, \"div\", 2);\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"div\", 4)(9, \"h2\", 5);\n          i0.ɵɵelement(10, \"ion-icon\", 6);\n          i0.ɵɵtext(11, \" New Arrivals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 7);\n          i0.ɵɵtext(13, \"Fresh styles just landed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_14_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(15, \" View All \");\n          i0.ɵɵelement(16, \"ion-icon\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(17, NewArrivalsComponent_div_17_Template, 3, 2, \"div\", 10)(18, NewArrivalsComponent_div_18_Template, 7, 1, \"div\", 11)(19, NewArrivalsComponent_div_19_Template, 9, 9, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate4(\" Loading: \", ctx.isLoading, \", Error: \", ctx.error, \", Products: \", ctx.newArrivals.length, \", Mobile: \", ctx.isMobile, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n      styles: [\".new-arrivals-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 10px;\\n}\\n\\n.products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50px;\\n  left: 12px;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.15);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .new-arrivals-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 5px;\\n  }\\n  .products-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 220px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "NewArrivalsComponent_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "NewArrivalsComponent_div_6_Template_button_click_5_listener", "openComments", "NewArrivalsComponent_div_6_Template_button_click_9_listener", "shareSection", "NewArrivalsComponent_div_6_Template_button_click_13_listener", "toggleSectionBookmark", "NewArrivalsComponent_div_6_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "NewArrivalsComponent_div_17_div_2_Template", "ɵɵpureFunction0", "_c0", "NewArrivalsComponent_div_18_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r6", "formatPrice", "originalPrice", "star_r7", "rating", "average", "NewArrivalsComponent_div_19_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onProductClick", "NewArrivalsComponent_div_19_div_7_div_8_Template", "NewArrivalsComponent_div_19_div_7_Template_button_click_10_listener", "$event", "onLikeProduct", "NewArrivalsComponent_div_19_div_7_Template_button_click_12_listener", "onShareProduct", "NewArrivalsComponent_div_19_div_7_span_22_Template", "NewArrivalsComponent_div_19_div_7_ion_icon_25_Template", "NewArrivalsComponent_div_19_div_7_Template_button_click_29_listener", "onAddToCart", "NewArrivalsComponent_div_19_div_7_Template_button_click_32_listener", "onAddToWishlist", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON>", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "getDaysAgo", "createdAt", "isProductLiked", "_id", "brand", "price", "_c1", "count", "NewArrivalsComponent_div_19_Template_div_mouseenter_0_listener", "_r4", "pauseAutoSlide", "NewArrivalsComponent_div_19_Template_div_mouseleave_0_listener", "resumeAutoSlide", "NewArrivalsComponent_div_19_Template_button_click_1_listener", "prevSlide", "NewArrivalsComponent_div_19_Template_button_click_3_listener", "nextSlide", "NewArrivalsComponent_div_19_div_7_Template", "NewArrivalsComponent_div_19_div_8_Template", "canGoPrev", "canGoNext", "slideOffset", "newArrivals", "trackByProductId", "isLoading", "length", "NewArrivalsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "likedProducts", "Set", "subscription", "currentSlide", "visibleCards", "maxSlide", "autoSlideDelay", "isMobile", "ngOnInit", "loadNewArrivals", "subscribeNewArrivals", "subscribeLikedProducts", "initializeSlider", "startAutoSlide", "checkMobileDevice", "setTimeout", "console", "log", "addMockData", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "newArrivals$", "subscribe", "products", "calculateMaxSlide", "updateSlidePosition", "likedProducts$", "_this", "_asyncToGenerator", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "now", "Date", "created", "diffTime", "abs", "getTime", "diffDays", "ceil", "onViewAll", "queryParams", "filter", "productId", "has", "index", "updateResponsiveSettings", "addEventListener", "containerWidth", "innerWidth", "max", "autoSlideInterval", "setInterval", "clearInterval", "share", "title", "text", "href", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NewArrivalsComponent_Template", "rf", "ctx", "NewArrivalsComponent_div_6_Template", "NewArrivalsComponent_Template_button_click_14_listener", "NewArrivalsComponent_div_17_Template", "NewArrivalsComponent_div_18_Template", "NewArrivalsComponent_div_19_Template", "ɵɵtextInterpolate4", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-new-arrivals',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './new-arrivals.component.html',\n  styleUrls: ['./new-arrivals.component.scss']\n})\nexport class NewArrivalsComponent implements OnInit, OnDestroy {\n  newArrivals: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 280;\n  visibleCards = 4;\n  maxSlide = 0;\n  autoSlideInterval: any;\n  autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 421;\n  sectionComments = 156;\n  isMobile = false;\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n    this.checkMobileDevice();\n\n    // Add fallback mock data if no data loads after 3 seconds\n    setTimeout(() => {\n      if (this.newArrivals.length === 0 && !this.isLoading && !this.error) {\n        console.log('NewArrivals: Adding mock data as fallback');\n        this.addMockData();\n      }\n    }, 3000);\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private subscribeNewArrivals() {\n    this.subscription.add(\n      this.trendingService.newArrivals$.subscribe(products => {\n        this.newArrivals = products;\n        this.isLoading = false;\n        this.calculateMaxSlide();\n        this.currentSlide = 0;\n        this.updateSlidePosition();\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadNewArrivals() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadNewArrivals(1, 6);\n    } catch (error) {\n      console.error('Error loading new arrivals:', error);\n      this.error = 'Failed to load new arrivals';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  getDaysAgo(createdAt: Date): number {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n\n  onRetry() {\n    this.loadNewArrivals();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], { \n      queryParams: { filter: 'new-arrivals' } \n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Slider methods\n  private initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n\n  private updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n\n  private calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n\n  private updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n\n  private startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n\n  get canGoPrev(): boolean {\n    return this.currentSlide > 0;\n  }\n\n  get canGoNext(): boolean {\n    return this.currentSlide < this.maxSlide;\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for new arrivals section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'New Arrivals',\n        text: 'Check out these fresh new fashion arrivals!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for new arrivals');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n}\n", "<div class=\"new-arrivals-container\">\n  <!-- Debug: Component Status -->\n  <div style=\"background: #e3f2fd; padding: 8px; margin-bottom: 10px; font-size: 11px; border-radius: 4px;\">\n    <strong>New Arrivals Component</strong><br>\n    Loading: {{ isLoading }}, Error: {{ error }}, Products: {{ newArrivals.length }}, Mobile: {{ isMobile }}\n  </div>\n\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"sparkles\" class=\"title-icon\"></ion-icon>\n        New Arrivals\n      </h2>\n      <p class=\"section-subtitle\">Fresh styles just landed</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length > 0\" class=\"products-slider-container\"\n       (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n\n    <!-- Navigation Buttons -->\n    <button class=\"nav-btn prev-btn\"\n            [disabled]=\"!canGoPrev\"\n            (click)=\"prevSlide()\"\n            [attr.aria-label]=\"'Previous products'\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n\n    <button class=\"nav-btn next-btn\"\n            [disabled]=\"!canGoNext\"\n            (click)=\"nextSlide()\"\n            [attr.aria-label]=\"'Next products'\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <div class=\"products-slider-wrapper\">\n      <div class=\"products-slider\"\n           [style.transform]=\"'translateX(-' + slideOffset + 'px)'\">\n        <div\n          *ngFor=\"let product of newArrivals; trackBy: trackByProductId\"\n          class=\"product-card\"\n          [style.width.px]=\"cardWidth\"\n          (click)=\"onProductClick(product)\"\n        >\n      <!-- Product Image -->\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n        <!-- New Badge -->\n        <div class=\"new-badge\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n          New\n        </div>\n\n        <!-- Days Badge -->\n        <div class=\"days-badge\">\n          {{ getDaysAgo(product.createdAt) }} days ago\n        </div>\n\n        <!-- Discount Badge -->\n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n        <!-- Price Section -->\n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n        <!-- Rating -->\n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"sparkles-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No New Arrivals</h3>\n    <p class=\"empty-message\">Check back soon for fresh new styles</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;;ICD/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,4DAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,4DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,6DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,6DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA2BxE7B,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAU,SAAA,cAAiC;IACjCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,0CAAA,kBAA6D;IASjE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAY1CjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,6DAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAwDhCrC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IADJZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BExC,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IApEvE7C,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,gEAAA;MAAA,MAAAN,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2C,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IAGrCxC,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAGFV,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAU,SAAA,mBAAqC;IACrCV,EAAA,CAAAW,MAAA,YACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAoB,gDAAA,kBAAuE;IAMrElD,EADF,CAAAC,cAAA,cAA4B,kBAMzB;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,oEAAAC,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAb,UAAA,EAAAY,MAAA,CAA8B;IAAA,EAAC;IAGxCpD,EAAA,CAAAU,SAAA,oBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAoD,oEAAAF,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAf,UAAA,EAAAY,MAAA,CAA+B;IAAA,EAAC;IAGzCpD,EAAA,CAAAU,SAAA,oBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACpDZ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAI9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAA0B,kDAAA,mBAC6B;IAC/BxD,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA2B,sDAAA,uBAIC;IACHzD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IACxDX,EADwD,CAAAY,YAAA,EAAO,EACzD;IAIJZ,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAE,UAAA,mBAAAwD,oEAAAN,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,WAAA,CAAAnB,UAAA,EAAAY,MAAA,CAA4B;IAAA,EAAC;IAEtCpD,EAAA,CAAAU,SAAA,oBAA4C;IAC5CV,EAAA,CAAAW,MAAA,qBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAE,UAAA,mBAAA0D,oEAAAR,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,eAAA,CAAArB,UAAA,EAAAY,MAAA,CAAgC;IAAA,EAAC;IAE1CpD,EAAA,CAAAU,SAAA,oBAA0C;IAIlDV,EAHM,CAAAY,YAAA,EAAS,EACL,EACF,EACF;;;;;IAzFAZ,EAAA,CAAA8D,WAAA,UAAAxD,MAAA,CAAAyD,SAAA,OAA4B;IAM5B/D,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAgB,UAAA,CAAAwB,MAAA,IAAAC,GAAA,EAAAjE,EAAA,CAAAkE,aAAA,CAA6B,QAAA1B,UAAA,CAAAwB,MAAA,IAAAG,GAAA,IAAA3B,UAAA,CAAA4B,IAAA,CACgB;IAa7CpE,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAA+D,UAAA,CAAA7B,UAAA,CAAA8B,SAAA,gBACF;IAGMtE,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CxC,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,EAA2C;;IAIjCxE,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,8BAAgE;IAK1ExE,EAAA,CAAAqB,SAAA,EAA2C;;IASpBrB,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAAiC,KAAA,CAAmB;IACrBzE,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAA4B,IAAA,CAAkB;IAIbpE,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAkC,KAAA,EAAgC;IACrD1E,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAAkC,KAAA,CAAoE;IAQtD1E,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAA2C,GAAA,EAAc;IAKT3E,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAsC,kBAAA,MAAAE,UAAA,CAAAI,MAAA,CAAAgC,KAAA,MAA4B;;;;;IAwB9D5E,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAU,SAAA,mBAAgE;IAChEV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,2CAAoC;IAC/DX,EAD+D,CAAAY,YAAA,EAAI,EAC7D;;;;;;IAzHNZ,EAAA,CAAAC,cAAA,cACsE;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA2E,+DAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAyE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,+DAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2E,eAAA,EAAiB;IAAA,EAAC;IAGnEjF,EAAA,CAAAC,cAAA,iBAGgD;IADxCD,EAAA,CAAAE,UAAA,mBAAAgF,6DAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6E,SAAA,EAAW;IAAA,EAAC;IAE3BnF,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,iBAG4C;IADpCD,EAAA,CAAAE,UAAA,mBAAAkF,6DAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,SAAA,EAAW;IAAA,EAAC;IAE3BrF,EAAA,CAAAU,SAAA,kBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGPZ,EADF,CAAAC,cAAA,cAAqC,cAE2B;IAC5DD,EAAA,CAAA8B,UAAA,IAAAwD,0CAAA,oBAKC;IAwFPtF,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAyD,0CAAA,kBAAsF;IAKxFvF,EAAA,CAAAY,YAAA,EAAM,EAzHkE;;;;IAI5DZ,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAkF,SAAA,CAAuB;;IAOvBxF,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAmF,SAAA,CAAuB;;IAQxBzF,EAAA,CAAAqB,SAAA,GAAwD;IAAxDrB,EAAA,CAAA8D,WAAA,+BAAAxD,MAAA,CAAAoF,WAAA,SAAwD;IAErC1F,EAAA,CAAAqB,SAAA,EAAgB;IAAArB,EAAhB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAqF,WAAA,CAAgB,iBAAArF,MAAA,CAAAsF,gBAAA,CAAyB;IA+F/D5F,EAAA,CAAAqB,SAAA,EAAsD;IAAtDrB,EAAA,CAAAwB,UAAA,UAAAlB,MAAA,CAAAuF,SAAA,KAAAvF,MAAA,CAAA+B,KAAA,IAAA/B,MAAA,CAAAqF,WAAA,CAAAG,MAAA,OAAsD;;;ADhL9D,OAAM,MAAOC,oBAAoB;EAuB/BC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAAV,WAAW,GAAc,EAAE;IAC3B,KAAAE,SAAS,GAAG,IAAI;IAChB,KAAAxD,KAAK,GAAkB,IAAI;IAC3B,KAAAiE,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3G,YAAY,EAAE;IAEvD;IACA,KAAA4G,YAAY,GAAG,CAAC;IAChB,KAAAf,WAAW,GAAG,CAAC;IACf,KAAA3B,SAAS,GAAG,GAAG;IACf,KAAA2C,YAAY,GAAG,CAAC;IAChB,KAAAC,QAAQ,GAAG,CAAC;IAEZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IAEvB;IACA,KAAArF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAAiF,QAAQ,GAAG,KAAK;EAQb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC1B,WAAW,CAACG,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAACD,SAAS,IAAI,CAAC,IAAI,CAACxD,KAAK,EAAE;QACnEiF,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QACxD,IAAI,CAACC,WAAW,EAAE;;IAEtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,YAAY,CAACkB,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQX,oBAAoBA,CAAA;IAC1B,IAAI,CAACR,YAAY,CAACoB,GAAG,CACnB,IAAI,CAAC3B,eAAe,CAAC4B,YAAY,CAACC,SAAS,CAACC,QAAQ,IAAG;MACrD,IAAI,CAACpC,WAAW,GAAGoC,QAAQ;MAC3B,IAAI,CAAClC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACmC,iBAAiB,EAAE;MACxB,IAAI,CAACvB,YAAY,GAAG,CAAC;MACrB,IAAI,CAACwB,mBAAmB,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQhB,sBAAsBA,CAAA;IAC5B,IAAI,CAACT,YAAY,CAACoB,GAAG,CACnB,IAAI,CAAC1B,aAAa,CAACgC,cAAc,CAACJ,SAAS,CAACxB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcS,eAAeA,CAAA;IAAA,IAAAoB,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI;QACFD,KAAI,CAACtC,SAAS,GAAG,IAAI;QACrBsC,KAAI,CAAC9F,KAAK,GAAG,IAAI;QACjB,MAAM8F,KAAI,CAAClC,eAAe,CAACc,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;OACjD,CAAC,OAAO1E,KAAK,EAAE;QACdiF,OAAO,CAACjF,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD8F,KAAI,CAAC9F,KAAK,GAAG,6BAA6B;QAC1C8F,KAAI,CAACtC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA5C,cAAcA,CAACoF,OAAgB;IAC7B,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC7D,GAAG,CAAC,CAAC;EACjD;EAEMnB,aAAaA,CAACgF,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAJ,iBAAA;MAChDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAACtC,aAAa,CAACyC,WAAW,CAACN,OAAO,CAAC7D,GAAG,CAAC;QAChE,IAAIkE,MAAM,CAACE,OAAO,EAAE;UAClBtB,OAAO,CAACC,GAAG,CAACmB,MAAM,CAACG,OAAO,CAAC;SAC5B,MAAM;UACLvB,OAAO,CAACjF,KAAK,CAAC,yBAAyB,EAAEqG,MAAM,CAACG,OAAO,CAAC;;OAE3D,CAAC,OAAOxG,KAAK,EAAE;QACdiF,OAAO,CAACjF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMkB,cAAcA,CAAC8E,OAAgB,EAAEE,KAAY;IAAA,IAAAO,MAAA;IAAA,OAAAV,iBAAA;MACjDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMM,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAAC7D,GAAG,EAAE;QACrE,MAAM2E,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC5C,aAAa,CAACoD,YAAY,CAACjB,OAAO,CAAC7D,GAAG,EAAE;UACjD+E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,iCAAiCR,OAAO,CAACjE,IAAI,SAASiE,OAAO,CAAC5D,KAAK;SAC7E,CAAC;QAEF6C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOlF,KAAK,EAAE;QACdiF,OAAO,CAACjF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMsB,WAAWA,CAAC0E,OAAgB,EAAEE,KAAY;IAAA,IAAAiB,MAAA;IAAA,OAAApB,iBAAA;MAC9CG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMe,MAAI,CAACrD,WAAW,CAACsD,SAAS,CAACpB,OAAO,CAAC7D,GAAG,EAAE,CAAC,CAAC;QAChD8C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOlF,KAAK,EAAE;QACdiF,OAAO,CAACjF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMwB,eAAeA,CAACwE,OAAgB,EAAEE,KAAY;IAAA,IAAAmB,MAAA;IAAA,OAAAtB,iBAAA;MAClDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMiB,MAAI,CAACtD,eAAe,CAACuD,aAAa,CAACtB,OAAO,CAAC7D,GAAG,CAAC;QACrD8C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOlF,KAAK,EAAE;QACdiF,OAAO,CAACjF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC8F,OAAgB;IACpC,IAAIA,OAAO,CAAC3F,aAAa,IAAI2F,OAAO,CAAC3F,aAAa,GAAG2F,OAAO,CAAC3D,KAAK,EAAE;MAClE,OAAOkF,IAAI,CAACC,KAAK,CAAE,CAACxB,OAAO,CAAC3F,aAAa,GAAG2F,OAAO,CAAC3D,KAAK,IAAI2D,OAAO,CAAC3F,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAACiC,KAAa;IACvB,OAAO,IAAIoF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACzF,KAAK,CAAC;EAClB;EAEAL,UAAUA,CAACC,SAAe;IACxB,MAAM8F,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAAC/F,SAAS,CAAC;IACnC,MAAMiG,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,OAAO,CAACG,OAAO,EAAE,CAAC;IAC5D,MAAMC,QAAQ,GAAGd,IAAI,CAACe,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB;EAEAtI,OAAOA,CAAA;IACL,IAAI,CAAC2E,eAAe,EAAE;EACxB;EAEA6D,SAASA,CAAA;IACP,IAAI,CAACvE,MAAM,CAACiC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCuC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAc;KACtC,CAAC;EACJ;EAEAvG,cAAcA,CAACwG,SAAiB;IAC9B,OAAO,IAAI,CAACzE,aAAa,CAAC0E,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAnF,gBAAgBA,CAACqF,KAAa,EAAE5C,OAAgB;IAC9C,OAAOA,OAAO,CAAC7D,GAAG;EACpB;EAEA;EACQ0C,gBAAgBA,CAAA;IACtB,IAAI,CAACgE,wBAAwB,EAAE;IAC/B,IAAI,CAAClD,iBAAiB,EAAE;IACxBgB,MAAM,CAACmC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAACD,wBAAwB,EAAE,CAAC;EAC1E;EAEQA,wBAAwBA,CAAA;IAC9B,MAAME,cAAc,GAAGpC,MAAM,CAACqC,UAAU;IAExC,IAAID,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAC1E,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIqH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAAC1E,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIqH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAAC1E,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAAC2C,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;;IAGtB,IAAI,CAACiE,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACrB,QAAQ,GAAGiD,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3F,WAAW,CAACG,MAAM,GAAG,IAAI,CAACY,YAAY,CAAC;EAC1E;EAEQuB,mBAAmBA,CAAA;IACzB,IAAI,CAACvC,WAAW,GAAG,IAAI,CAACe,YAAY,IAAI,IAAI,CAAC1C,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;EAChE;EAEAsB,SAASA,CAAA;IACP,IAAI,IAAI,CAACoB,YAAY,GAAG,IAAI,CAACE,QAAQ,EAAE;MACrC,IAAI,CAACF,YAAY,EAAE;MACnB,IAAI,CAACwB,mBAAmB,EAAE;;EAE9B;EAEA9C,SAASA,CAAA;IACP,IAAI,IAAI,CAACsB,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACwB,mBAAmB,EAAE;;EAE9B;EAEQd,cAAcA,CAAA;IACpB,IAAI,CAACoE,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC/E,YAAY,IAAI,IAAI,CAACE,QAAQ,EAAE;QACtC,IAAI,CAACF,YAAY,GAAG,CAAC;OACtB,MAAM;QACL,IAAI,CAACA,YAAY,EAAE;;MAErB,IAAI,CAACwB,mBAAmB,EAAE;IAC5B,CAAC,EAAE,IAAI,CAACrB,cAAc,CAAC;EACzB;EAEQe,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC4D,iBAAiB,EAAE;MAC1BE,aAAa,CAAC,IAAI,CAACF,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAxG,cAAcA,CAAA;IACZ,IAAI,CAAC4C,aAAa,EAAE;EACtB;EAEA1C,eAAeA,CAAA;IACb,IAAI,CAACkC,cAAc,EAAE;EACvB;EAEA,IAAI3B,SAASA,CAAA;IACX,OAAO,IAAI,CAACiB,YAAY,GAAG,CAAC;EAC9B;EAEA,IAAIhB,SAASA,CAAA;IACX,OAAO,IAAI,CAACgB,YAAY,GAAG,IAAI,CAACE,QAAQ;EAC1C;EAEA;EACAlG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACVwG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;EAEAvG,YAAYA,CAAA;IACV,IAAImI,SAAS,CAACuC,KAAK,EAAE;MACnBvC,SAAS,CAACuC,KAAK,CAAC;QACdC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,6CAA6C;QACnD3H,GAAG,EAAE+E,MAAM,CAACC,QAAQ,CAAC4C;OACtB,CAAC;KACH,MAAM;MACL1C,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAAC4C,IAAI,CAAC;MACnDvE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAnG,eAAeA,CAAA;IACbkG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACtD;EAEA7F,WAAWA,CAACkD,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEkH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAIlH,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEkH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOlH,KAAK,CAACmH,QAAQ,EAAE;EACzB;EAEQ3E,iBAAiBA,CAAA;IACvB,IAAI,CAACP,QAAQ,GAAGmC,MAAM,CAACqC,UAAU,IAAI,GAAG;EAC1C;;;uBAvTWtF,oBAAoB,EAAA/F,EAAA,CAAAgM,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAlM,EAAA,CAAAgM,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAApM,EAAA,CAAAgM,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtM,EAAA,CAAAgM,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAxM,EAAA,CAAAgM,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB3G,oBAAoB;MAAA4G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7M,EAAA,CAAA8M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB7BpN,EAHJ,CAAAC,cAAA,aAAoC,aAEwE,aAChG;UAAAD,EAAA,CAAAW,MAAA,6BAAsB;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAAAZ,EAAA,CAAAU,SAAA,SAAI;UAC3CV,EAAA,CAAAW,MAAA,GACF;UAAAX,EAAA,CAAAY,YAAA,EAAM;UAGNZ,EAAA,CAAA8B,UAAA,IAAAwL,mCAAA,kBAAoD;UAiChDtN,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,mBAAwD;UACxDV,EAAA,CAAAW,MAAA,sBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,YAA4B;UAAAD,EAAA,CAAAW,MAAA,gCAAwB;UACtDX,EADsD,CAAAY,YAAA,EAAI,EACpD;UACNZ,EAAA,CAAAC,cAAA,iBAAmD;UAAtBD,EAAA,CAAAE,UAAA,mBAAAqN,uDAAA;YAAA,OAASF,GAAA,CAAAzC,SAAA,EAAW;UAAA,EAAC;UAChD5K,EAAA,CAAAW,MAAA,kBACA;UAAAX,EAAA,CAAAU,SAAA,mBAA4C;UAEhDV,EADE,CAAAY,YAAA,EAAS,EACL;UA2BNZ,EAxBA,CAAA8B,UAAA,KAAA0L,oCAAA,kBAAiD,KAAAC,oCAAA,kBAcQ,KAAAC,oCAAA,kBAWa;UA/ExE1N,EAAA,CAAAY,YAAA,EAAoC;;;UAIhCZ,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAA2N,kBAAA,eAAAN,GAAA,CAAAxH,SAAA,eAAAwH,GAAA,CAAAhL,KAAA,kBAAAgL,GAAA,CAAA1H,WAAA,CAAAG,MAAA,gBAAAuH,GAAA,CAAAxG,QAAA,MACF;UAGoC7G,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA6L,GAAA,CAAAxG,QAAA,CAAc;UA8C5C7G,EAAA,CAAAqB,SAAA,IAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA6L,GAAA,CAAAxH,SAAA,CAAe;UAcf7F,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA6L,GAAA,CAAAhL,KAAA,KAAAgL,GAAA,CAAAxH,SAAA,CAAyB;UAUzB7F,EAAA,CAAAqB,SAAA,EAAoD;UAApDrB,EAAA,CAAAwB,UAAA,UAAA6L,GAAA,CAAAxH,SAAA,KAAAwH,GAAA,CAAAhL,KAAA,IAAAgL,GAAA,CAAA1H,WAAA,CAAAG,MAAA,KAAoD;;;qBD/DhDlG,YAAY,EAAAgO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhO,WAAW,EAAAiO,EAAA,CAAAC,OAAA,EAAEjO,cAAc;MAAAkO,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}