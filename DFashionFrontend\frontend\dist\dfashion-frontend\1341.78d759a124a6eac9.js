"use strict";(self.webpackChunkdfashion_frontend=self.webpackChunkdfashion_frontend||[]).push([[1341],{1341:(nt,E,g)=>{g.d(E,{V:()=>Z});var e=g(4438),w=g(177),y=g(1708),C=g(2168),z=g(1626),L=g(5494),V=g(7935);const v=["storiesContainer"],A=["feedCover"],P=["storiesSlider"],R=()=>[1,2,3,4,5];function I(u,x){1&u&&(e.j41(0,"div",8)(1,"h3",9),e.EFF(2,"Stories"),e.k0s(),e.j41(3,"p",10),e.EFF(4,"Watch stories from people you follow"),e.k0s()())}function $(u,x){1&u&&(e.j41(0,"div",13),e.nrm(1,"div",14)(2,"div",15),e.k0s())}function S(u,x){1&u&&(e.j41(0,"div",11),e.DNE(1,$,3,0,"div",12),e.k0s()),2&u&&(e.R7$(),e.Y8G("ngForOf",e.lJ4(1,R)))}function O(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",30),e.bIt("click",function(){e.eBV(o);const d=e.XpG().index,f=e.XpG(2);return e.Njj(f.openStories(d))}),e.j41(1,"div",19),e.nrm(2,"div",31)(3,"div",32),e.k0s(),e.j41(4,"div",24),e.EFF(5),e.k0s()()}if(2&u){const o=e.XpG().$implicit;e.R7$(2),e.xc7("background-image","url("+o.user.avatar+")"),e.R7$(),e.AVh("viewed",o.isViewed),e.R7$(2),e.JRh(o.user.username)}}function M(u,x){1&u&&(e.qex(0),e.DNE(1,O,6,5,"ng-template",29),e.bVm())}function F(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",16)(1,"div",17)(2,"div",18),e.bIt("click",function(){e.eBV(o);const d=e.XpG();return e.Njj(d.openAddStoryModal())}),e.j41(3,"div",19)(4,"div",20)(5,"div",21),e.nrm(6,"i",22),e.k0s(),e.nrm(7,"div",23),e.k0s()(),e.j41(8,"div",24),e.EFF(9,"Add Story"),e.k0s()()(),e.j41(10,"div",25)(11,"div",26)(12,"owl-carousel-o",27),e.bIt("initialized",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onInitialized(d))})("changed",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onSlideChanged(d))}),e.DNE(13,M,2,0,"ng-container",28),e.k0s()()()()}if(2&u){const o=e.XpG();e.R7$(7),e.xc7("background-image","url("+o.getCurrentUserAvatar()+")"),e.R7$(5),e.Y8G("options",o.customOptions),e.R7$(),e.Y8G("ngForOf",o.stories)}}function N(u,x){if(1&u&&(e.j41(0,"div",64),e.nrm(1,"div",65),e.k0s()),2&u){const o=x.index,h=e.XpG(2);e.AVh("active",o===h.currentIndex)("completed",o<h.currentIndex)}}function U(u,x){if(1&u&&e.nrm(0,"video",66),2&u){const o=e.XpG(2);e.Y8G("src",o.getCurrentStory().mediaUrl,e.B4B)}}function G(u,x){if(1&u&&e.nrm(0,"div",67),2&u){const o=e.XpG(2);e.xc7("background-image","url("+o.getCurrentStory().mediaUrl+")")}}function j(u,x){if(1&u&&(e.j41(0,"div",68),e.EFF(1),e.k0s()),2&u){const o=e.XpG(2);e.R7$(),e.SpI(" ",o.getCurrentStory().caption," ")}}function D(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",71),e.bIt("click",function(){const d=e.eBV(o).$implicit,f=e.XpG(3);return e.Njj(f.viewProduct(d._id))}),e.j41(1,"div",72),e.EFF(2,"\u{1f6cd}\ufe0f"),e.k0s(),e.j41(3,"div",73)(4,"div",74),e.EFF(5),e.k0s(),e.j41(6,"div",75),e.EFF(7),e.k0s()()()}if(2&u){const o=x.$implicit,h=e.XpG(3);e.R7$(5),e.JRh(o.name),e.R7$(2),e.JRh(h.formatPrice(o.price))}}function X(u,x){if(1&u&&(e.j41(0,"div",69),e.DNE(1,D,8,2,"div",70),e.k0s()),2&u){const o=e.XpG(2);e.R7$(),e.Y8G("ngForOf",o.getStoryProducts())}}function Y(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",76)(1,"button",77),e.bIt("click",function(){e.eBV(o);const d=e.XpG(2);return e.Njj(d.viewProduct(d.getStoryProducts()[0]._id))}),e.nrm(2,"i",78),e.j41(3,"span"),e.EFF(4,"Shop Now"),e.k0s()()()}}function W(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",79)(1,"button",80),e.bIt("click",function(){e.eBV(o);const d=e.XpG(2);return e.Njj(d.buyNow())}),e.nrm(2,"i",81),e.j41(3,"span"),e.EFF(4,"Buy Now"),e.k0s()(),e.j41(5,"button",82),e.bIt("click",function(){e.eBV(o);const d=e.XpG(2);return e.Njj(d.addToWishlist())}),e.nrm(6,"i",55),e.j41(7,"span"),e.EFF(8,"Wishlist"),e.k0s()(),e.j41(9,"button",83),e.bIt("click",function(){e.eBV(o);const d=e.XpG(2);return e.Njj(d.addToCart())}),e.nrm(10,"i",22),e.j41(11,"span"),e.EFF(12,"Add to Cart"),e.k0s()()()}}function K(u,x){if(1&u){const o=e.RV6();e.j41(0,"div",33)(1,"div",34,0)(3,"div",35),e.DNE(4,N,2,4,"div",36),e.k0s(),e.j41(5,"div",37),e.bIt("click",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onStoryClick(d))})("touchstart",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onTouchStart(d))})("touchmove",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onTouchMove(d))})("touchend",function(d){e.eBV(o);const f=e.XpG();return e.Njj(f.onTouchEnd(d))}),e.j41(6,"div",38)(7,"div",39),e.nrm(8,"div",40),e.j41(9,"div",41),e.EFF(10),e.k0s(),e.j41(11,"div",42),e.EFF(12),e.k0s(),e.j41(13,"div",43),e.EFF(14),e.k0s()(),e.j41(15,"button",44),e.bIt("click",function(){e.eBV(o);const d=e.XpG();return e.Njj(d.closeStories())}),e.nrm(16,"i",45),e.k0s()(),e.j41(17,"div",46),e.DNE(18,U,1,1,"video",47)(19,G,1,2,"div",48)(20,j,2,1,"div",49)(21,X,2,1,"div",50)(22,Y,5,0,"div",51),e.k0s(),e.j41(23,"div",52)(24,"div",53)(25,"button",54),e.nrm(26,"i",55),e.k0s(),e.j41(27,"button",56),e.nrm(28,"i",57),e.k0s(),e.j41(29,"button",58),e.nrm(30,"i",59),e.k0s()(),e.DNE(31,W,13,0,"div",60),e.k0s(),e.nrm(32,"div",61)(33,"div",62),e.k0s()(),e.nrm(34,"div",63,1),e.k0s()}if(2&u){const o=e.XpG();e.AVh("is-open",o.isOpen),e.R7$(4),e.Y8G("ngForOf",o.stories),e.R7$(),e.BMQ("data-story-id",o.currentIndex),e.R7$(3),e.xc7("background-image","url("+o.getCurrentStory().user.avatar+")"),e.R7$(2),e.JRh(o.getCurrentStory().user.fullName),e.R7$(2),e.JRh(o.getTimeAgo(o.getCurrentStory().createdAt)),e.R7$(2),e.SpI("",o.formatNumber(o.getCurrentStory().views)," views"),e.R7$(4),e.Y8G("ngIf","video"===o.getCurrentStory().mediaType),e.R7$(),e.Y8G("ngIf","image"===o.getCurrentStory().mediaType),e.R7$(),e.Y8G("ngIf",o.getCurrentStory().caption),e.R7$(),e.Y8G("ngIf",o.hasProducts()),e.R7$(),e.Y8G("ngIf",o.hasProducts()),e.R7$(9),e.Y8G("ngIf",o.hasProducts()),e.R7$(3),e.AVh("is-hidden",o.isOpen)}}function Q(u,x){1&u&&(e.j41(0,"div",84)(1,"div",85),e.nrm(2,"i",86),e.j41(3,"span"),e.EFF(4,"Tap to go back"),e.k0s()(),e.j41(5,"div",87)(6,"span"),e.EFF(7,"Tap to continue"),e.k0s(),e.nrm(8,"i",88),e.k0s()())}let Z=(()=>{class u{constructor(o,h,d,f){this.router=o,this.http=h,this.cartService=d,this.wishlistService=f,this.isMobile=!1,this.stories=[],this.showAddStory=!0,this.currentUser=null,this.storyClick=new e.bkB,this.isLoadingStories=!0,this.currentIndex=0,this.isOpen=!1,this.isRotating=!1,this.isDragging=!1,this.rotateY=0,this.targetRotateY=0,this.targetDirection=null,this.dragStartX=0,this.dragCurrentX=0,this.minDragPercentToTransition=.5,this.minVelocityToTransition=.65,this.transitionSpeed=6,this.isCarouselInitialized=!1,this.isAutoPlaying=!0,this.currentSlideIndex=0,this.customOptions={loop:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!1,dots:!1,navSpeed:700,navText:['<i class="fas fa-chevron-left"></i>','<i class="fas fa-chevron-right"></i>'],responsive:{0:{items:3,nav:!1},400:{items:4,nav:!1},740:{items:5,nav:!0},940:{items:6,nav:!0}},nav:!0,margin:2,stagePadding:0,autoplay:!0,autoplayTimeout:4e3,autoplayHoverPause:!0,autoplaySpeed:1e3},this.subscriptions=[]}ngOnInit(){this.checkScreenSize(),this.stories&&0!==this.stories.length?this.isLoadingStories=!1:this.loadStories(),this.setupEventListeners()}ngOnDestroy(){this.subscriptions.forEach(o=>o.unsubscribe()),this.removeEventListeners()}loadStories(){this.isLoadingStories=!0,this.stories=[{_id:"1",user:{_id:"user1",username:"zara",fullName:"Zara Official",avatar:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face"},mediaUrl:"https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop",mediaType:"image",caption:"New Summer Collection \u{1f31e}",createdAt:new Date(Date.now()-72e5).toISOString(),expiresAt:new Date(Date.now()+792e5).toISOString(),views:1250,isActive:!0,isViewed:!1},{_id:"2",user:{_id:"user2",username:"nike",fullName:"Nike",avatar:"https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center"},mediaUrl:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop",mediaType:"image",caption:"Just Do It \u2728",createdAt:new Date(Date.now()-144e5).toISOString(),expiresAt:new Date(Date.now()+72e6).toISOString(),views:2340,isActive:!0,isViewed:!1},{_id:"3",user:{_id:"user3",username:"adidas",fullName:"Adidas",avatar:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center"},mediaUrl:"https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop",mediaType:"image",caption:"Impossible is Nothing \u{1f525}",createdAt:new Date(Date.now()-216e5).toISOString(),expiresAt:new Date(Date.now()+648e5).toISOString(),views:1890,isActive:!0,isViewed:!1},{_id:"4",user:{_id:"user4",username:"hm",fullName:"H&M",avatar:"https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center"},mediaUrl:"https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop",mediaType:"image",caption:"Fashion for Everyone \u{1f4ab}",createdAt:new Date(Date.now()-288e5).toISOString(),expiresAt:new Date(Date.now()+576e5).toISOString(),views:3420,isActive:!0,isViewed:!1}],this.isLoadingStories=!1}getCurrentStory(){return this.stories[this.currentIndex]||this.stories[0]}getTimeAgo(o){const h=new Date,d=new Date(o),f=Math.floor((h.getTime()-d.getTime())/6e4);if(f<1)return"now";if(f<60)return`${f}m`;const k=Math.floor(f/60);return k<24?`${k}h`:`${Math.floor(k/24)}d`}formatNumber(o){return o&&null!=o?o>=1e6?(o/1e6).toFixed(1)+"M":o>=1e3?(o/1e3).toFixed(1)+"K":o.toString():"0"}openStories(o=0){this.currentIndex=o,this.isOpen=!0,this.showStory(o),document.body.style.overflow="hidden",this.stories[o]&&this.storyClick.emit({story:this.stories[o],index:o})}closeStories(){this.isOpen=!1,this.pauseAllVideos(),document.body.style.overflow="auto",this.storiesContainer&&this.storiesContainer.nativeElement.classList.add("is-closed"),setTimeout(()=>{this.storiesContainer&&this.storiesContainer.nativeElement.classList.remove("is-closed")},300)}showStory(o){this.currentIndex=o,this.rotateY=0,this.storiesContainer&&(this.storiesContainer.nativeElement.style.transform="translateZ(-50vw)")}nextStory(){this.currentIndex<this.stories.length-1?(this.targetRotateY=-90,this.targetDirection="forward",this.isRotating=!0,this.update()):this.closeStories()}previousStory(){this.currentIndex>0?(this.targetRotateY=90,this.targetDirection="back",this.isRotating=!0,this.update()):this.closeStories()}handleKeydown(o){if(this.isOpen)switch(o.key){case"ArrowLeft":this.previousStory();break;case"ArrowRight":this.nextStory();break;case"Escape":this.closeStories()}}onStoryClick(o){if(this.isRotating)return;const h=o.target.getBoundingClientRect();o.clientX-h.left<h.width/2?this.previousStory():this.nextStory()}onTouchStart(o){this.isDragging=!0,this.dragStartX=o.touches[0].clientX,this.dragCurrentX=this.dragStartX}onTouchMove(o){if(!this.isDragging)return;this.dragCurrentX=o.touches[0].clientX;const h=this.dragCurrentX-this.dragStartX;Math.abs(h)/window.innerWidth>this.minDragPercentToTransition&&(h>0?this.previousStory():this.nextStory(),this.isDragging=!1)}onTouchEnd(o){this.isDragging=!1}setupEventListeners(){}removeEventListeners(){}pauseAllVideos(){document.querySelectorAll("video").forEach(h=>{h.pause()})}update(){if(!this.isRotating)return;const o=this.targetRotateY-this.rotateY;this.rotateY+=.1*o,Math.abs(o)<.1&&(this.rotateY=this.targetRotateY,this.isRotating=!1,"forward"===this.targetDirection?this.currentIndex++:"back"===this.targetDirection&&this.currentIndex--,this.targetRotateY=0,this.targetDirection=null),this.storiesContainer&&(this.storiesContainer.nativeElement.style.transform=`translateZ(-50vw) rotateY(${this.rotateY}deg)`),this.isRotating&&requestAnimationFrame(()=>this.update())}hasProducts(){const o=this.getCurrentStory();return!!(o?.products&&o.products.length>0)}getStoryProducts(){return this.getCurrentStory().products||[]}formatPrice(o){return`\u20b9${(o/100).toLocaleString("en-IN")}`}viewProductDetails(o){console.log("Viewing product:",o),this.router.navigate(["/products",o._id])}getCurrentUserAvatar(){return this.currentUser?.avatar||"/assets/images/default-avatar.svg"}openAddStoryModal(){console.log("Opening add story modal"),this.router.navigate(["/add-story"])}buyNow(){const o=this.getStoryProducts();if(o.length>0){const h=o[0];console.log("Buying product:",h),this.router.navigate(["/checkout"],{queryParams:{productId:h._id,source:"story"}})}}viewProduct(o){this.trackProductClick(o,"view_product"),this.router.navigate(["/shop/product",o])}viewCategory(o){this.router.navigate(["/shop/category",o])}trackProductClick(o,h){console.log(`Story product ${h} tracked:`,o)}addToWishlist(){const o=this.getStoryProducts();if(o.length>0){const h=o[0];console.log("Adding to wishlist:",h),this.wishlistService.addToWishlist(h._id).subscribe({next:d=>{d.success?alert("Product added to wishlist!"):alert("Failed to add product to wishlist")},error:d=>{console.error("Error adding to wishlist:",d),alert("Error adding product to wishlist")}})}}addToCart(){const o=this.getStoryProducts();if(o.length>0){const h=o[0];console.log("Adding to cart:",h),this.cartService.addToCart(h._id,1,void 0,void 0).subscribe({next:d=>{d.success?alert("Product added to cart!"):alert("Failed to add product to cart")},error:d=>{console.error("Error adding to cart:",d),alert("Error adding product to cart")}})}}onSlideChanged(o){o&&void 0!==o.startPosition&&(this.currentSlideIndex=o.startPosition,console.log(`Stories slide changed to: ${this.currentSlideIndex}`),this.updateSlideAnalytics())}onInitialized(o){this.isCarouselInitialized=!0,console.log("Stories carousel initialized successfully with auto-sliding enabled")}updateSlideAnalytics(){this.stories&&this.stories[this.currentSlideIndex]&&console.log(`Viewing story from: ${this.stories[this.currentSlideIndex].user.username}`)}toggleAutoPlay(){this.isAutoPlaying=!this.isAutoPlaying,console.log("Auto-play "+(this.isAutoPlaying?"enabled":"disabled"))}checkScreenSize(){const o=window.innerWidth,h=navigator.userAgent,d=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(h);this.isMobile=o<=768||d}onResize(){this.checkScreenSize()}static{this.\u0275fac=function(h){return new(h||u)(e.rXU(C.Ix),e.rXU(z.Qq),e.rXU(L.CartService),e.rXU(V.WishlistService))}}static{this.\u0275cmp=e.VBU({type:u,selectors:[["app-view-add-stories"]],viewQuery:function(h,d){if(1&h&&(e.GBs(v,5),e.GBs(A,5),e.GBs(P,5)),2&h){let f;e.mGM(f=e.lsd())&&(d.storiesContainer=f.first),e.mGM(f=e.lsd())&&(d.feedCover=f.first),e.mGM(f=e.lsd())&&(d.storiesSlider=f.first)}},hostBindings:function(h,d){1&h&&e.bIt("keydown",function(k){return d.handleKeydown(k)},!1,e.EBC)("resize",function(k){return d.onResize(k)},!1,e.tSv)},inputs:{stories:"stories",showAddStory:"showAddStory",currentUser:"currentUser"},outputs:{storyClick:"storyClick"},standalone:!0,features:[e.aNF],decls:6,vars:5,consts:[["storiesContainer",""],["feedCover",""],[1,"stories-container"],["class","stories-header",4,"ngIf"],["class","stories-loading",4,"ngIf"],["class","stories-section",4,"ngIf"],["class","stories-wrapper",3,"is-open",4,"ngIf"],["class","touch-indicators",4,"ngIf"],[1,"stories-header"],[1,"stories-title"],[1,"stories-subtitle"],[1,"stories-loading"],["class","story-skeleton",4,"ngFor","ngForOf"],[1,"story-skeleton"],[1,"skeleton-avatar"],[1,"skeleton-name"],[1,"stories-section"],[1,"add-story-static"],[1,"story-item","add-story-item",3,"click"],[1,"story-avatar-container"],[1,"add-story-avatar"],[1,"add-story-icon"],[1,"fas","fa-plus"],[1,"current-user-avatar"],[1,"story-username"],[1,"stories-slider-wrapper"],[1,"stories-slider-container"],[3,"initialized","changed","options"],[4,"ngFor","ngForOf"],["carouselSlide",""],[1,"story-slide",3,"click"],[1,"story-avatar"],[1,"story-ring"],[1,"stories-wrapper"],[1,"stories"],[1,"story-progress"],["class","story-progress__bar",3,"active","completed",4,"ngFor","ngForOf"],[1,"story",3,"click","touchstart","touchmove","touchend"],[1,"story__top"],[1,"story__details"],[1,"story__avatar"],[1,"story__user"],[1,"story__time"],[1,"story__views"],[1,"story__close",3,"click"],[1,"fas","fa-times"],[1,"story__content"],["class","story__video","autoplay","","muted","","loop","","playsinline","",3,"src",4,"ngIf"],["class","story__image",3,"background-image",4,"ngIf"],["class","story__caption",4,"ngIf"],["class","story__product-tags",4,"ngIf"],["class","middle-navigation",4,"ngIf"],[1,"story__bottom"],[1,"story__actions"],[1,"story__action-btn","like-btn"],[1,"fas","fa-heart"],[1,"story__action-btn","comment-btn"],[1,"fas","fa-comment"],[1,"story__action-btn","share-btn"],[1,"fas","fa-share"],["class","story__ecommerce-actions",4,"ngIf"],[1,"story__nav-area","story__nav-prev"],[1,"story__nav-area","story__nav-next"],[1,"feed__cover"],[1,"story-progress__bar"],[1,"story-progress__fill"],["autoplay","","muted","","loop","","playsinline","",1,"story__video",3,"src"],[1,"story__image"],[1,"story__caption"],[1,"story__product-tags"],["class","product-tag",3,"click",4,"ngFor","ngForOf"],[1,"product-tag",3,"click"],[1,"product-tag-icon"],[1,"product-tag-info"],[1,"product-tag-name"],[1,"product-tag-price"],[1,"middle-navigation"],[1,"middle-nav-btn",3,"click"],[1,"fas","fa-shopping-bag"],[1,"story__ecommerce-actions"],[1,"ecommerce-btn","buy-now-btn",3,"click"],[1,"fas","fa-shopping-cart"],[1,"ecommerce-btn","wishlist-btn",3,"click"],[1,"ecommerce-btn","cart-btn",3,"click"],[1,"touch-indicators"],[1,"touch-indicator","left"],[1,"fas","fa-chevron-left"],[1,"touch-indicator","right"],[1,"fas","fa-chevron-right"]],template:function(h,d){1&h&&(e.j41(0,"div",2),e.DNE(1,I,5,0,"div",3)(2,S,2,2,"div",4)(3,F,14,4,"div",5),e.k0s(),e.DNE(4,K,36,17,"div",6)(5,Q,9,0,"div",7)),2&h&&(e.R7$(),e.Y8G("ngIf",!d.isMobile),e.R7$(),e.Y8G("ngIf",d.isLoadingStories),e.R7$(),e.Y8G("ngIf",!d.isLoadingStories),e.R7$(),e.Y8G("ngIf",d.isOpen),e.R7$(),e.Y8G("ngIf",d.isOpen))},dependencies:[w.MD,w.Sq,w.bT,y.Rl,y.gU,y.NA],styles:['.stories-container[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;padding:20px;margin-bottom:24px;width:100%;box-shadow:0 2px 8px #0000001a}@media (min-width: 769px){.stories-container[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;padding:24px;margin-bottom:24px;box-shadow:0 2px 8px #0000001a;position:relative;z-index:10}}@media (max-width: 768px){.stories-container[_ngcontent-%COMP%]{padding:12px 0;border:none;border-radius:0;border-bottom:1px solid #efefef;background:#fafafa;box-shadow:none;margin-bottom:0}}.stories-header[_ngcontent-%COMP%]{margin-bottom:20px}@media (min-width: 769px){.stories-header[_ngcontent-%COMP%]{border-bottom:1px solid #efefef;padding-bottom:16px;margin-bottom:24px}}.stories-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#262626;margin:0 0 4px}@media (min-width: 769px){.stories-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700}}.stories-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e;margin:0}@media (min-width: 769px){.stories-subtitle[_ngcontent-%COMP%]{font-size:15px}}.stories-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;padding:0}@media (min-width: 769px){.stories-section[_ngcontent-%COMP%]{padding:0;gap:20px;align-items:center;min-height:120px}}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]{padding:0 16px;gap:16px}}.add-story-static[_ngcontent-%COMP%]{flex-shrink:0;width:82px}.stories-slider-wrapper[_ngcontent-%COMP%]{flex:1;overflow:hidden;max-width:calc(100% - 98px);position:relative}.stories-slider-wrapper[_ngcontent-%COMP%]:after{content:"";position:absolute;top:0;right:0;width:20px;height:100%;background:linear-gradient(to left,rgba(255,255,255,.8),transparent);pointer-events:none;z-index:5;opacity:0;transition:opacity .3s ease}.stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]:after{opacity:1}.stories-slider-container[_ngcontent-%COMP%]{position:relative;width:100%;overflow:visible}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage{display:flex;align-items:flex-start}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item{display:flex;justify-content:center;align-items:flex-start;min-height:120px}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{position:absolute;top:50%;transform:translateY(-50%);width:100%;pointer-events:none}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{position:absolute;top:50%;transform:translateY(-50%);width:40px;height:40px;border-radius:50%;background:#00000080;color:#fff;border:none;display:none;align-items:center;justify-content:center;cursor:pointer;z-index:10;transition:all .2s ease;box-shadow:0 2px 8px #0003;pointer-events:all}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover{background:#000000b3;box-shadow:0 4px 12px #0000004d;transform:translateY(-50%) scale(1.1)}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active{transform:translateY(-50%) scale(.95)}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas{font-size:14px;color:#fff}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-20px}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-20px}@media (max-width: 768px){.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{display:none}}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer{position:relative}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer:after{content:"";position:absolute;bottom:-2px;left:0;right:0;height:2px;background:linear-gradient(90deg,transparent 0%,#405de6 50%,transparent 100%);opacity:.3;animation:_ngcontent-%COMP%_autoSlideIndicator 4s infinite linear}@keyframes _ngcontent-%COMP%_autoSlideIndicator{0%{transform:translate(-100%)}to{transform:translate(100%)}}.story-slide[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:transform .2s ease;width:66px}.story-slide[_ngcontent-%COMP%]:hover{transform:scale(1.05);animation-play-state:paused}.slider-nav-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);width:32px;height:32px;border-radius:50%;background:#ffffffe6;border:1px solid rgba(0,0,0,.1);display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:10;transition:all .2s ease;box-shadow:0 2px 8px #0000001a}.slider-nav-btn[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translateY(-50%) scale(1.1)}.slider-nav-btn[_ngcontent-%COMP%]:active{transform:translateY(-50%) scale(.95)}.slider-nav-btn.hidden[_ngcontent-%COMP%]{opacity:0;pointer-events:none}.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#262626}.slider-nav-left[_ngcontent-%COMP%]{left:-16px}.slider-nav-right[_ngcontent-%COMP%]{right:-16px}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;flex-shrink:0;transition:all .3s ease;width:82px;min-width:82px;position:relative}@media (min-width: 769px){.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:90px;min-width:90px;padding:8px;border-radius:12px}.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover{background:#0000000d;transform:scale(1.08)}}@media (max-width: 768px){.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:76px;min-width:76px}}.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%]{animation-duration:1s}.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%]{color:#0095f6;font-weight:600}.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active{transform:scale(.95)}.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);animation:_ngcontent-%COMP%_pulse 2s infinite}.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{color:#405de6;font-weight:600}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}to{transform:scale(1);opacity:1}}.story-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:8px}@media (max-width: 768px){.story-avatar-container[_ngcontent-%COMP%]{margin-bottom:6px}}.story-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;background-size:cover;background-position:center;border:2px solid #fff;box-shadow:0 2px 8px #0000001a;position:relative;z-index:2;transition:all .3s ease}@media (min-width: 769px){.story-avatar[_ngcontent-%COMP%]{width:74px;height:74px;border:3px solid #fff;box-shadow:0 4px 12px #00000026}.story-avatar[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 6px 16px #0003}}@media (max-width: 768px){.story-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border:1.5px solid #fff;box-shadow:0 1px 4px #0000001a}}.story-ring[_ngcontent-%COMP%]{position:absolute;top:-2px;left:-2px;width:70px;height:70px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:1;animation:_ngcontent-%COMP%_pulse 2s infinite}@media (min-width: 769px){.story-ring[_ngcontent-%COMP%]{top:-3px;left:-3px;width:80px;height:80px;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);box-shadow:0 0 20px #f094334d}}@media (max-width: 768px){.story-ring[_ngcontent-%COMP%]{width:64px;height:64px;top:-2px;left:-2px}}.story-ring.viewed[_ngcontent-%COMP%]{background:#c7c7c7;animation:none}.story-ring.active[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);animation:_ngcontent-%COMP%_pulse 1.5s infinite;box-shadow:0 0 10px #f0943380}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}.story-username[_ngcontent-%COMP%]{font-size:12px;color:#262626;font-weight:400;max-width:74px;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:1.2;margin-top:8px}@media (min-width: 769px){.story-username[_ngcontent-%COMP%]{font-size:13px;font-weight:500;max-width:90px;margin-top:10px;color:#262626;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}}@media (max-width: 768px){.story-username[_ngcontent-%COMP%]{font-size:11px;max-width:70px;font-weight:500;margin-top:6px}}.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-weight:600;color:#262626}.add-story-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;position:relative;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);display:flex;align-items:center;justify-content:center;z-index:2}.add-story-icon[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:20px;height:20px;background:#0095f6;border-radius:50%;display:flex;align-items:center;justify-content:center;border:2px solid white;z-index:3}.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:10px;font-weight:700}.current-user-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background-size:cover;background-position:center;border:2px solid white}.stories-loading[_ngcontent-%COMP%]{display:flex;gap:16px;padding:0 16px}.story-skeleton[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px}.skeleton-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-name[_ngcontent-%COMP%]{width:60px;height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.story-bar__user[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:transform .2s ease}.story-bar__user[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.story-bar__user.bounce[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_bounce .3s ease}.story-bar__user-avatar[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;background-size:cover;background-position:center;border:3px solid transparent;background-clip:padding-box;position:relative}.story-bar__user-avatar[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-3px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-1}.story-bar__user-name[_ngcontent-%COMP%]{margin-top:4px;font-size:12px;color:#262626;text-align:center;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.stories-wrapper[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;z-index:9999;perspective:400px;overflow:hidden;opacity:0;visibility:hidden;transition:opacity .3s ease,visibility .3s ease}.stories-wrapper.is-open[_ngcontent-%COMP%]{opacity:1;visibility:visible}.stories[_ngcontent-%COMP%]{width:100%;height:100%;transform-style:preserve-3d;transform:translateZ(-50vw);transition:transform .25s ease-out}.stories.is-closed[_ngcontent-%COMP%]{opacity:0;transform:scale(.1)}.story-progress[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;right:8px;display:flex;gap:2px;z-index:100}.story-progress__bar[_ngcontent-%COMP%]{flex:1;height:2px;background:#ffffff4d;border-radius:1px;overflow:hidden}.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%]{width:100%}.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progress 15s linear}.story-progress__fill[_ngcontent-%COMP%]{height:100%;background:#fff;width:0%;transition:width .1s ease}@keyframes _ngcontent-%COMP%_progress{0%{width:0%}to{width:100%}}.story[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%;overflow:hidden;display:flex;flex-direction:column;-webkit-user-select:none;user-select:none}.story__top[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;padding:48px 16px 16px;background:linear-gradient(180deg,rgba(0,0,0,.6) 0%,transparent 100%);z-index:10;display:flex;justify-content:space-between;align-items:center}.story__details[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.story__avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-size:cover;background-position:center;border:2px solid #fff}.story__user[_ngcontent-%COMP%]{color:#fff;font-weight:600;font-size:14px}.story__time[_ngcontent-%COMP%]{color:#ffffffb3;font-size:12px}.story__views[_ngcontent-%COMP%]{color:#fff9;font-size:11px;margin-left:8px}.story__close[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:18px;cursor:pointer;padding:8px;border-radius:50%;transition:background .2s ease}.story__close[_ngcontent-%COMP%]:hover{background:#ffffff1a}.story__content[_ngcontent-%COMP%]{flex:1;position:relative;display:flex;align-items:center;justify-content:center;background:#000}.story__video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.story__image[_ngcontent-%COMP%]{width:100%;height:100%;background-size:cover;background-position:center;background-repeat:no-repeat}.story__caption[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:16px;right:16px;background:#0009;color:#fff;padding:12px 16px;border-radius:20px;font-size:14px;line-height:1.4;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:5}.story__product-tags[_ngcontent-%COMP%]{position:absolute;top:50%;left:20px;transform:translateY(-50%);z-index:6}.product-tag[_ngcontent-%COMP%]{background:#fffffff2;border-radius:12px;padding:8px 12px;margin-bottom:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);display:flex;align-items:center;gap:8px;min-width:160px}.product-tag[_ngcontent-%COMP%]:hover{transform:scale(1.05);background:#fff;box-shadow:0 4px 15px #0003}.product-tag-icon[_ngcontent-%COMP%]{font-size:16px}.product-tag-info[_ngcontent-%COMP%]{flex:1}.product-tag-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#333;margin-bottom:2px;line-height:1.2}.product-tag-price[_ngcontent-%COMP%]{font-size:11px;color:#666;font-weight:500}.story__bottom[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;padding:16px;background:linear-gradient(0deg,rgba(0,0,0,.6) 0%,transparent 100%);z-index:10}.story__actions[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:12px}.story__action-btn[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:20px;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.story__action-btn[_ngcontent-%COMP%]:hover{background:#ffffff1a;transform:scale(1.1)}.story__ecommerce-actions[_ngcontent-%COMP%]{display:flex;gap:8px;justify-content:center}.ecommerce-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;padding:8px 12px;border:none;border-radius:20px;font-size:12px;font-weight:600;cursor:pointer;transition:all .2s ease}.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#ee5a24);color:#fff}.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #ff6b6b66}.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff9ff3,#f368e0);color:#fff}.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #ff9ff366}.ecommerce-btn.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#54a0ff,#2e86de);color:#fff}.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #54a0ff66}.story__nav-area[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;width:33%;z-index:5;cursor:pointer}.story__nav-area.story__nav-prev[_ngcontent-%COMP%]{left:0}.story__nav-area.story__nav-next[_ngcontent-%COMP%]{right:0;width:67%}.feed__cover[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#fff;z-index:-1}.feed__cover.is-hidden[_ngcontent-%COMP%]{opacity:0}.touch-indicators[_ngcontent-%COMP%]{position:fixed;top:50%;left:0;right:0;transform:translateY(-50%);z-index:101;pointer-events:none;display:none}@media (max-width: 768px){.touch-indicators[_ngcontent-%COMP%]{display:block}}.touch-indicator[_ngcontent-%COMP%]{position:absolute;display:flex;align-items:center;gap:8px;color:#ffffffb3;font-size:12px;animation:_ngcontent-%COMP%_fadeInOut 3s infinite}.touch-indicator.left[_ngcontent-%COMP%]{left:16px}.touch-indicator.right[_ngcontent-%COMP%]{right:16px}@keyframes _ngcontent-%COMP%_fadeInOut{0%,to{opacity:0}50%{opacity:1}}@keyframes _ngcontent-%COMP%_bounce{0%,to{transform:scale(1)}50%{transform:scale(.8)}}@media (max-width: 1024px){.story-bar[_ngcontent-%COMP%]{padding:12px 16px;gap:10px;overflow-x:auto;scroll-behavior:smooth;-webkit-overflow-scrolling:touch}.stories-wrapper[_ngcontent-%COMP%]{touch-action:pan-y}.story[_ngcontent-%COMP%]{touch-action:manipulation}.stories-section[_ngcontent-%COMP%]{gap:12px;padding:0 12px}.add-story-static[_ngcontent-%COMP%]{width:70px}.stories-slider-wrapper[_ngcontent-%COMP%]{max-width:calc(100% - 82px)}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:70px;min-width:70px}.stories-list[_ngcontent-%COMP%]{gap:12px}.slider-nav-btn[_ngcontent-%COMP%]{display:none}}@media (max-width: 768px){.story-bar[_ngcontent-%COMP%]{padding:8px 12px;gap:8px;scrollbar-width:none;-ms-overflow-style:none}.story-bar[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.stories-section[_ngcontent-%COMP%]{gap:10px;padding:0 8px}.add-story-static[_ngcontent-%COMP%]{width:60px}.stories-slider-wrapper[_ngcontent-%COMP%]{max-width:calc(100% - 70px)}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:60px;min-width:60px}.stories-list[_ngcontent-%COMP%]{gap:10px}.slider-nav-btn[_ngcontent-%COMP%]{display:none}.story-avatar[_ngcontent-%COMP%]{width:56px;height:56px}.story-ring[_ngcontent-%COMP%]{width:60px;height:60px;top:-2px;left:-2px}.add-story-avatar[_ngcontent-%COMP%]{width:56px;height:56px}.current-user-avatar[_ngcontent-%COMP%]{width:50px;height:50px}.story-username[_ngcontent-%COMP%]{font-size:11px;max-width:60px}.story-bar__user-avatar[_ngcontent-%COMP%]{width:48px;height:48px}.story-bar__user-avatar[_ngcontent-%COMP%]:before{inset:-2px}.story-bar__user-name[_ngcontent-%COMP%]{font-size:11px;max-width:56px}.story__top[_ngcontent-%COMP%]{padding:40px 12px 12px}.story__bottom[_ngcontent-%COMP%]{padding:12px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:row;flex-wrap:wrap;gap:6px;justify-content:space-between}.ecommerce-btn[_ngcontent-%COMP%]{padding:8px 12px;font-size:11px;flex:1;min-width:80px}.ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.story__actions[_ngcontent-%COMP%]{gap:12px;margin-bottom:8px}.story__action-btn[_ngcontent-%COMP%]{font-size:18px;padding:6px}}@media (max-width: 480px){.story-bar[_ngcontent-%COMP%]{padding:6px 8px;gap:6px}.story-bar__user-avatar[_ngcontent-%COMP%]{width:40px;height:40px}.story-bar__user-avatar[_ngcontent-%COMP%]:before{inset:-2px}.story-bar__user-name[_ngcontent-%COMP%]{font-size:10px;max-width:48px}.story__top[_ngcontent-%COMP%]{padding:32px 8px 8px}.story__bottom[_ngcontent-%COMP%]{padding:8px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:column;gap:4px}.ecommerce-btn[_ngcontent-%COMP%]{padding:6px 8px;font-size:10px}.ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.story__actions[_ngcontent-%COMP%]{gap:8px;margin-bottom:6px}.story__action-btn[_ngcontent-%COMP%]{font-size:16px;padding:4px}.story__user[_ngcontent-%COMP%]{font-size:12px}.story__time[_ngcontent-%COMP%]{font-size:10px}.story__avatar[_ngcontent-%COMP%]{width:28px;height:28px}}@media (hover: none) and (pointer: coarse){.story-bar__user[_ngcontent-%COMP%]:active, .ecommerce-btn[_ngcontent-%COMP%]:active{transform:scale(.95);transition:transform .1s ease}.story__action-btn[_ngcontent-%COMP%]:active, .story__close[_ngcontent-%COMP%]:active{transform:scale(.9);transition:transform .1s ease}}@media (max-width: 896px) and (orientation: landscape){.story__top[_ngcontent-%COMP%]{padding:24px 12px 8px}.story__bottom[_ngcontent-%COMP%]{padding:8px 12px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:row;gap:8px}.ecommerce-btn[_ngcontent-%COMP%]{padding:6px 10px;font-size:10px}}.middle-navigation[_ngcontent-%COMP%]{position:absolute;bottom:100px;left:50%;transform:translate(-50%);z-index:4}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#ee5a24);border:none;border-radius:25px;padding:12px 24px;color:#fff;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #ff6b6b4d}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #ff6b6b66}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px}@media (min-resolution: 192dpi){.story-bar__user-avatar[_ngcontent-%COMP%], .story__avatar[_ngcontent-%COMP%]{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}}@media (max-width: 768px){.middle-navigation[_ngcontent-%COMP%]{bottom:80px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]{padding:10px 20px;font-size:12px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px}}']})}}return u})()},1986:(nt,E,g)=>{g.d(E,{c:()=>$});var e=g(3236),w=g(8793),y=g(6697),C=g(9974),z=g(4360),L=g(5343),v=g(3703),A=g(1397),P=g(8750);function R(S,O){return O?M=>(0,w.x)(O.pipe((0,y.s)(1),function V(){return(0,C.N)((S,O)=>{S.subscribe((0,z._)(O,L.l))})}()),M.pipe(R(S))):(0,A.Z)((M,F)=>(0,P.Tg)(S(M,F)).pipe((0,y.s)(1),(0,v.u)(M)))}var I=g(1584);function $(S,O=e.E){const M=(0,I.O)(S,O);return R(()=>M)}},1708:(nt,E,g)=>{g.d(E,{gU:()=>Ot,Rl:()=>Pt,NA:()=>ut});var e=g(4438),w=g(177),y=g(1413),C=g(7786),z=g(7673),L=g(3726),V=g(6648),v=g(8141),A=g(5964),P=g(5558),R=g(1594),I=g(6697),$=g(5245),S=g(6354),O=g(6649),M=g(9974);const N=(a,m)=>(a.push(m),a);var G=g(1986),j=g(2168),D=g(9969);const X=(a,m,t,i,s)=>({width:a,transform:m,transition:t,"padding-left":i,"padding-right":s}),Y=(a,m,t,i)=>({width:a,"margin-left":m,"margin-right":t,left:i}),W=(a,m)=>({$implicit:a,index:m});function K(a,m){}function Q(a,m){if(1&a&&e.DNE(0,K,0,0,"ng-template",4),2&a){const t=e.XpG(),i=t.$implicit,s=t.index,n=e.XpG();e.Y8G("ngTemplateOutlet",i.tplRef)("ngTemplateOutletContext",e.l_i(2,W,n.preparePublicSlide(i),s))}}function Z(a,m){if(1&a){const t=e.RV6();e.qex(0),e.j41(1,"div",2),e.bIt("animationend",function(){const s=e.eBV(t).$implicit,n=e.XpG();return e.Njj(n.clear(s.id))}),e.DNE(2,Q,1,5,null,3),e.k0s(),e.bVm()}if(2&a){const t=m.$implicit;e.R7$(),e.Y8G("ngClass",t.classes)("ngStyle",e.ziG(4,Y,t.width+"px",t.marginL?t.marginL+"px":"",t.marginR?t.marginR+"px":"",t.left))("@autoHeight",t.heightState),e.R7$(),e.Y8G("ngIf",t.load)}}const u=(a,m,t,i,s)=>({"owl-rtl":a,"owl-loaded":m,"owl-responsive":t,"owl-drag":i,"owl-grab":s}),x=(a,m)=>({isMouseDragable:a,isTouchDragable:m}),o=a=>({disabled:a}),h=(a,m)=>({active:a,"owl-dot-text":m});function d(a,m){if(1&a&&(e.j41(0,"div",4),e.nrm(1,"owl-stage",5),e.k0s()),2&a){const t=e.XpG();e.R7$(),e.Y8G("owlDraggable",e.l_i(3,x,null==t.owlDOMData?null:t.owlDOMData.isMouseDragable,null==t.owlDOMData?null:t.owlDOMData.isTouchDragable))("stageData",t.stageData)("slidesData",t.slidesData)}}function f(a,m){if(1&a){const t=e.RV6();e.j41(0,"div",11),e.bIt("click",function(){const s=e.eBV(t).$implicit,n=e.XpG(2);return e.Njj(n.moveByDot(s.id))}),e.nrm(1,"span",12),e.k0s()}if(2&a){const t=m.$implicit;e.Y8G("ngClass",e.l_i(2,h,t.active,t.showInnerContent)),e.R7$(),e.Y8G("innerHTML",t.innerContent,e.npT)}}function k(a,m){if(1&a){const t=e.RV6();e.qex(0),e.j41(1,"div",6)(2,"div",7),e.bIt("click",function(){e.eBV(t);const s=e.XpG();return e.Njj(s.prev())}),e.k0s(),e.j41(3,"div",8),e.bIt("click",function(){e.eBV(t);const s=e.XpG();return e.Njj(s.next())}),e.k0s()(),e.j41(4,"div",9),e.DNE(5,f,2,5,"div",10),e.k0s(),e.bVm()}if(2&a){const t=e.XpG();e.R7$(),e.Y8G("ngClass",e.eq3(7,o,null==t.navData?null:t.navData.disabled)),e.R7$(),e.Y8G("ngClass",e.eq3(9,o,null==t.navData||null==t.navData.prev?null:t.navData.prev.disabled))("innerHTML",null==t.navData||null==t.navData.prev?null:t.navData.prev.htmlText,e.npT),e.R7$(),e.Y8G("ngClass",e.eq3(11,o,null==t.navData||null==t.navData.next?null:t.navData.next.disabled))("innerHTML",null==t.navData||null==t.navData.next?null:t.navData.next.htmlText,e.npT),e.R7$(),e.Y8G("ngClass",e.eq3(13,o,null==t.dotsData?null:t.dotsData.disabled)),e.R7$(),e.Y8G("ngForOf",null==t.dotsData?null:t.dotsData.dots)}}class rt{items=3;skip_validateItems=!1;loop=!1;center=!1;rewind=!1;mouseDrag=!0;touchDrag=!0;pullDrag=!0;freeDrag=!1;margin=0;stagePadding=0;merge=!1;mergeFit=!0;autoWidth=!1;startPosition=0;rtl=!1;smartSpeed=250;fluidSpeed=!1;dragEndSpeed=!1;responsive={};responsiveRefreshRate=200;nav=!1;navText=["prev","next"];navSpeed=!1;slideBy=1;dots=!0;dotsEach=!1;dotsData=!1;dotsSpeed=!1;autoplay=!1;autoplayTimeout=5e3;autoplayHoverPause=!1;autoplaySpeed=!1;autoplayMouseleaveTimeout=1;lazyLoad=!1;lazyLoadEager=0;slideTransition="";animateOut=!1;animateIn=!1;autoHeight=!1;URLhashListener=!1;constructor(){}}class pt{items="number";skip_validateItems="boolean";loop="boolean";center="boolean";rewind="boolean";mouseDrag="boolean";touchDrag="boolean";pullDrag="boolean";freeDrag="boolean";margin="number";stagePadding="number";merge="boolean";mergeFit="boolean";autoWidth="boolean";startPosition="number|string";rtl="boolean";smartSpeed="number";fluidSpeed="boolean";dragEndSpeed="number|boolean";responsive={};responsiveRefreshRate="number";nav="boolean";navText="string[]";navSpeed="number|boolean";slideBy="number|string";dots="boolean";dotsEach="number|boolean";dotsData="boolean";dotsSpeed="number|boolean";autoplay="boolean";autoplayTimeout="number";autoplayHoverPause="boolean";autoplaySpeed="number|boolean";autoplayMouseleaveTimeout="number";lazyLoad="boolean";lazyLoadEager="number";slideTransition="string";animateOut="string|boolean";animateIn="string|boolean";autoHeight="boolean";URLhashListener="boolean";constructor(){}}let J=(()=>{class a{errorHandler;constructor(t){this.errorHandler=t}log(t,...i){(0,e.naY)()&&console.log(t,...i)}error(t){this.errorHandler.handleError(t)}warn(t,...i){console.warn(t,...i)}static \u0275fac=function(i){return new(i||a)(e.KVO(e.zcH))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})();var at=function(a){return a.Event="event",a.State="state",a}(at||{}),B=function(a){return a.Default="default",a.Inner="inner",a.Outer="outer",a}(B||{});let T=(()=>{class a{logger;_viewSettingsShipper$=new y.B;_initializedCarousel$=new y.B;_changeSettingsCarousel$=new y.B;_changedSettingsCarousel$=new y.B;_translateCarousel$=new y.B;_translatedCarousel$=new y.B;_resizeCarousel$=new y.B;_resizedCarousel$=new y.B;_refreshCarousel$=new y.B;_refreshedCarousel$=new y.B;_dragCarousel$=new y.B;_draggedCarousel$=new y.B;settings={items:0};owlDOMData={rtl:!1,isResponsive:!1,isRefreshed:!1,isLoaded:!1,isLoading:!1,isMouseDragable:!1,isGrab:!1,isTouchDragable:!1};stageData={transform:"translate3d(0px,0px,0px)",transition:"0s",width:0,paddingL:0,paddingR:0};slidesData;navData;dotsData;_width;_items=[];_widths=[];_supress={};_plugins={};_current=null;_clones=[];_mergers=[];_speed=null;_coordinates=[];_breakpoint=null;clonedIdPrefix="cloned-";_options={};_invalidated={};get invalidated(){return this._invalidated}_states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}};get states(){return this._states}_pipe=[{filter:["width","items","settings"],run:t=>{t.current=this._items&&this._items[this.relative(this._current)]?.id}},{filter:["width","items","settings"],run:t=>{const i=this.settings.margin||"",n=this.settings.rtl,r={"margin-left":n?i:"","margin-right":n?"":i};!this.settings.autoWidth||this.slidesData.forEach(l=>{l.marginL=r["margin-left"],l.marginR=r["margin-right"]}),t.css=r}},{filter:["width","items","settings"],run:t=>{const i=+(this.width()/this.settings.items).toFixed(3)-this.settings.margin,s=!this.settings.autoWidth,n=[];let r=null,l=this._items.length;for(t.items={merge:!1,width:i};l-- >0;)r=this._mergers[l],r=this.settings.mergeFit&&Math.min(r,this.settings.items)||r,t.items.merge=r>1||t.items.merge,n[l]=s?i*r:this._items[l].width?this._items[l].width:i;this._widths=n,this.slidesData.forEach((c,p)=>{c.width=this._widths[p],c.marginR=t.css["margin-right"],c.marginL=t.css["margin-left"]})}},{filter:["items","settings"],run:()=>{const t=[],i=this._items,s=this.settings,n=Math.max(2*s.items,4),r=2*Math.ceil(i.length/2);let l=[],c=[],p=s.loop&&i.length?s.rewind?n:Math.max(n,r):0;for(p/=2;p-- >0;)t.push(this.normalize(t.length/2,!0)),l.push({...this.slidesData[t[t.length-1]]}),t.push(this.normalize(i.length-1-(t.length-1)/2,!0)),c.unshift({...this.slidesData[t[t.length-1]]});this._clones=t,l=l.map(_=>(_.id=`${this.clonedIdPrefix}${_.id}`,_.isActive=!1,_.isCloned=!0,_)),c=c.map(_=>(_.id=`${this.clonedIdPrefix}${_.id}`,_.isActive=!1,_.isCloned=!0,_)),this.slidesData=c.concat(this.slidesData).concat(l)}},{filter:["width","items","settings"],run:()=>{const t=this.settings.rtl?1:-1,i=this._clones.length+this._items.length,s=[];let n=-1,r=0,l=0;for(;++n<i;)r=s[n-1]||0,l=this._widths[this.relative(n)]+this.settings.margin,s.push(r+l*t);this._coordinates=s}},{filter:["width","items","settings"],run:()=>{const t=this.settings.stagePadding,i=this._coordinates,s={width:Math.ceil(Math.abs(i[i.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.stageData.width=s.width,this.stageData.paddingL=s["padding-left"],this.stageData.paddingR=s["padding-right"]}},{filter:["width","items","settings"],run:t=>{let i=t.current?this.slidesData.findIndex(s=>s.id===t.current):0;i=Math.max(this.minimum(),Math.min(this.maximum(),i)),this.reset(i)}},{filter:["position"],run:()=>{this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:()=>{const t=this.settings.rtl?1:-1,i=2*this.settings.stagePadding,s=[];let n,r,l,c,p,_;if(n=this.coordinates(this.current()),"number"==typeof n?n+=i:n=0,r=n+this.width()*t,-1===t&&this.settings.center){const b=this._coordinates.filter(H=>this.settings.items%2==1?H>=n:H>n);n=b.length?b[b.length-1]:n}for(p=0,_=this._coordinates.length;p<_;p++)l=Math.ceil(this._coordinates[p-1]||0),c=Math.ceil(Math.abs(this._coordinates[p])+i*t),(this._op(l,"<=",n)&&this._op(l,">",r)||this._op(c,"<",n)&&this._op(c,">",r))&&s.push(p);this.slidesData.forEach(b=>(b.isActive=!1,b)),s.forEach(b=>{this.slidesData[b].isActive=!0}),this.settings.center&&(this.slidesData.forEach(b=>(b.isCentered=!1,b)),this.slidesData[this.current()].isCentered=!0)}}];constructor(t){this.logger=t}getViewCurSettings(){return this._viewSettingsShipper$.asObservable()}getInitializedState(){return this._initializedCarousel$.asObservable()}getChangeState(){return this._changeSettingsCarousel$.asObservable()}getChangedState(){return this._changedSettingsCarousel$.asObservable()}getTranslateState(){return this._translateCarousel$.asObservable()}getTranslatedState(){return this._translatedCarousel$.asObservable()}getResizeState(){return this._resizeCarousel$.asObservable()}getResizedState(){return this._resizedCarousel$.asObservable()}getRefreshState(){return this._refreshCarousel$.asObservable()}getRefreshedState(){return this._refreshedCarousel$.asObservable()}getDragState(){return this._dragCarousel$.asObservable()}getDraggedState(){return this._draggedCarousel$.asObservable()}setOptions(t){const i=new rt,s=this._validateOptions(t,i);this._options={...i,...s}}_validateOptions(t,i){const s={...t},n=new pt,r=(l,c)=>(this.logger.log(`options.${c} must be type of ${l}; ${c}=${t[c]} skipped to defaults: ${c}=${i[c]}`),i[c]);for(const l in s)if(s.hasOwnProperty(l))if("number"===n[l])this._isNumeric(s[l])?(s[l]=+s[l],s[l]="items"===l?this._validateItems(s[l],s.skip_validateItems):s[l]):s[l]=r(n[l],l);else if("boolean"===n[l]&&"boolean"!=typeof s[l])s[l]=r(n[l],l);else if("number|boolean"!==n[l]||this._isNumberOrBoolean(s[l]))if("number|string"!==n[l]||this._isNumberOrString(s[l]))if("string|boolean"!==n[l]||this._isStringOrBoolean(s[l])){if("string[]"===n[l])if(Array.isArray(s[l])){let c=!1;s[l].forEach(p=>{c="string"==typeof p}),c||(s[l]=r(n[l],l))}else s[l]=r(n[l],l)}else s[l]=r(n[l],l);else s[l]=r(n[l],l);else s[l]=r(n[l],l);return s}_validateItems(t,i){let s=t;return t>this._items.length?i?this.logger.log("The option 'items' in your options is bigger than the number of slides. The navigation got disabled"):(s=this._items.length,this.logger.log("The option 'items' in your options is bigger than the number of slides. This option is updated to the current number of slides and the navigation got disabled")):t===this._items.length&&(this.settings.dots||this.settings.nav)&&this.logger.log("Option 'items' in your options is equal to the number of slides. So the navigation got disabled"),s}setCarouselWidth(t){this._width=t}setup(t,i,s){this.setCarouselWidth(t),this.setItems(i),this._defineSlidesData(),this.setOptions(s),this.settings={...this._options},this.setOptionsForViewport(),this._trigger("change",{property:{name:"settings",value:this.settings}}),this.invalidate("settings"),this._trigger("changed",{property:{name:"settings",value:this.settings}})}setOptionsForViewport(){const t=this._width,i=this._options.responsive;let s=-1;if(!Object.keys(i).length)return;if(!t)return void(this.settings.items=1);for(const r in i)i.hasOwnProperty(r)&&+r<=t&&+r>s&&(s=Number(r));this.settings={...this._options,...i[s],items:i[s]&&i[s].items?this._validateItems(i[s].items,this._options.skip_validateItems):this._options.items},delete this.settings.responsive,this.owlDOMData.isResponsive=!0,this.owlDOMData.isMouseDragable=this.settings.mouseDrag,this.owlDOMData.isTouchDragable=this.settings.touchDrag;const n=[];this._items.forEach(r=>{n.push(this.settings.merge?r.dataMerge:1)}),this._mergers=n,this._breakpoint=s,this.invalidate("settings")}initialize(t){this.enter("initializing"),this.owlDOMData.rtl=this.settings.rtl,this._mergers.length&&(this._mergers=[]),t.forEach(i=>{this._mergers.push(this.settings.merge?i.dataMerge:1)}),this._clones=[],this.reset(this._isNumeric(this.settings.startPosition)?+this.settings.startPosition:0),this.invalidate("items"),this.refresh(),this.owlDOMData.isLoaded=!0,this.owlDOMData.isMouseDragable=this.settings.mouseDrag,this.owlDOMData.isTouchDragable=this.settings.touchDrag,this.sendChanges(),this.leave("initializing"),this._trigger("initialized")}sendChanges(){this._viewSettingsShipper$.next({owlDOMData:this.owlDOMData,stageData:this.stageData,slidesData:this.slidesData,navData:this.navData,dotsData:this.dotsData})}_optionsLogic(){this.settings.autoWidth&&(this.settings.stagePadding=0,this.settings.merge=!1)}update(){let t=0;const i=this._pipe.length,s=r=>this._invalidated[r],n={};for(;t<i;){const r=this._pipe[t].filter.filter(s);(this._invalidated.all||r.length>0)&&this._pipe[t].run(n),t++}this.slidesData.forEach(r=>r.classes=this.setCurSlideClasses(r)),this.sendChanges(),this._invalidated={},this.is("valid")||this.enter("valid")}width(t){switch(t=t||B.Default){case B.Inner:case B.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}}refresh(){this.enter("refreshing"),this._trigger("refresh"),this._defineSlidesData(),this.setOptionsForViewport(),this._optionsLogic(),this.update(),this.leave("refreshing"),this._trigger("refreshed")}onResize(t){if(!this._items.length)return!1;this.setCarouselWidth(t),this.enter("resizing"),this._trigger("resize"),this.invalidate("width"),this.refresh(),this.leave("resizing"),this._trigger("resized")}prepareDragging(t){let s,i=null;return s=this.stageData.transform.replace(/.*\(|\)| |[^,-\d]\w|\)/g,"").split(","),i={x:+s[0],y:+s[1]},this.is("animating")&&this.invalidate("position"),"mousedown"===t.type&&(this.owlDOMData.isGrab=!0),this.speed(0),i}enterDragging(){this.enter("dragging"),this._trigger("drag")}defineNewCoordsDrag(t,i){let s=null,n=null,r=null;const l=this.difference(i.pointer,this.pointer(t)),c=this.difference(i.stage.start,l);return!!this.is("dragging")&&(this.settings.loop?(s=this.coordinates(this.minimum()),n=+this.coordinates(this.maximum()+1)-s,c.x=((c.x-s)%n+n)%n+s):(s=this.coordinates(this.settings.rtl?this.maximum():this.minimum()),n=this.coordinates(this.settings.rtl?this.minimum():this.maximum()),r=this.settings.pullDrag?-1*l.x/5:0,c.x=Math.max(Math.min(c.x,s+r),n+r)),c)}finishDragging(t,i,s){const r=this.difference(i.pointer,this.pointer(t)),l=i.stage.current,c=["right","left"][+(this.settings.rtl?r.x<+this.settings.rtl:r.x>+this.settings.rtl)];let p,_,b;(0!==r.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(+this.settings.dragEndSpeed||this.settings.smartSpeed),p=this.closest(l.x,0!==r.x?c:i.direction),_=this.current(),b=this.current(-1===p?void 0:p),_!==b&&(this.invalidate("position"),this.update()),i.direction=c,(Math.abs(r.x)>3||(new Date).getTime()-i.time>300)&&s()),this.is("dragging")&&(this.leave("dragging"),this._trigger("dragged"))}closest(t,i){const n=this.width();let r=this.coordinates(),l=-1;this.settings.center&&(r=r.map(c=>(0===c&&(c+=1e-6),c)));for(let c=0;c<r.length&&("left"===i&&t>r[c]-30&&t<r[c]+30?l=c:"right"===i&&t>r[c]-n-30&&t<r[c]-n+30?l=c+1:this._op(t,"<",r[c])&&this._op(t,">",r[c+1]||r[c]-n)?l="left"===i?c+1:c:null===i&&t>r[c]-30&&t<r[c]+30&&(l=c),-1===l);c++);return this.settings.loop||(this._op(t,">",r[this.minimum()])?l=t=this.minimum():this._op(t,"<",r[this.maximum()])&&(l=t=this.maximum())),l}animate(t){const i=this.speed()>0;this.is("animating")&&this.onTransitionEnd(),i&&(this.enter("animating"),this._trigger("translate")),this.stageData.transform="translate3d("+t+"px,0px,0px)",this.stageData.transition=this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}is(t){return this._states.current[t]&&this._states.current[t]>0}current(t){return void 0===t?this._current:0!==this._items.length?(t=this.normalize(t),this._current!==t&&(this._trigger("change",{property:{name:"position",value:t}}),this._current=t,this.invalidate("position"),this._trigger("changed",{property:{name:"position",value:this._current}})),this._current):void 0}invalidate(t){return"string"==typeof t&&(this._invalidated[t]=!0,this.is("valid")&&this.leave("valid")),Object.keys(this._invalidated)}reset(t){void 0!==(t=this.normalize(t))&&(this._speed=0,this._current=t,this._suppress(["translate","translated"]),this.animate(this.coordinates(t)),this._release(["translate","translated"]))}normalize(t,i){const s=this._items.length,n=i?0:this._clones.length;return!this._isNumeric(t)||s<1?t=void 0:(t<0||t>=s+n)&&(t=((t-n/2)%s+s)%s+n/2),t}relative(t){return this.normalize(t-=this._clones.length/2,!0)}maximum(t=!1){const i=this.settings;let n,r,l,s=this._coordinates.length;if(i.loop)s=this._clones.length/2+this._items.length-1;else if(i.autoWidth||i.merge){for(n=this._items.length,r=this.slidesData[--n].width,l=this._width;n-- >0&&(r+=+this.slidesData[n].width+this.settings.margin,!(r>l)););s=n+1}else s=i.center?this._items.length-1:this._items.length-i.items;return t&&(s-=this._clones.length/2),Math.max(s,0)}minimum(t=!1){return t?0:this._clones.length/2}items(t){return void 0===t?this._items.slice():(t=this.normalize(t,!0),[this._items[t]])}mergers(t){return void 0===t?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])}clones(t){const i=this._clones.length/2,s=i+this._items.length,n=r=>r%2==0?s+r/2:i-(r+1)/2;return void 0===t?this._clones.map((r,l)=>n(l)):this._clones.map((r,l)=>r===t?n(l):null).filter(r=>r)}speed(t){return void 0!==t&&(this._speed=t),this._speed}coordinates(t){let n,r,i=1,s=t-1;return void 0===t?(r=this._coordinates.map((l,c)=>this.coordinates(c)),r):(this.settings.center?(this.settings.rtl&&(i=-1,s=t+1),n=this._coordinates[t],n+=(this.width()-n+(this._coordinates[s]||0))/2*i):n=this._coordinates[s]||0,n=Math.ceil(n),n)}_duration(t,i,s){return 0===s?0:Math.min(Math.max(Math.abs(i-t),1),6)*Math.abs(+s||this.settings.smartSpeed)}to(t,i){let s=this.current(),n=null,r=t-this.relative(s),l=this.maximum(),c=0;const p=+(r>0)-+(r<0),_=this._items.length,b=this.minimum();this.settings.loop?(!this.settings.rewind&&Math.abs(r)>_/2&&(r+=-1*p*_),n=(((t=s+r)-b)%_+_)%_+b,n!==t&&n-r<=l&&n-r>0&&(s=n-r,t=n,c=30,this.reset(s),this.sendChanges())):this.settings.rewind?(l+=1,t=(t%l+l)%l):t=Math.max(b,Math.min(l,t)),setTimeout(()=>{this.speed(this._duration(s,t,i)),this.current(t),this.update()},c)}next(t){t=t||!1,this.to(this.relative(this.current())+1,t)}prev(t){t=t||!1,this.to(this.relative(this.current())-1,t)}onTransitionEnd(t){if(void 0!==t)return!1;this.leave("animating"),this._trigger("translated")}_viewport(){let t;return this._width?t=this._width:this.logger.log("Can not detect viewport width."),t}setItems(t){this._items=t}_defineSlidesData(){let t;this.slidesData&&this.slidesData.length&&(t=new Map,this.slidesData.forEach(i=>{i.load&&t.set(i.id,i.load)})),this.slidesData=this._items.map(i=>({id:`${i.id}`,isActive:!1,tplRef:i.tplRef,dataMerge:i.dataMerge,width:0,isCloned:!1,load:!!t&&t.get(i.id),hashFragment:i.dataHash}))}setCurSlideClasses(t){const i={active:t.isActive,center:t.isCentered,cloned:t.isCloned,animated:t.isAnimated,"owl-animated-in":t.isDefAnimatedIn,"owl-animated-out":t.isDefAnimatedOut};return this.settings.animateIn&&(i[this.settings.animateIn]=t.isCustomAnimatedIn),this.settings.animateOut&&(i[this.settings.animateOut]=t.isCustomAnimatedOut),i}_op(t,i,s){const n=this.settings.rtl;switch(i){case"<":return n?t>s:t<s;case">":return n?t<s:t>s;case">=":return n?t<=s:t>=s;case"<=":return n?t>=s:t<=s}}_trigger(t,i,s,n,r){switch(t){case"initialized":this._initializedCarousel$.next(t);break;case"change":this._changeSettingsCarousel$.next(i);break;case"changed":this._changedSettingsCarousel$.next(i);break;case"drag":this._dragCarousel$.next(t);break;case"dragged":this._draggedCarousel$.next(t);break;case"resize":this._resizeCarousel$.next(t);break;case"resized":this._resizedCarousel$.next(t);break;case"refresh":this._refreshCarousel$.next(t);break;case"refreshed":this._refreshedCarousel$.next(t);break;case"translate":this._translateCarousel$.next(t);break;case"translated":this._translatedCarousel$.next(t)}}enter(t){[t].concat(this._states.tags[t]||[]).forEach(i=>{void 0===this._states.current[i]&&(this._states.current[i]=0),this._states.current[i]++})}leave(t){[t].concat(this._states.tags[t]||[]).forEach(i=>{(0===this._states.current[i]||this._states.current[i])&&this._states.current[i]--})}register(t){t.type===at.State&&(this._states.tags[t.name]=this._states.tags[t.name]?this._states.tags[t.name].concat(t.tags):t.tags,this._states.tags[t.name]=this._states.tags[t.name].filter((i,s)=>this._states.tags[t.name].indexOf(i)===s))}_suppress(t){t.forEach(i=>{this._supress[i]=!0})}_release(t){t.forEach(i=>{delete this._supress[i]})}pointer(t){const i={x:null,y:null};return(t=(t=t.originalEvent||t||window.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(i.x=t.pageX,i.y=t.pageY):(i.x=t.clientX,i.y=t.clientY),i}_isNumeric(t){return!isNaN(parseFloat(t))}_isNumberOrBoolean(t){return this._isNumeric(t)||"boolean"==typeof t}_isNumberOrString(t){return this._isNumeric(t)||"string"==typeof t}_isStringOrBoolean(t){return"string"==typeof t||"boolean"==typeof t}difference(t,i){return null===t||null===i?{x:0,y:0}:{x:t.x-i.x,y:t.y-i.y}}static \u0275fac=function(i){return new(i||a)(e.KVO(J))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),ot=(()=>{class a{carouselService;navSubscription;_initialized=!1;_pages=[];_navData={disabled:!1,prev:{disabled:!1,htmlText:""},next:{disabled:!1,htmlText:""}};_dotsData={disabled:!1,dots:[]};constructor(t){this.carouselService=t,this.spyDataStreams()}ngOnDestroy(){this.navSubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getInitializedState().pipe((0,v.M)(r=>{this.initialize(),this._updateNavPages(),this.draw(),this.update(),this.carouselService.sendChanges()})),i=this.carouselService.getChangedState().pipe((0,A.p)(r=>"position"===r.property.name),(0,v.M)(r=>{this.update()})),s=this.carouselService.getRefreshedState().pipe((0,v.M)(()=>{this._updateNavPages(),this.draw(),this.update(),this.carouselService.sendChanges()})),n=(0,C.h)(t,i,s);this.navSubscription=n.subscribe(()=>{})}initialize(){this._navData.disabled=!0,this._navData.prev.htmlText=this.carouselService.settings.navText[0],this._navData.next.htmlText=this.carouselService.settings.navText[1],this._dotsData.disabled=!0,this.carouselService.navData=this._navData,this.carouselService.dotsData=this._dotsData}_updateNavPages(){let t,i,s;const n=this.carouselService.clones().length/2,r=n+this.carouselService.items().length,l=this.carouselService.maximum(!0),c=[],p=this.carouselService.settings;let _=p.center||p.autoWidth||p.dotsData?1:Math.floor(Number(p.dotsEach))||Math.floor(p.items);if(_=+_,"page"!==p.slideBy&&(p.slideBy=Math.min(+p.slideBy,p.items)),p.dots||"page"===p.slideBy)for(t=n,i=0,s=0;t<r;t++){if(i>=_||0===i){if(c.push({start:Math.min(l,t-n),end:t-n+_-1}),Math.min(l,t-n)===l)break;i=0,++s}i+=this.carouselService.mergers(this.carouselService.relative(t))}this._pages=c}draw(){let t;const i=this.carouselService.settings,s=this.carouselService.items(),n=s.length<=i.items;if(this._navData.disabled=!i.nav||n,this._dotsData.disabled=!i.dots||n,i.dots)if(t=this._pages.length-this._dotsData.dots.length,i.dotsData&&0!==t)this._dotsData.dots=[],s.forEach(r=>{this._dotsData.dots.push({active:!1,id:`dot-${r.id}`,innerContent:r.dotContent,showInnerContent:!0})});else if(t>0){const r=this._dotsData.dots.length>0?this._dotsData.dots.length:0;for(let l=0;l<t;l++)this._dotsData.dots.push({active:!1,id:`dot-${l+r}`,innerContent:"",showInnerContent:!1})}else t<0&&this._dotsData.dots.splice(t,Math.abs(t));this.carouselService.navData=this._navData,this.carouselService.dotsData=this._dotsData}update(){this._updateNavButtons(),this._updateDots()}_updateNavButtons(){const t=this.carouselService.settings,i=t.loop||t.rewind,s=this.carouselService.relative(this.carouselService.current());t.nav&&(this._navData.prev.disabled=!i&&s<=this.carouselService.minimum(!0),this._navData.next.disabled=!i&&s>=this.carouselService.maximum(!0)),this.carouselService.navData=this._navData}_updateDots(){let t;this.carouselService.settings.dots&&(this._dotsData.dots.forEach(i=>{!0===i.active&&(i.active=!1)}),t=this._current(),this._dotsData.dots.length&&(this._dotsData.dots[t].active=!0),this.carouselService.dotsData=this._dotsData)}_current(){const t=this.carouselService.relative(this.carouselService.current());let i;const s=this._pages.filter((n,r)=>n.start<=t&&n.end>=t).pop();return i=this._pages.findIndex(n=>n.start===s.start&&n.end===s.end),i}_getPosition(t){let i,s;const n=this.carouselService.settings;return"page"===n.slideBy?(i=this._current(),s=this._pages.length,t?++i:--i,i=this._pages[(i%s+s)%s].start):(i=this.carouselService.relative(this.carouselService.current()),s=this.carouselService.items().length,t?i+=+n.slideBy:i-=+n.slideBy),i}next(t){this.carouselService.to(this._getPosition(!0),t)}prev(t){this.carouselService.to(this._getPosition(!1),t)}to(t,i,s){let n;!s&&this._pages.length?(n=this._pages.length,this.carouselService.to(this._pages[(t%n+n)%n].start,i)):this.carouselService.to(t,i)}moveByDot(t){const i=this._dotsData.dots.findIndex(s=>t===s.id);this.to(i,this.carouselService.settings.dotsSpeed)}toSlideById(t){const i=this.carouselService.slidesData.findIndex(s=>s.id===t&&!1===s.isCloned);-1===i||i===this.carouselService.current()||this.carouselService.to(this.carouselService.relative(i),!1)}static \u0275fac=function(i){return new(i||a)(e.KVO(T))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})();const q=new e.nKC("WindowToken");class tt{get nativeWindow(){throw new Error("Not implemented.")}}const yt=[{provide:tt,useClass:(()=>{class a extends tt{constructor(){super()}get nativeWindow(){return window}static \u0275fac=function(i){return new(i||a)};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})()},{provide:q,useFactory:function mt(a,m){return(0,w.UE)(m)?a.nativeWindow:{setTimeout:(i,s)=>{},clearTimeout:i=>{}}},deps:[tt,e.Agw]}],et=new e.nKC("DocumentToken");class it{get nativeDocument(){throw new Error("Not implemented.")}}const St=[{provide:it,useClass:(()=>{class a extends it{constructor(){super()}get nativeDocument(){return document}static \u0275fac=function(i){return new(i||a)};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})()},{provide:et,useFactory:function xt(a,m){return(0,w.UE)(m)?a.nativeDocument:{hidden:!1,visibilityState:"visible"}},deps:[it,e.Agw]}];let lt=(()=>{class a{carouselService;ngZone;autoplaySubscription;_timeout=null;_paused=!1;_isArtificialAutoplayTimeout;_isAutoplayStopped=!1;get isAutoplayStopped(){return this._isAutoplayStopped}set isAutoplayStopped(t){this._isAutoplayStopped=t}winRef;docRef;constructor(t,i,s,n){this.carouselService=t,this.ngZone=n,this.winRef=i,this.docRef=s,this.spyDataStreams()}ngOnDestroy(){this.autoplaySubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getInitializedState().pipe((0,v.M)(()=>{this.carouselService.settings.autoplay&&this.play()})),i=this.carouselService.getChangedState().pipe((0,v.M)(r=>{this._handleChangeObservable(r)})),s=this.carouselService.getResizedState().pipe((0,v.M)(()=>{this.carouselService.settings.autoplay&&!this._isAutoplayStopped?this.play():this.stop()})),n=(0,C.h)(t,i,s);this.autoplaySubscription=n.subscribe(()=>{})}play(t,i){this._paused&&(this._paused=!1,this._setAutoPlayInterval(this.carouselService.settings.autoplayMouseleaveTimeout)),!this.carouselService.is("rotating")&&(this.carouselService.enter("rotating"),this._setAutoPlayInterval())}_getNextTimeout(t,i){return this._timeout&&this.winRef.clearTimeout(this._timeout),this._isArtificialAutoplayTimeout=!!t,this.ngZone.runOutsideAngular(()=>this.winRef.setTimeout(()=>{this.ngZone.run(()=>{this._paused||this.carouselService.is("busy")||this.carouselService.is("interacting")||this.docRef.hidden||this.carouselService.next(i||this.carouselService.settings.autoplaySpeed)})},t||this.carouselService.settings.autoplayTimeout))}_setAutoPlayInterval(t){this._timeout=this._getNextTimeout(t)}stop(){this.carouselService.is("rotating")&&(this._paused=!0,this.winRef.clearTimeout(this._timeout),this.carouselService.leave("rotating"))}pause(){this.carouselService.is("rotating")&&(this._paused=!0)}_handleChangeObservable(t){"settings"===t.property.name?this.carouselService.settings.autoplay?this.play():this.stop():"position"===t.property.name&&this.carouselService.settings.autoplay&&this._setAutoPlayInterval()}_playAfterTranslated(){(0,z.of)("translated").pipe((0,P.n)(t=>this.carouselService.getTranslatedState()),(0,R.$)(),(0,A.p)(()=>this._isArtificialAutoplayTimeout),(0,v.M)(()=>this._setAutoPlayInterval())).subscribe(()=>{})}startPausing(){this.carouselService.settings.autoplayHoverPause&&this.carouselService.is("rotating")&&this.pause()}startPlayingMouseLeave(){this.carouselService.settings.autoplayHoverPause&&this.carouselService.is("rotating")&&(this.play(),this._playAfterTranslated())}startPlayingTouchEnd(){this.carouselService.settings.autoplayHoverPause&&this.carouselService.is("rotating")&&(this.play(),this._playAfterTranslated())}static \u0275fac=function(i){return new(i||a)(e.KVO(T),e.KVO(q),e.KVO(et),e.KVO(e.SKi))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),ct=(()=>{class a{carouselService;lazyLoadSubscription;constructor(t){this.carouselService=t,this.spyDataStreams()}ngOnDestroy(){this.lazyLoadSubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getInitializedState().pipe((0,v.M)(()=>{const r=this.carouselService.settings&&!this.carouselService.settings.lazyLoad;this.carouselService.slidesData.forEach(l=>l.load=!!r)})),i=this.carouselService.getChangeState(),s=this.carouselService.getResizedState(),n=(0,C.h)(t,i,s).pipe((0,v.M)(r=>this._defineLazyLoadSlides(r)));this.lazyLoadSubscription=n.subscribe(()=>{})}_defineLazyLoadSlides(t){if(this.carouselService.settings&&this.carouselService.settings.lazyLoad&&(t.property&&"position"===t.property.name||"initialized"===t||"resized"===t)){const i=this.carouselService.settings,s=this.carouselService.clones().length;let n=i.center&&Math.ceil(i.items/2)||i.items,r=i.center&&-1*n||0,l=(t.property&&void 0!==t.property.value?t.property.value:this.carouselService.current())+r;for(i.lazyLoadEager>0&&(n+=i.lazyLoadEager,i.loop&&(l-=i.lazyLoadEager,n++));r++<n;)this._load(s/2+this.carouselService.relative(l)),s&&this.carouselService.clones(this.carouselService.relative(l)).forEach(c=>this._load(c)),l++}}_load(t){this.carouselService.slidesData[t].load||(this.carouselService.slidesData[t].load=!0)}static \u0275fac=function(i){return new(i||a)(e.KVO(T))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),st=(()=>{class a{carouselService;animateSubscription;swapping=!0;previous=void 0;next=void 0;constructor(t){this.carouselService=t,this.spyDataStreams()}ngOnDestroy(){this.animateSubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getChangeState().pipe((0,v.M)(p=>{"position"===p.property.name&&(this.previous=this.carouselService.current(),this.next=p.property.value)})),i=this.carouselService.getDragState(),s=this.carouselService.getDraggedState(),n=this.carouselService.getTranslatedState(),r=(0,C.h)(i,s,n).pipe((0,v.M)(p=>this.swapping="translated"===p)),l=this.carouselService.getTranslateState().pipe((0,v.M)(p=>{this.swapping&&(this.carouselService._options.animateOut||this.carouselService._options.animateIn)&&this._swap()})),c=(0,C.h)(t,l,r).pipe();this.animateSubscription=c.subscribe(()=>{})}_swap(){if(1!==this.carouselService.settings.items)return;let t;this.carouselService.speed(0);const i=this.carouselService.slidesData[this.previous],s=this.carouselService.slidesData[this.next],n=this.carouselService.settings.animateIn,r=this.carouselService.settings.animateOut;this.carouselService.current()!==this.previous&&(r&&(t=+this.carouselService.coordinates(this.previous)-+this.carouselService.coordinates(this.next),this.carouselService.slidesData.forEach(l=>{l.id===i.id&&(l.left=`${t}px`,l.isAnimated=!0,l.isDefAnimatedOut=!0,l.isCustomAnimatedOut=!0)})),n&&this.carouselService.slidesData.forEach(l=>{l.id===s.id&&(l.isAnimated=!0,l.isDefAnimatedIn=!0,l.isCustomAnimatedIn=!0)}))}clear(t){this.carouselService.slidesData.forEach(i=>{i.id===t&&(i.left="",i.isAnimated=!1,i.isDefAnimatedOut=!1,i.isCustomAnimatedOut=!1,i.isDefAnimatedIn=!1,i.isCustomAnimatedIn=!1,i.classes=this.carouselService.setCurSlideClasses(i))}),this.carouselService.onTransitionEnd()}static \u0275fac=function(i){return new(i||a)(e.KVO(T))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),dt=(()=>{class a{carouselService;autoHeightSubscription;constructor(t){this.carouselService=t,this.spyDataStreams()}ngOnDestroy(){this.autoHeightSubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getInitializedState().pipe((0,v.M)(r=>{this.carouselService.settings.autoHeight?this.update():this.carouselService.slidesData.forEach(l=>l.heightState="full")})),i=this.carouselService.getChangedState().pipe((0,v.M)(r=>{this.carouselService.settings.autoHeight&&"position"===r.property.name&&this.update()})),s=this.carouselService.getRefreshedState().pipe((0,v.M)(r=>{this.carouselService.settings.autoHeight&&this.update()})),n=(0,C.h)(t,i,s);this.autoHeightSubscription=n.subscribe(()=>{})}update(){const t=this.carouselService.settings.items;let i=this.carouselService.current(),s=i+t;this.carouselService.settings.center&&(i=t%2==1?i-(t-1)/2:i-t/2,s=t%2==1?i+t:i+t+1),this.carouselService.slidesData.forEach((n,r)=>{n.heightState=r>=i&&r<s?"full":"nulled"})}static \u0275fac=function(i){return new(i||a)(e.KVO(T))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),ht=(()=>{class a{carouselService;route;router;hashSubscription;currentHashFragment;constructor(t,i,s){this.carouselService=t,this.route=i,this.router=s,this.spyDataStreams(),this.route||(this.route={fragment:(0,z.of)("no route").pipe((0,I.s)(1))}),this.router||(this.router={navigate:(n,r)=>{}})}ngOnDestroy(){this.hashSubscription.unsubscribe()}spyDataStreams(){const t=this.carouselService.getInitializedState().pipe((0,v.M)(()=>this.listenToRoute())),i=this.carouselService.getChangedState().pipe((0,v.M)(n=>{if(this.carouselService.settings.URLhashListener&&"position"===n.property.name){const r=this.carouselService.current(),l=this.carouselService.slidesData[r].hashFragment;if(!l||l===this.currentHashFragment)return;this.router.navigate(["./"],{fragment:l,relativeTo:this.route})}})),s=(0,C.h)(t,i);this.hashSubscription=s.subscribe(()=>{})}rewind(t){const i=this.carouselService.slidesData.findIndex(s=>s.hashFragment===t&&!1===s.isCloned);-1===i||i===this.carouselService.current()||this.carouselService.to(this.carouselService.relative(i),!1)}listenToRoute(){this.route.fragment.pipe((0,$.i)("URLHash"===this.carouselService.settings.startPosition?0:2)).subscribe(i=>{this.currentHashFragment=i,this.rewind(i)})}static \u0275fac=function(i){return new(i||a)(e.KVO(T),e.KVO(j.nX,8),e.KVO(j.Ix,8))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),Mt=0,ut=(()=>{class a{tplRef;id="owl-slide-"+Mt++;_dataMerge=1;set dataMerge(t){this._dataMerge=this.isNumeric(t)?t:1}get dataMerge(){return this._dataMerge}width=0;dotContent="";dataHash="";constructor(t){this.tplRef=t}isNumeric(t){return!isNaN(parseFloat(t))}static \u0275fac=function(i){return new(i||a)(e.rXU(e.C4Q))};static \u0275dir=e.FsC({type:a,selectors:[["ng-template","carouselSlide",""]],inputs:{id:"id",dataMerge:"dataMerge",width:"width",dotContent:"dotContent",dataHash:"dataHash"}})}return a})(),gt=(()=>{class a{resizeObservable$;get onResize$(){return this.resizeObservable$}constructor(t,i){this.resizeObservable$=(0,w.UE)(i)?(0,L.R)(t,"resize"):(new y.B).asObservable()}static \u0275fac=function(i){return new(i||a)(e.KVO(q),e.KVO(e.Agw))};static \u0275prov=e.jDH({token:a,factory:a.\u0275fac})}return a})(),Dt=(()=>{class a{zone;el;renderer;carouselService;animateService;owlDraggable;stageData;slidesData;listenerMouseMove;listenerTouchMove;listenerOneMouseMove;listenerOneTouchMove;listenerMouseUp;listenerTouchEnd;listenerOneClick;listenerATag;_drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null,active:!1,moving:!1};_oneDragMove$=new y.B;_oneMoveSubsription;preparePublicSlide=t=>{const i={...t};return delete i.tplRef,i};constructor(t,i,s,n,r){this.zone=t,this.el=i,this.renderer=s,this.carouselService=n,this.animateService=r}onMouseDown(t){this.owlDraggable.isMouseDragable&&this._onDragStart(t)}onTouchStart(t){if(t.targetTouches.length>=2)return!1;this.owlDraggable.isTouchDragable&&this._onDragStart(t)}onTouchCancel(t){this._onDragEnd(t)}onDragStart(){if(this.owlDraggable.isMouseDragable)return!1}onSelectStart(){if(this.owlDraggable.isMouseDragable)return!1}ngOnInit(){this._oneMoveSubsription=this._oneDragMove$.pipe((0,R.$)()).subscribe(()=>{this._sendChanges()})}ngOnDestroy(){this._oneMoveSubsription.unsubscribe()}bindOneMouseTouchMove=t=>{this._oneMouseTouchMove(t)};bindOnDragMove=t=>{this._onDragMove(t)};bindOnDragEnd=t=>{this._onDragEnd(t)};_onDragStart(t){let i=null;3!==t.which&&(i=this._prepareDragging(t),this._drag.time=(new Date).getTime(),this._drag.target=t.target,this._drag.stage.start=i,this._drag.stage.current=i,this._drag.pointer=this._pointer(t),this.listenerMouseUp=this.renderer.listen(document,"mouseup",this.bindOnDragEnd),this.listenerTouchEnd=this.renderer.listen(document,"touchend",this.bindOnDragEnd),this.zone.runOutsideAngular(()=>{this.listenerOneMouseMove=this.renderer.listen(document,"mousemove",this.bindOneMouseTouchMove),this.listenerOneTouchMove=this.renderer.listen(document,"touchmove",this.bindOneMouseTouchMove)}))}_oneMouseTouchMove(t){const i=this._difference(this._drag.pointer,this._pointer(t));this.listenerATag&&this.listenerATag(),!(Math.abs(i.x)<3&&Math.abs(i.y)<3&&this._is("valid"))&&(Math.abs(i.x)<3&&Math.abs(i.x)<Math.abs(i.y)&&this._is("valid")||(this.listenerOneMouseMove(),this.listenerOneTouchMove(),this._drag.moving=!0,this.blockClickAnchorInDragging(t),this.listenerMouseMove=this.renderer.listen(document,"mousemove",this.bindOnDragMove),this.listenerTouchMove=this.renderer.listen(document,"touchmove",this.bindOnDragMove),t.preventDefault(),this._enterDragging(),this._oneDragMove$.next(t)))}blockClickAnchorInDragging(t){let i=t.target;for(;i&&!(i instanceof HTMLAnchorElement);)i=i.parentElement;i instanceof HTMLAnchorElement&&(this.listenerATag=this.renderer.listen(i,"click",()=>!1))}_onDragMove(t){let i;const s=this.carouselService.defineNewCoordsDrag(t,this._drag);!1!==s&&(i=s,t.preventDefault(),this._drag.stage.current=i,this._animate(i.x-this._drag.stage.start.x))}_animate(t){this.renderer.setStyle(this.el.nativeElement.children[0],"transform",`translate3d(${t}px,0px,0px`),this.renderer.setStyle(this.el.nativeElement.children[0],"transition","0s")}_onDragEnd(t){this.carouselService.owlDOMData.isGrab=!1,this.listenerOneMouseMove(),this.listenerOneTouchMove(),this._drag.moving&&(this.renderer.setStyle(this.el.nativeElement.children[0],"transform",""),this.renderer.setStyle(this.el.nativeElement.children[0],"transition",this.carouselService.speed(+this.carouselService.settings.dragEndSpeed||this.carouselService.settings.smartSpeed)/1e3+"s"),this._finishDragging(t),this.listenerMouseMove(),this.listenerTouchMove()),this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null,active:!1,moving:!1},this.listenerMouseUp(),this.listenerTouchEnd()}_prepareDragging(t){return this.carouselService.prepareDragging(t)}_oneClickHandler=()=>{this.listenerOneClick=this.renderer.listen(this._drag.target,"click",()=>!1),this.listenerOneClick()};_finishDragging(t){this.carouselService.finishDragging(t,this._drag,this._oneClickHandler)}_pointer(t){return this.carouselService.pointer(t)}_difference(t,i){return this.carouselService.difference(t,i)}_is(t){return this.carouselService.is(t)}_enter(t){this.carouselService.enter(t)}_sendChanges(){this.carouselService.sendChanges()}onTransitionEnd(){this.carouselService.onTransitionEnd()}_enterDragging(){this.carouselService.enterDragging()}clear(t){this.animateService.clear(t)}static \u0275fac=function(i){return new(i||a)(e.rXU(e.SKi),e.rXU(e.aKT),e.rXU(e.sFG),e.rXU(T),e.rXU(st))};static \u0275cmp=e.VBU({type:a,selectors:[["owl-stage"]],hostBindings:function(i,s){1&i&&e.bIt("mousedown",function(r){return s.onMouseDown(r)})("touchstart",function(r){return s.onTouchStart(r)})("touchcancel",function(r){return s.onTouchCancel(r)})("dragstart",function(){return s.onDragStart()})("selectstart",function(){return s.onSelectStart()})},inputs:{owlDraggable:"owlDraggable",stageData:"stageData",slidesData:"slidesData"},decls:3,vars:8,consts:[[1,"owl-stage",3,"transitionend","ngStyle"],[4,"ngFor","ngForOf"],[1,"owl-item",3,"animationend","ngClass","ngStyle"],[4,"ngIf"],[3,"ngTemplateOutlet","ngTemplateOutletContext"]],template:function(i,s){1&i&&(e.j41(0,"div")(1,"div",0),e.bIt("transitionend",function(){return s.onTransitionEnd()}),e.DNE(2,Z,3,9,"ng-container",1),e.k0s()()),2&i&&(e.R7$(),e.Y8G("ngStyle",e.s1E(2,X,s.stageData.width+"px",s.stageData.transform,s.stageData.transition,s.stageData.paddingL?s.stageData.paddingL+"px":"",s.stageData.paddingR?s.stageData.paddingR+"px":"")),e.R7$(),e.Y8G("ngForOf",s.slidesData))},dependencies:[w.YU,w.Sq,w.bT,w.T3,w.B3],encapsulation:2,data:{animation:[(0,D.hZ)("autoHeight",[(0,D.wk)("nulled",(0,D.iF)({height:0})),(0,D.wk)("full",(0,D.iF)({height:"*"})),(0,D.kY)("full => nulled",[(0,D.i0)("700ms 350ms")]),(0,D.kY)("nulled => full",[(0,D.i0)(350)])])]}})}return a})(),Ot=(()=>{class a{el;resizeService;carouselService;navigationService;autoplayService;lazyLoadService;animateService;autoHeightService;hashService;logger;changeDetectorRef;slides;translated=new e.bkB;dragging=new e.bkB;change=new e.bkB;changed=new e.bkB;initialized=new e.bkB;carouselWindowWidth;resizeSubscription;_allObservSubscription;_slidesChangesSubscription;owlDOMData;stageData;slidesData=[];navData;dotsData;slidesOutputData;carouselLoaded=!1;options;prevOptions;_viewCurSettings$;_translatedCarousel$;_draggingCarousel$;_changeCarousel$;_changedCarousel$;_initializedCarousel$;_carouselMerge$;docRef;constructor(t,i,s,n,r,l,c,p,_,b,H,kt){this.el=t,this.resizeService=i,this.carouselService=s,this.navigationService=n,this.autoplayService=r,this.lazyLoadService=l,this.animateService=c,this.autoHeightService=p,this.hashService=_,this.logger=b,this.changeDetectorRef=H,this.docRef=kt}onVisibilityChange(t){if(this.carouselService.settings.autoplay)switch(this.docRef.visibilityState){case"visible":!this.autoplayService.isAutoplayStopped&&this.autoplayService.play();break;case"hidden":this.autoplayService.pause()}}ngOnInit(){this.spyDataStreams(),this.carouselWindowWidth=this.el.nativeElement.querySelector(".owl-carousel").clientWidth}ngOnChanges(){this.prevOptions!==this.options&&(this.prevOptions&&this.slides?.toArray().length?(this.carouselService.setup(this.carouselWindowWidth,this.slides.toArray(),this.options),this.carouselService.initialize(this.slides.toArray())):this.prevOptions&&!this.slides?.toArray().length?(this.carouselLoaded=!1,this.logger.log("There are no slides to show. So the carousel won't be re-rendered")):this.carouselLoaded=!1,this.prevOptions=this.options)}ngAfterContentInit(){this.slides.toArray().length?(this.carouselService.setup(this.carouselWindowWidth,this.slides.toArray(),this.options),this.carouselService.initialize(this.slides.toArray()),this._winResizeWatcher()):this.logger.log("There are no slides to show. So the carousel won't be rendered"),this._slidesChangesSubscription=this.slides.changes.pipe((0,v.M)(t=>{this.carouselService.setup(this.carouselWindowWidth,t.toArray(),this.options),this.carouselService.initialize(t.toArray()),t.toArray().length||(this.carouselLoaded=!1),t.toArray().length&&!this.resizeSubscription&&this._winResizeWatcher()})).subscribe(()=>{})}ngOnDestroy(){this.resizeSubscription&&this.resizeSubscription.unsubscribe(),this._slidesChangesSubscription&&this._slidesChangesSubscription.unsubscribe(),this._allObservSubscription&&this._allObservSubscription.unsubscribe()}spyDataStreams(){this._viewCurSettings$=this.carouselService.getViewCurSettings().pipe((0,v.M)(t=>{this.owlDOMData=t.owlDOMData,this.stageData=t.stageData,this.slidesData=t.slidesData,this.carouselLoaded||(this.carouselLoaded=!0),this.navData=t.navData,this.dotsData=t.dotsData,this.changeDetectorRef.markForCheck()})),this._initializedCarousel$=this.carouselService.getInitializedState().pipe((0,v.M)(()=>{this.gatherTranslatedData(),this.initialized.emit(this.slidesOutputData)})),this._translatedCarousel$=this.carouselService.getTranslatedState().pipe((0,v.M)(()=>{this.gatherTranslatedData(),this.translated.emit(this.slidesOutputData)})),this._changeCarousel$=this.carouselService.getChangeState().pipe((0,v.M)(()=>{this.gatherTranslatedData(),this.change.emit(this.slidesOutputData)})),this._changedCarousel$=this.carouselService.getChangeState().pipe((0,P.n)(t=>{const i=(0,z.of)(t).pipe((0,A.p)(()=>"position"===t.property.name),(0,P.n)(()=>(0,V.H)(this.slidesData)),(0,$.i)(t.property.value),(0,I.s)(this.carouselService.settings.items),(0,S.T)(s=>{const n=this.carouselService.clonedIdPrefix,r=s.id.indexOf(n)>=0?s.id.slice(n.length):s.id;return{...s,id:r,isActive:!0}}),function U(){return(0,M.N)((a,m)=>{(function F(a,m){return(0,M.N)((0,O.S)(a,m,arguments.length>=2,!1,!0))})(N,[])(a).subscribe(m)})}(),(0,S.T)(s=>({slides:s,startPosition:this.carouselService.relative(t.property.value)})));return(0,C.h)(i)}),(0,v.M)(t=>{this.gatherTranslatedData(),this.changed.emit(t.slides.length?t:this.slidesOutputData)})),this._draggingCarousel$=this.carouselService.getDragState().pipe((0,v.M)(()=>{this.gatherTranslatedData(),this.dragging.emit({dragging:!0,data:this.slidesOutputData})}),(0,P.n)(()=>this.carouselService.getDraggedState().pipe((0,S.T)(()=>!!this.carouselService.is("animating")))),(0,P.n)(t=>t?this.carouselService.getTranslatedState().pipe((0,R.$)()):(0,z.of)("not animating")),(0,v.M)(()=>{this.dragging.emit({dragging:!1,data:this.slidesOutputData})})),this._carouselMerge$=(0,C.h)(this._viewCurSettings$,this._translatedCarousel$,this._draggingCarousel$,this._changeCarousel$,this._changedCarousel$,this._initializedCarousel$),this._allObservSubscription=this._carouselMerge$.subscribe(()=>{})}_winResizeWatcher(){Object.keys(this.carouselService._options.responsive).length&&(this.resizeSubscription=this.resizeService.onResize$.pipe((0,A.p)(()=>this.carouselWindowWidth!==this.el.nativeElement.querySelector(".owl-carousel").clientWidth),(0,G.c)(this.carouselService.settings.responsiveRefreshRate)).subscribe(()=>{this.carouselService.onResize(this.el.nativeElement.querySelector(".owl-carousel").clientWidth),this.carouselWindowWidth=this.el.nativeElement.querySelector(".owl-carousel").clientWidth}))}onTransitionEnd(){this.carouselService.onTransitionEnd()}next(){this.carouselLoaded&&this.navigationService.next(this.carouselService.settings.navSpeed)}prev(){this.carouselLoaded&&this.navigationService.prev(this.carouselService.settings.navSpeed)}moveByDot(t){this.carouselLoaded&&this.navigationService.moveByDot(t)}to(t){this.carouselLoaded&&this.navigationService.toSlideById(t)}gatherTranslatedData(){let t;const i=this.carouselService.clonedIdPrefix,s=this.slidesData.filter(n=>!0===n.isActive).map(n=>({id:n.id.indexOf(i)>=0?n.id.slice(i.length):n.id,width:n.width,marginL:n.marginL,marginR:n.marginR,center:n.isCentered}));t=this.carouselService.relative(this.carouselService.current()),this.slidesOutputData={startPosition:t,slides:s}}startPausing(){this.autoplayService.startPausing()}startPlayML(){this.autoplayService.startPlayingMouseLeave()}startPlayTE(){this.autoplayService.startPlayingTouchEnd()}stopAutoplay(){this.autoplayService.isAutoplayStopped=!0,this.autoplayService.stop()}startAutoplay(){this.autoplayService.isAutoplayStopped=!1,this.autoplayService.play()}static \u0275fac=function(i){return new(i||a)(e.rXU(e.aKT),e.rXU(gt),e.rXU(T),e.rXU(ot),e.rXU(lt),e.rXU(ct),e.rXU(st),e.rXU(dt),e.rXU(ht),e.rXU(J),e.rXU(e.gRc),e.rXU(et))};static \u0275cmp=e.VBU({type:a,selectors:[["owl-carousel-o"]],contentQueries:function(i,s,n){if(1&i&&e.wni(n,ut,4),2&i){let r;e.mGM(r=e.lsd())&&(s.slides=r)}},hostBindings:function(i,s){1&i&&e.bIt("visibilitychange",function(r){return s.onVisibilityChange(r)},!1,e.EBC)},inputs:{options:"options"},outputs:{translated:"translated",dragging:"dragging",change:"change",changed:"changed",initialized:"initialized"},features:[e.Jv_([ot,lt,T,ct,st,dt,ht]),e.OA$],decls:4,vars:9,consts:[["owlCarousel",""],[1,"owl-carousel","owl-theme",3,"mouseover","mouseleave","touchstart","touchend","ngClass"],["class","owl-stage-outer",4,"ngIf"],[4,"ngIf"],[1,"owl-stage-outer"],[3,"owlDraggable","stageData","slidesData"],[1,"owl-nav",3,"ngClass"],[1,"owl-prev",3,"click","ngClass","innerHTML"],[1,"owl-next",3,"click","ngClass","innerHTML"],[1,"owl-dots",3,"ngClass"],["class","owl-dot",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"owl-dot",3,"click","ngClass"],[3,"innerHTML"]],template:function(i,s){if(1&i){const n=e.RV6();e.j41(0,"div",1,0),e.bIt("mouseover",function(){return e.eBV(n),e.Njj(s.startPausing())})("mouseleave",function(){return e.eBV(n),e.Njj(s.startPlayML())})("touchstart",function(){return e.eBV(n),e.Njj(s.startPausing())})("touchend",function(){return e.eBV(n),e.Njj(s.startPlayTE())}),e.DNE(2,d,2,6,"div",2)(3,k,6,15,"ng-container",3),e.k0s()}2&i&&(e.Y8G("ngClass",e.s1E(3,u,null==s.owlDOMData?null:s.owlDOMData.rtl,null==s.owlDOMData?null:s.owlDOMData.isLoaded,null==s.owlDOMData?null:s.owlDOMData.isResponsive,null==s.owlDOMData?null:s.owlDOMData.isMouseDragable,null==s.owlDOMData?null:s.owlDOMData.isGrab)),e.R7$(2),e.Y8G("ngIf",s.carouselLoaded),e.R7$(),e.Y8G("ngIf",s.slides.toArray().length))},dependencies:[w.YU,w.Sq,w.bT,Dt],styles:[".owl-theme[_ngcontent-%COMP%]{display:block}"],changeDetection:0})}return a})(),Pt=(()=>{class a{static \u0275fac=function(i){return new(i||a)};static \u0275mod=e.$C({type:a});static \u0275inj=e.G2t({providers:[yt,gt,St,J],imports:[w.MD]})}return a})()}}]);