{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction ShopByCategoryComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵelement(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopByCategoryComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵtemplate(2, ShopByCategoryComponent_div_8_div_2_Template, 6, 0, \"div\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction ShopByCategoryComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ShopByCategoryComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h3\", 27);\n    i0.ɵɵtext(3, \"No Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 28);\n    i0.ɵɵtext(5, \"Product categories will appear here when available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopByCategoryComponent_div_11_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"ion-icon\", 52);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Trending\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopByCategoryComponent_div_11_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r5.discount, \"% OFF \");\n  }\n}\nfunction ShopByCategoryComponent_div_11_div_7_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sub_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r6, \" \");\n  }\n}\nfunction ShopByCategoryComponent_div_11_div_7_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", category_r5.subcategories.length - 3, \" \");\n  }\n}\nfunction ShopByCategoryComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_11_div_7_Template_div_click_0_listener() {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryClick(category_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵtemplate(3, ShopByCategoryComponent_div_11_div_7_div_3_Template, 4, 0, \"div\", 40)(4, ShopByCategoryComponent_div_11_div_7_div_4_Template, 2, 1, \"div\", 41);\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"ion-icon\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 44)(8, \"h3\", 45);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 46);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 47);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 48);\n    i0.ɵɵtemplate(15, ShopByCategoryComponent_div_11_div_7_span_15_Template, 2, 1, \"span\", 49)(16, ShopByCategoryComponent_div_11_div_7_span_16_Template, 2, 1, \"span\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"trending\", category_r5.trending);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r5.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.trending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.discount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatProductCount(category_r5.productCount), \" Products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r5.subcategories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.subcategories.length > 3);\n  }\n}\nfunction ShopByCategoryComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵlistener(\"mouseenter\", function ShopByCategoryComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function ShopByCategoryComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 35);\n    i0.ɵɵtemplate(7, ShopByCategoryComponent_div_11_div_7_Template, 17, 11, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories)(\"ngForTrackBy\", ctx_r1.trackByCategoryId);\n  }\n}\nfunction ShopByCategoryComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h3\", 27);\n    i0.ɵɵtext(3, \"No Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 28);\n    i0.ɵɵtext(5, \"Check back later for product categories\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ShopByCategoryComponent {\n  constructor(router) {\n    this.router = router;\n    this.categories = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each category card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4500; // 4.5 seconds for categories\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    try {\n      this.loadCategories();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n    } catch (error) {\n      console.error('Error initializing shop-by-category component:', error);\n      this.isLoading = false;\n      this.error = 'Failed to initialize component';\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadCategories() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for shop categories\n        _this.categories = [{\n          id: '1',\n          name: 'Women\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n          productCount: 15420,\n          description: 'Trendy outfits for every occasion',\n          trending: true,\n          discount: 30,\n          subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n        }, {\n          id: '2',\n          name: 'Men\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n          productCount: 12850,\n          description: 'Stylish clothing for modern men',\n          trending: true,\n          discount: 25,\n          subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n        }, {\n          id: '3',\n          name: 'Footwear',\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n          productCount: 8960,\n          description: 'Step up your shoe game',\n          trending: false,\n          discount: 20,\n          subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n        }, {\n          id: '4',\n          name: 'Accessories',\n          image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n          productCount: 6750,\n          description: 'Complete your look',\n          trending: true,\n          subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n        }, {\n          id: '5',\n          name: 'Kids Fashion',\n          image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n          productCount: 4320,\n          description: 'Adorable styles for little ones',\n          trending: false,\n          discount: 35,\n          subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n        }, {\n          id: '6',\n          name: 'Sports & Fitness',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n          productCount: 5680,\n          description: 'Gear up for your workout',\n          trending: true,\n          subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n        }, {\n          id: '7',\n          name: 'Beauty & Personal Care',\n          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n          productCount: 7890,\n          description: 'Look and feel your best',\n          trending: false,\n          discount: 15,\n          subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n        }, {\n          id: '8',\n          name: 'Home & Living',\n          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n          productCount: 3450,\n          description: 'Style your space',\n          trending: false,\n          subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnCategoriesLoad();\n      } catch (error) {\n        console.error('Error loading categories:', error);\n        _this.error = 'Failed to load categories';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/category', category.id]);\n  }\n  formatProductCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadCategories();\n  }\n  trackByCategoryId(index, category) {\n    return category.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.categories.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 160;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 180;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 200;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when categories load\n  updateSliderOnCategoriesLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  static {\n    this.ɵfac = function ShopByCategoryComponent_Factory(t) {\n      return new (t || ShopByCategoryComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopByCategoryComponent,\n      selectors: [[\"app-shop-by-category\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"shop-categories-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"grid\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"categories-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-category-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-category-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"empty-state\"], [\"name\", \"grid-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"categories-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"categories-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"categories-slider\"], [\"class\", \"category-card\", 3, \"trending\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"category-card\", 3, \"click\"], [1, \"category-image-container\"], [\"loading\", \"lazy\", 1, \"category-image\", 3, \"src\", \"alt\"], [\"class\", \"trending-badge\", 4, \"ngIf\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"category-overlay\"], [\"name\", \"arrow-forward\", 1, \"explore-icon\"], [1, \"category-info\"], [1, \"category-name\"], [1, \"category-description\"], [1, \"product-count\"], [1, \"subcategories\"], [\"class\", \"subcategory-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-subcategories\", 4, \"ngIf\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [1, \"discount-badge\"], [1, \"subcategory-tag\"], [1, \"more-subcategories\"], [1, \"empty-container\"]],\n      template: function ShopByCategoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Shop by Category \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Explore our collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, ShopByCategoryComponent_div_8_Template, 3, 2, \"div\", 6)(9, ShopByCategoryComponent_div_9_Template, 7, 1, \"div\", 7)(10, ShopByCategoryComponent_div_10_Template, 6, 0, \"div\", 8)(11, ShopByCategoryComponent_div_11_Template, 8, 6, \"div\", 9)(12, ShopByCategoryComponent_div_12_Template, 6, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.categories.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.categories.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon],\n      styles: [\".shop-categories-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  overflow: hidden;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.categories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.categories-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.categories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  width: 180px;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.category-card[_ngcontent-%COMP%]:hover   .category-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.category-card.trending[_ngcontent-%COMP%] {\\n  border: 2px solid #ff6b6b;\\n}\\n.category-card.trending[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n}\\n\\n.category-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 120px;\\n  overflow: hidden;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 700;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]   .explore-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n\\n.category-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 6px 0;\\n  line-height: 1.2;\\n}\\n.category-info[_ngcontent-%COMP%]   .category-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n}\\n.category-info[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #6c5ce7;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n\\n.subcategories[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n}\\n.subcategories[_ngcontent-%COMP%]   .subcategory-tag[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n  background: rgba(108, 92, 231, 0.1);\\n  color: #6c5ce7;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n}\\n.subcategories[_ngcontent-%COMP%]   .more-subcategories[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n  color: #999;\\n  font-weight: 500;\\n}\\n\\n.empty-state[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(25% - 15px);\\n    width: calc(25% - 15px);\\n    max-width: 200px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(20% - 16px);\\n    width: calc(20% - 16px);\\n    max-width: 190px;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(16.666% - 17px);\\n    width: calc(16.666% - 17px);\\n    max-width: 180px;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(14.285% - 18px);\\n    width: calc(14.285% - 18px);\\n    max-width: 170px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .categories-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .categories-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .categories-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 160px;\\n    width: 160px;\\n  }\\n  .category-image-container[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .category-info[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n    flex: 0 0 150px;\\n    width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ShopByCategoryComponent_div_8_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "ShopByCategoryComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "category_r5", "discount", "sub_r6", "subcategories", "length", "ShopByCategoryComponent_div_11_div_7_Template_div_click_0_listener", "_r4", "$implicit", "onCategoryClick", "ShopByCategoryComponent_div_11_div_7_div_3_Template", "ShopByCategoryComponent_div_11_div_7_div_4_Template", "ShopByCategoryComponent_div_11_div_7_span_15_Template", "ShopByCategoryComponent_div_11_div_7_span_16_Template", "ɵɵclassProp", "trending", "image", "ɵɵsanitizeUrl", "name", "description", "formatProductCount", "productCount", "slice", "ShopByCategoryComponent_div_11_Template_button_click_1_listener", "_r3", "slidePrev", "ShopByCategoryComponent_div_11_Template_button_click_3_listener", "slideNext", "ShopByCategoryComponent_div_11_Template_div_mouseenter_5_listener", "pauseAutoSlide", "ShopByCategoryComponent_div_11_Template_div_mouseleave_5_listener", "resumeAutoSlide", "ShopByCategoryComponent_div_11_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "categories", "trackByCategoryId", "ShopByCategoryComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadCategories", "updateResponsiveSettings", "setupResizeListener", "console", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "updateSliderOnCategoriesLoad", "category", "navigate", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ShopByCategoryComponent_Template", "rf", "ctx", "ShopByCategoryComponent_div_8_Template", "ShopByCategoryComponent_div_9_Template", "ShopByCategoryComponent_div_10_Template", "ShopByCategoryComponent_div_11_Template", "ShopByCategoryComponent_div_12_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\shop-by-category\\shop-by-category.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\shop-by-category\\shop-by-category.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\n\ninterface ShopCategory {\n  id: string;\n  name: string;\n  image: string;\n  productCount: number;\n  description: string;\n  trending: boolean;\n  discount?: number;\n  subcategories: string[];\n}\n\n@Component({\n  selector: 'app-shop-by-category',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './shop-by-category.component.html',\n  styleUrls: ['./shop-by-category.component.scss']\n})\nexport class ShopByCategoryComponent implements OnInit, OnDestroy {\n  categories: ShopCategory[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each category card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 4500; // 4.5 seconds for categories\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    try {\n      this.loadCategories();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n    } catch (error) {\n      console.error('Error initializing shop-by-category component:', error);\n      this.isLoading = false;\n      this.error = 'Failed to initialize component';\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadCategories() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for shop categories\n      this.categories = [\n        {\n          id: '1',\n          name: 'Women\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n          productCount: 15420,\n          description: 'Trendy outfits for every occasion',\n          trending: true,\n          discount: 30,\n          subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n        },\n        {\n          id: '2',\n          name: 'Men\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n          productCount: 12850,\n          description: 'Stylish clothing for modern men',\n          trending: true,\n          discount: 25,\n          subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n        },\n        {\n          id: '3',\n          name: 'Footwear',\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n          productCount: 8960,\n          description: 'Step up your shoe game',\n          trending: false,\n          discount: 20,\n          subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n        },\n        {\n          id: '4',\n          name: 'Accessories',\n          image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n          productCount: 6750,\n          description: 'Complete your look',\n          trending: true,\n          subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n        },\n        {\n          id: '5',\n          name: 'Kids Fashion',\n          image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n          productCount: 4320,\n          description: 'Adorable styles for little ones',\n          trending: false,\n          discount: 35,\n          subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n        },\n        {\n          id: '6',\n          name: 'Sports & Fitness',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n          productCount: 5680,\n          description: 'Gear up for your workout',\n          trending: true,\n          subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n        },\n        {\n          id: '7',\n          name: 'Beauty & Personal Care',\n          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n          productCount: 7890,\n          description: 'Look and feel your best',\n          trending: false,\n          discount: 15,\n          subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n        },\n        {\n          id: '8',\n          name: 'Home & Living',\n          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n          productCount: 3450,\n          description: 'Style your space',\n          trending: false,\n          subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnCategoriesLoad();\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      this.error = 'Failed to load categories';\n      this.isLoading = false;\n    }\n  }\n\n  onCategoryClick(category: ShopCategory) {\n    this.router.navigate(['/category', category.id]);\n  }\n\n  formatProductCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadCategories();\n  }\n\n  trackByCategoryId(index: number, category: ShopCategory): string {\n    return category.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.categories.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 160;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 180;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 200;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when categories load\n  private updateSliderOnCategoriesLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n\n}\n", "<div class=\"shop-categories-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"grid\" class=\"title-icon\"></ion-icon>\n        Shop by Category\n      </h2>\n      <p class=\"section-subtitle\">Explore our collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-category-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- No Data State -->\n  <div *ngIf=\"!isLoading && !error && categories.length === 0\" class=\"empty-state\">\n    <ion-icon name=\"grid-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Categories</h3>\n    <p class=\"empty-message\">Product categories will appear here when available</p>\n  </div>\n\n  <!-- Categories Slider -->\n  <div *ngIf=\"!isLoading && !error && categories.length > 0\" class=\"categories-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n    \n    <!-- Slider Wrapper -->\n    <div class=\"categories-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"categories-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div \n          *ngFor=\"let category of categories; trackBy: trackByCategoryId\" \n          class=\"category-card\"\n          [class.trending]=\"category.trending\"\n          (click)=\"onCategoryClick(category)\"\n        >\n          <!-- Category Image -->\n          <div class=\"category-image-container\">\n            <img \n              [src]=\"category.image\"\n              [alt]=\"category.name\"\n              class=\"category-image\"\n              loading=\"lazy\"\n            />\n            \n            <!-- Trending Badge -->\n            <div *ngIf=\"category.trending\" class=\"trending-badge\">\n              <ion-icon name=\"trending-up\"></ion-icon>\n              <span>Trending</span>\n            </div>\n            \n            <!-- Discount Badge -->\n            <div *ngIf=\"category.discount\" class=\"discount-badge\">\n              {{ category.discount }}% OFF\n            </div>\n            \n            <!-- Overlay -->\n            <div class=\"category-overlay\">\n              <ion-icon name=\"arrow-forward\" class=\"explore-icon\"></ion-icon>\n            </div>\n          </div>\n\n          <!-- Category Info -->\n          <div class=\"category-info\">\n            <h3 class=\"category-name\">{{ category.name }}</h3>\n            <p class=\"category-description\">{{ category.description }}</p>\n            <p class=\"product-count\">{{ formatProductCount(category.productCount) }} Products</p>\n            \n            <!-- Subcategories -->\n            <div class=\"subcategories\">\n              <span \n                *ngFor=\"let sub of category.subcategories.slice(0, 3)\" \n                class=\"subcategory-tag\"\n              >\n                {{ sub }}\n              </span>\n              <span *ngIf=\"category.subcategories.length > 3\" class=\"more-subcategories\">\n                +{{ category.subcategories.length - 3 }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div> <!-- End categories-slider-wrapper -->\n  </div> <!-- End categories-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && categories.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"grid-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Categories</h3>\n    <p class=\"empty-message\">Check back later for product categories</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;ICWtCC,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,kBAAkE;IAStEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAY;;;;;;IAYtCT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,+DAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAQtCpB,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,yDAAkD;IAC7EV,EAD6E,CAAAG,YAAA,EAAI,EAC3E;;;;;IA+BIH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAChBV,EADgB,CAAAG,YAAA,EAAO,EACjB;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAC,WAAA,CAAAC,QAAA,WACF;;;;;IAgBEvB,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAG,MAAA,MACF;;;;;IACAxB,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,OAAAC,WAAA,CAAAG,aAAA,CAAAC,MAAA,UACF;;;;;;IAhDN1B,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAW,UAAA,mBAAAgB,mEAAA;MAAA,MAAAL,WAAA,GAAAtB,EAAA,CAAAa,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAe,eAAA,CAAAR,WAAA,CAAyB;IAAA,EAAC;IAGnCtB,EAAA,CAAAC,cAAA,cAAsC;IACpCD,EAAA,CAAAE,SAAA,cAKE;IASFF,EANA,CAAAI,UAAA,IAAA2B,mDAAA,kBAAsD,IAAAC,mDAAA,kBAMA;IAKtDhC,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,mBAA+D;IAEnEF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,cAA2B,aACC;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAU,MAAA,IAA0B;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAC9DH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAwD;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAGrFH,EAAA,CAAAC,cAAA,eAA2B;IAOzBD,EANA,CAAAI,UAAA,KAAA6B,qDAAA,mBAGC,KAAAC,qDAAA,mBAG0E;IAKjFlC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhDJH,EAAA,CAAAmC,WAAA,aAAAb,WAAA,CAAAc,QAAA,CAAoC;IAMhCpC,EAAA,CAAAM,SAAA,GAAsB;IACtBN,EADA,CAAAO,UAAA,QAAAe,WAAA,CAAAe,KAAA,EAAArC,EAAA,CAAAsC,aAAA,CAAsB,QAAAhB,WAAA,CAAAiB,IAAA,CACD;IAMjBvC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAe,WAAA,CAAAc,QAAA,CAAuB;IAMvBpC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAe,WAAA,CAAAC,QAAA,CAAuB;IAYHvB,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAG,WAAA,CAAAiB,IAAA,CAAmB;IACbvC,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAmB,iBAAA,CAAAG,WAAA,CAAAkB,WAAA,CAA0B;IACjCxC,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAAqB,kBAAA,KAAAN,MAAA,CAAA0B,kBAAA,CAAAnB,WAAA,CAAAoB,YAAA,eAAwD;IAK7D1C,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,UAAA,YAAAe,WAAA,CAAAG,aAAA,CAAAkB,KAAA,OAAqC;IAKhD3C,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAO,UAAA,SAAAe,WAAA,CAAAG,aAAA,CAAAC,MAAA,KAAuC;;;;;;IAxDxD1B,EAFF,CAAAC,cAAA,cAA+F,iBAEH;IAAtDD,EAAA,CAAAW,UAAA,mBAAAiC,gEAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+B,SAAA,EAAW;IAAA,EAAC;IACvD9C,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAW,UAAA,mBAAAoC,gEAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC;IACvDhD,EAAA,CAAAE,SAAA,mBAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAAwG;IAAjCD,EAAhC,CAAAW,UAAA,wBAAAsC,kEAAA;MAAAjD,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAcF,MAAA,CAAAmC,cAAA,EAAgB;IAAA,EAAC,wBAAAC,kEAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAAqC,eAAA,EAAiB;IAAA,EAAC;IACrGpD,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAI,UAAA,IAAAiD,6CAAA,oBAKC;IAiDPrD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAhEsDH,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAAuC,YAAA,OAA+B;IAG/BtD,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAAuC,YAAA,IAAAvC,MAAA,CAAAwC,QAAA,CAAqC;IAM9DvD,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAwD,WAAA,8BAAAzC,MAAA,CAAA0C,WAAA,SAAuD;IAE7DzD,EAAA,CAAAM,SAAA,EAAe;IAAAN,EAAf,CAAAO,UAAA,YAAAQ,MAAA,CAAA2C,UAAA,CAAe,iBAAA3C,MAAA,CAAA4C,iBAAA,CAA0B;;;;;IAwDtE3D,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,8CAAuC;IAClEV,EADkE,CAAAG,YAAA,EAAI,EAChE;;;AD7FR,OAAM,MAAOyD,uBAAuB;EAmBlCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlB1B,KAAAJ,UAAU,GAAmB,EAAE;IAC/B,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAA3C,KAAK,GAAkB,IAAI;IACnB,KAAA4C,YAAY,GAAiB,IAAIlE,YAAY,EAAE;IAEvD;IACA,KAAAwD,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI;MACF,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,mBAAmB,EAAE;KAC3B,CAAC,OAAOrD,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE,IAAI,CAAC2C,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC3C,KAAK,GAAG,gCAAgC;;EAEjD;EAEAuD,WAAWA,CAAA;IACT,IAAI,CAACX,YAAY,CAACY,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcN,cAAcA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MAC1B,IAAI;QACFD,KAAI,CAACf,SAAS,GAAG,IAAI;QACrBe,KAAI,CAAC1D,KAAK,GAAG,IAAI;QAEjB;QACA0D,KAAI,CAACpB,UAAU,GAAG,CAChB;UACEsB,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,kBAAkB;UACxBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,KAAK;UACnBF,WAAW,EAAE,mCAAmC;UAChDJ,QAAQ,EAAE,IAAI;UACdb,QAAQ,EAAE,EAAE;UACZE,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa;SAC5D,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,gBAAgB;UACtBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,KAAK;UACnBF,WAAW,EAAE,iCAAiC;UAC9CJ,QAAQ,EAAE,IAAI;UACdb,QAAQ,EAAE,EAAE;UACZE,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO;SACtD,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,UAAU;UAChBF,KAAK,EAAE,gFAAgF;UACvFK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,wBAAwB;UACrCJ,QAAQ,EAAE,KAAK;UACfb,QAAQ,EAAE,EAAE;UACZE,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;SACzD,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,aAAa;UACnBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,oBAAoB;UACjCJ,QAAQ,EAAE,IAAI;UACdX,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;SACtD,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,cAAc;UACpBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,iCAAiC;UAC9CJ,QAAQ,EAAE,KAAK;UACfb,QAAQ,EAAE,EAAE;UACZE,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;SAChD,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,kBAAkB;UACxBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,0BAA0B;UACvCJ,QAAQ,EAAE,IAAI;UACdX,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa;SAClE,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,wBAAwB;UAC9BF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,yBAAyB;UACtCJ,QAAQ,EAAE,KAAK;UACfb,QAAQ,EAAE,EAAE;UACZE,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW;SAC9D,EACD;UACEuD,EAAE,EAAE,GAAG;UACPzC,IAAI,EAAE,eAAe;UACrBF,KAAK,EAAE,mFAAmF;UAC1FK,YAAY,EAAE,IAAI;UAClBF,WAAW,EAAE,kBAAkB;UAC/BJ,QAAQ,EAAE,KAAK;UACfX,aAAa,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS;SAC3D,CACF;QAEDqD,KAAI,CAACf,SAAS,GAAG,KAAK;QACtBe,KAAI,CAACG,4BAA4B,EAAE;OACpC,CAAC,OAAO7D,KAAK,EAAE;QACdsD,OAAO,CAACtD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD0D,KAAI,CAAC1D,KAAK,GAAG,2BAA2B;QACxC0D,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAjC,eAAeA,CAACoD,QAAsB;IACpC,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,WAAW,EAAED,QAAQ,CAACF,EAAE,CAAC,CAAC;EAClD;EAEAvC,kBAAkBA,CAAC2C,KAAa;IAC9B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEApE,OAAOA,CAAA;IACL,IAAI,CAACqD,cAAc,EAAE;EACvB;EAEAZ,iBAAiBA,CAAC4B,KAAa,EAAEL,QAAsB;IACrD,OAAOA,QAAQ,CAACF,EAAE;EACpB;EAEA;EACQQ,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACpB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACQ,aAAa,EAAE;IACpB,IAAI,CAACY,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACrB,QAAQ,IAAI,IAAI,CAACX,UAAU,CAAChC,MAAM,GAAG,IAAI,CAACwC,YAAY,EAAE;QAChE,IAAI,CAACyB,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACxB,cAAc,CAAC;EACzB;EAEQU,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACY,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQE,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACrC,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAACuC,iBAAiB,EAAE;EAC1B;EAEA3C,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACQ,aAAa,EAAE;EACtB;EAEAzB,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACmB,cAAc,EAAE;EACvB;EAEA;EACQhB,wBAAwBA,CAAA;IAC9B,MAAMsB,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAC7B,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI4B,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAC7B,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI4B,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAC7B,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAAC+B,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQpB,mBAAmBA,CAAA;IACzBsB,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC1B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACAyB,kBAAkBA,CAAA;IAChB,IAAI,CAAC1C,QAAQ,GAAG4C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1C,UAAU,CAAChC,MAAM,GAAG,IAAI,CAACwC,YAAY,CAAC;EACzE;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACuC,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEArD,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAACuC,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACpC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQoC,gCAAgCA,CAAA;IACtC,IAAI,CAACxB,aAAa,EAAE;IACpByB,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQP,4BAA4BA,CAAA;IAClCqB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAC3C,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC+B,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;;;uBAhQW5B,uBAAuB,EAAA5D,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB7C,uBAAuB;MAAA8C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5G,EAAA,CAAA6G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB9BnH,EAJN,CAAAC,cAAA,aAAuC,aAET,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAAoD;UACpDF,EAAA,CAAAU,MAAA,yBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,8BAAuB;UAEvDV,EAFuD,CAAAG,YAAA,EAAI,EACnD,EACF;UAuGNH,EApGA,CAAAI,UAAA,IAAAiH,sCAAA,iBAAiD,IAAAC,sCAAA,iBAcQ,KAAAC,uCAAA,iBAUwB,KAAAC,uCAAA,iBAOc,KAAAC,uCAAA,kBAqEV;UAKvFzH,EAAA,CAAAG,YAAA,EAAM;;;UAzGEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAA6G,GAAA,CAAArD,SAAA,CAAe;UAcf/D,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAA6G,GAAA,CAAAhG,KAAA,KAAAgG,GAAA,CAAArD,SAAA,CAAyB;UAUzB/D,EAAA,CAAAM,SAAA,EAAqD;UAArDN,EAAA,CAAAO,UAAA,UAAA6G,GAAA,CAAArD,SAAA,KAAAqD,GAAA,CAAAhG,KAAA,IAAAgG,GAAA,CAAA1D,UAAA,CAAAhC,MAAA,OAAqD;UAOrD1B,EAAA,CAAAM,SAAA,EAAmD;UAAnDN,EAAA,CAAAO,UAAA,UAAA6G,GAAA,CAAArD,SAAA,KAAAqD,GAAA,CAAAhG,KAAA,IAAAgG,GAAA,CAAA1D,UAAA,CAAAhC,MAAA,KAAmD;UAqEnD1B,EAAA,CAAAM,SAAA,EAAqD;UAArDN,EAAA,CAAAO,UAAA,UAAA6G,GAAA,CAAArD,SAAA,KAAAqD,GAAA,CAAAhG,KAAA,IAAAgG,GAAA,CAAA1D,UAAA,CAAAhC,MAAA,OAAqD;;;qBD7FjD7B,YAAY,EAAA6H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7H,WAAW,EAAA8H,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}