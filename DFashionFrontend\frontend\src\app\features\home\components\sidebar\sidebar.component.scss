.sidebar {
  position: sticky;
  top: 80px;
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 8px;

  // Force visibility for debugging
  visibility: visible !important;
  opacity: 1 !important;

  // Custom scrollbar for webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

// Styling for the new components in sidebar
app-trending-products,
app-featured-brands,
app-new-arrivals,
app-suggested-for-you,
app-top-fashion-influencers,
app-shop-by-category {
  width: 100%;
  display: block;
  margin-bottom: 16px;

  // Instagram-like card styling
  ::ng-deep {
    .component-container {
      background: #ffffff;
      border: 1px solid #dbdbdb;
      border-radius: 8px;
      overflow: hidden;

      .component-header {
        padding: 16px;
        border-bottom: 1px solid #efefef;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .see-all {
          color: #0095f6;
          font-size: 14px;
          font-weight: 600;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .component-content {
        padding: 16px;
      }
    }
  }
}

// Suggestions section with Trending Now styling
.suggestions {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;

  // Header Section
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-content {
      flex: 1;
    }

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: #1a1a1a;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 12px;

      .title-icon {
        font-size: 28px;
        color: #6c5ce7;
      }
    }

    .section-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }

    .view-all-btn {
      background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 25px;
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
      }

      ion-icon {
        font-size: 16px;
      }
    }
  }

  h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    gap: 12px;

    &::before {
      content: '💡';
      font-size: 28px;
    }
  }

  .suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .suggestion-item {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #f8f9fa;
      }

      .suggestion-info {
        flex: 1;

        h5 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
        }

        p {
          margin: 0;
          font-size: 12px;
          color: #666;
        }
      }

      .follow-btn {
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
        }
      }
    }
  }
}

// Influencers section with Trending Now styling
.influencers {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;

  h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    gap: 12px;

    &::before {
      content: '👑';
      font-size: 28px;
    }
  }

  .influencer-list {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .influencer-item {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 20px;
      display: flex;
      align-items: flex-start;
      gap: 16px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      img {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #f8f9fa;
      }

      .influencer-info {
        flex: 1;

        h5 {
          margin: 0 0 6px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
        }

        p {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .influencer-stats {
          display: flex;
          flex-direction: column;
          gap: 6px;
          margin-bottom: 16px;

          span {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
            width: fit-content;
            font-weight: 500;
          }
        }

        .follow-btn {
          background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(232, 67, 147, 0.3);
          }
        }
      }
    }
  }
}

// Categories section with Trending Now styling
.categories {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;

  h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    gap: 12px;

    &::before {
      content: '🛍️';
      font-size: 28px;
    }
  }

  .category-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .category-item {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 20px 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      text-decoration: none;
      color: inherit;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      img {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 12px;
        border: 3px solid #f8f9fa;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.1);
      }

      span {
        font-size: 14px;
        font-weight: 600;
        color: #1a1a1a;
        text-align: center;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .suggestions,
  .influencers,
  .categories {
    padding: 16px;
  }

  .suggestions h3,
  .influencers h3,
  .categories h3 {
    font-size: 20px;

    &::before {
      font-size: 24px;
    }
  }

  .suggestion-item,
  .influencer-item {
    padding: 12px;
    gap: 12px;

    img {
      width: 50px;
      height: 50px;
    }
  }

  .suggestion-info h5,
  .influencer-info h5 {
    font-size: 14px;
  }

  .category-list {
    grid-template-columns: 1fr;
    gap: 12px;

    .category-item {
      flex-direction: row;
      text-align: left;
      padding: 12px;
      gap: 12px;

      img {
        width: 50px;
        height: 50px;
        margin-bottom: 0;
      }

      span {
        font-size: 12px;
      }
    }
  }
}
