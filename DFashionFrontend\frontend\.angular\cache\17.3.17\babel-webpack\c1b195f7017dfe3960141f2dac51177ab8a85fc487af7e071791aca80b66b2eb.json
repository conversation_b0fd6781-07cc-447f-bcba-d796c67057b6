{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template, 6, 5, \"ng-template\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵelement(6, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"div\", 22)(12, \"owl-carousel-o\", 23);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_2_ng_container_13_Template, 2, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 62);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 63);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"div\", 70);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_3_div_21_div_1_Template, 8, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 77);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 18);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30, 0)(3, \"div\", 31);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_3_div_4_Template, 2, 4, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"div\", 35);\n    i0.ɵɵelement(8, \"div\", 36);\n    i0.ɵɵelementStart(9, \"div\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 39);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 42);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_3_video_18_Template, 1, 1, \"video\", 43)(19, ViewAddStoriesComponent_div_3_div_19_Template, 1, 2, \"div\", 44)(20, ViewAddStoriesComponent_div_3_div_20_Template, 2, 1, \"div\", 45)(21, ViewAddStoriesComponent_div_3_div_21_Template, 2, 1, \"div\", 46)(22, ViewAddStoriesComponent_div_3_div_22_Template, 5, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 48)(24, \"div\", 49)(25, \"button\", 50);\n    i0.ɵɵelement(26, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵelement(28, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 54);\n    i0.ɵɵelement(30, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_3_div_31_Template, 13, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 57)(33, \"div\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 59, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81);\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 83)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 84);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // Owl Carousel Options\n    this.customOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false\n        },\n        400: {\n          items: 4,\n          nav: false\n        },\n        740: {\n          items: 5,\n          nav: true\n        },\n        940: {\n          items: 6,\n          nav: true\n        }\n      },\n      nav: true,\n      margin: 2,\n      stagePadding: 0,\n      autoplay: true,\n      autoplayTimeout: 4000,\n      autoplayHoverPause: true,\n      autoplaySpeed: 1000 // Animation speed for auto sliding\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: 'user1',\n        username: 'zara',\n        fullName: 'Zara Official',\n        avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'New Summer Collection 🌞',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '2',\n      user: {\n        _id: 'user2',\n        username: 'nike',\n        fullName: 'Nike',\n        avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Just Do It ✨',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 2340,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '3',\n      user: {\n        _id: 'user3',\n        username: 'adidas',\n        fullName: 'Adidas',\n        avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Impossible is Nothing 🔥',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 1890,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '4',\n      user: {\n        _id: 'user4',\n        username: 'hm',\n        fullName: 'H&M',\n        avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Fashion for Everyone 💫',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 3420,\n      isActive: true,\n      isViewed: false\n    }];\n    this.isLoadingStories = false;\n  }\n  // Removed fallback stories - only use database data\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        stories: \"stories\",\n        showAddStory: \"showAddStory\",\n        currentUser: \"currentUser\"\n      },\n      outputs: {\n        storyClick: \"storyClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 14, 4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_3_Template, 36, 17, \"div\", 5)(4, ViewAddStoriesComponent_div_4_Template, 9, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n  width: 100%;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (min-width: 769px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    border: 1px solid #dbdbdb;\\n    border-radius: 8px;\\n    padding: 20px;\\n    margin-bottom: 24px;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n    position: relative;\\n    z-index: 10;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n    border: none;\\n    border-radius: 0;\\n    border-bottom: 1px solid #efefef;\\n    background: #fafafa;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n  }\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 20px;\\n    align-items: center;\\n    min-height: 120px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 16px;\\n  }\\n}\\n\\n.add-story-static[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 82px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  max-width: calc(100% - 98px);\\n  position: relative;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 100%;\\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);\\n  pointer-events: none;\\n  z-index: 5;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]::after {\\n  opacity: 1;\\n}\\n\\n.stories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow: visible; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  min-height: 120px;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5); \\n\\n  color: #fff; \\n\\n  border: none;\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  pointer-events: all;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas {\\n  font-size: 14px;\\n  color: #fff;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -20px; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -20px; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n    display: none;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer {\\n  position: relative;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_autoSlideIndicator 4s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_autoSlideIndicator {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 66px;\\n}\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  animation-play-state: paused;\\n}\\n\\n.slider-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.slider-nav-btn.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n}\\n\\n.slider-nav-left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n\\n.slider-nav-right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n\\n.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  width: 82px;\\n  min-width: 82px;\\n  position: relative;\\n}\\n@media (min-width: 769px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    min-width: 90px;\\n    padding: 8px;\\n    border-radius: 12px;\\n  }\\n  .story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n    background: rgba(0, 0, 0, 0.05);\\n    transform: scale(1.08);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 76px;\\n    min-width: 76px;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%] {\\n  animation-duration: 1s;\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n}\\n.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #405de6;\\n  font-weight: 600;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-container[_ngcontent-%COMP%] {\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 2;\\n  transition: all 0.3s ease;\\n}\\n@media (min-width: 769px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 74px;\\n    height: 74px;\\n    border: 3px solid #fff;\\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  }\\n  .story-avatar[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.05);\\n    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    border: 1.5px solid #fff;\\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n}\\n.story-ring.viewed[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  animation: none;\\n}\\n.story-ring.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n  box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n    font-weight: 500;\\n  }\\n}\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  position: relative;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 3;\\n}\\n.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n  font-weight: bold;\\n}\\n\\n.current-user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 12px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 82px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    min-width: 70px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 0 8px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    min-width: 60px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n  .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .current-user-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n.middle-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 4;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .middle-navigation[_ngcontent-%COMP%] {\\n    bottom: 80px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 12px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵlistener", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "i_r4", "ɵɵnextContext", "index", "ctx_r1", "ɵɵresetView", "openStories", "ɵɵtext", "ɵɵstyleProp", "story_r5", "user", "avatar", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "username", "ɵɵelementContainerStart", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template", "ViewAddStoriesComponent_div_2_Template_div_click_2_listener", "_r1", "openAddStoryModal", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener", "$event", "onInitialized", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener", "onSlideChanged", "ViewAddStoriesComponent_div_2_ng_container_13_Template", "getCurrentUserAvatar", "customOptions", "stories", "i_r7", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener", "product_r9", "_r8", "$implicit", "viewProduct", "_id", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_3_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener", "_r10", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener", "_r11", "buyNow", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener", "addToWishlist", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener", "addToCart", "ViewAddStoriesComponent_div_3_div_4_Template", "ViewAddStoriesComponent_div_3_Template_div_click_5_listener", "_r6", "onStoryClick", "ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_3_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_3_video_18_Template", "ViewAddStoriesComponent_div_3_div_19_Template", "ViewAddStoriesComponent_div_3_div_20_Template", "ViewAddStoriesComponent_div_3_div_21_Template", "ViewAddStoriesComponent_div_3_div_22_Template", "ViewAddStoriesComponent_div_3_div_31_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "responsive", "items", "nav", "margin", "stagePadding", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "subscriptions", "ngOnInit", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "Date", "now", "toISOString", "expiresAt", "isActive", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "num", "undefined", "toFixed", "toString", "showStory", "document", "body", "style", "overflow", "emit", "story", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "touches", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "products", "toLocaleString", "viewProductDetails", "product", "console", "log", "navigate", "queryParams", "productId", "source", "trackProductClick", "viewCategory", "categoryId", "action", "subscribe", "next", "response", "success", "alert", "error", "startPosition", "updateSlideAnalytics", "currentStory", "toggleAutoPlay", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "CartService", "i4", "WishlistService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "ViewAddStoriesComponent_div_4_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mponent, OnInit, OnD<PERSON>roy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\nimport { environment } from '../../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // Owl Carousel Options\n  customOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    responsive: {\n      0: {\n        items: 3,\n        nav: false\n      },\n      400: {\n        items: 4,\n        nav: false\n      },\n      740: {\n        items: 5,\n        nav: true\n      },\n      940: {\n        items: 6,\n        nav: true\n      }\n    },\n    nav: true,\n    margin: 2, // Minimal gap between items\n    stagePadding: 0,\n    autoplay: true, // Enable auto sliding\n    autoplayTimeout: 4000, // 4 seconds between slides\n    autoplayHoverPause: true, // Pause on hover\n    autoplaySpeed: 1000 // Animation speed for auto sliding\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [\n      {\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(), // 22 hours from now\n        views: 1250,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(), // 20 hours from now\n        views: 2340,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now\n        views: 1890,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(), // 16 hours from now\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }\n    ];\n\n    this.isLoadingStories = false;\n  }\n\n  // Removed fallback stories - only use database data\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Section -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Add Story Button (Static - Outside Slider) -->\n    <div class=\"add-story-static\">\n      <div class=\"story-item add-story-item\" (click)=\"openAddStoryModal()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"add-story-avatar\">\n            <div class=\"add-story-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"current-user-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\"></div>\n          </div>\n        </div>\n        <div class=\"story-username\">Add Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider-wrapper\">\n      <!-- Owl Carousel Stories Slider -->\n      <div class=\"stories-slider-container\">\n        <owl-carousel-o\n          [options]=\"customOptions\"\n          (initialized)=\"onInitialized($event)\"\n          (changed)=\"onSlideChanged($event)\">\n\n          <!-- User Stories -->\n          <ng-container *ngFor=\"let story of stories; let i = index\">\n            <ng-template carouselSlide>\n              <div class=\"story-slide\" (click)=\"openStories(i)\">\n                <div class=\"story-avatar-container\">\n                  <div class=\"story-avatar\"\n                       [style.background-image]=\"'url(' + story.user.avatar + ')'\">\n                  </div>\n                  <div class=\"story-ring\"\n                       [class.viewed]=\"story.isViewed\">\n                  </div>\n                </div>\n                <div class=\"story-username\">{{ story.user.username }}</div>\n              </div>\n            </ng-template>\n          </ng-container>\n        </owl-carousel-o>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product._id)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Middle Point Navigation Button -->\n        <div class=\"middle-navigation\" *ngIf=\"hasProducts()\">\n          <button class=\"middle-nav-btn\" (click)=\"viewProduct(getStoryProducts()[0]._id)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>Shop Now</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\" *ngIf=\"hasProducts()\">\n          <button class=\"ecommerce-btn buy-now-btn\" (click)=\"buyNow()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\" (click)=\"addToCart()\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2FA,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICH3DC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAmC1BT,EAAA,CAAAC,cAAA,cAAkD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,0FAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAC/Cd,EAAA,CAAAC,cAAA,cAAoC;IAIlCD,EAHA,CAAAE,SAAA,cAEM,cAGA;IACRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IACvDpB,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAPGH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAqB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3DxB,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAyB,WAAA,WAAAH,QAAA,CAAAI,QAAA,CAA+B;IAGV1B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA2B,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAK,QAAA,CAAyB;;;;;IAX3D5B,EAAA,CAAA6B,uBAAA,GAA2D;IACzD7B,EAAA,CAAAI,UAAA,IAAA0B,oEAAA,0BAA2B;;;;;;;IAxBjC9B,EAHJ,CAAAC,cAAA,cAAuD,cAEvB,cACyC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAqB,4DAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgB,iBAAA,EAAmB;IAAA,EAAC;IAG9DjC,EAFJ,CAAAC,cAAA,cAAoC,cACJ,cACA;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAwG;IAE5GF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,gBAAS;IAEzCpB,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;IAMFH,EAHJ,CAAAC,cAAA,eAAoC,eAEI,0BAIC;IAAnCD,EADA,CAAAU,UAAA,yBAAAwB,8EAAAC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAeD,MAAA,CAAAmB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,qBAAAE,0EAAAF,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAC1BD,MAAA,CAAAqB,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAGlCnC,EAAA,CAAAI,UAAA,KAAAmC,sDAAA,2BAA2D;IAkBnEvC,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACF;;;;IAnCqCH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAAuB,oBAAA,SAAgE;IAYnGxC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAwB,aAAA,CAAyB;IAKOzC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;;;;;IA2BhD1C,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAAyB,WAAA,WAAAkB,IAAA,KAAA1B,MAAA,CAAA2B,YAAA,CAAmC,cAAAD,IAAA,GAAA1B,MAAA,CAAA2B,YAAA,CACC;;;;;IA6BpC5C,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAU,MAAA,CAAA4B,eAAA,GAAAC,QAAA,EAAA9C,EAAA,CAAA+C,aAAA,CAAkC;;;;;IAQpC/C,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItE9C,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgD,kBAAA,MAAA/B,MAAA,CAAA4B,eAAA,GAAAI,OAAA,MACF;;;;;;IAIEjD,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAU,UAAA,mBAAAwC,yEAAA;MAAA,MAAAC,UAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAApC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAAH,UAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IAClCvD,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAoB,MAAA,yBAAG;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAoB,MAAA,GAAkB;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAoB,MAAA,GAAgC;IAEnEpB,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAA2B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACjBxD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAwC,WAAA,CAAAN,UAAA,CAAAO,KAAA,EAAgC;;;;;IARrE1D,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAI,UAAA,IAAAuD,mDAAA,kBAGqC;IAOvC3D,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAA2C,gBAAA,GAAqB;;;;;;IAa3C5D,EADF,CAAAC,cAAA,cAAqD,iBAC6B;IAAjDD,EAAA,CAAAU,UAAA,mBAAAmD,sEAAA;MAAA7D,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAA7C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAYrC,MAAA,CAAA2C,gBAAA,EAAkB,CAAC,CAAC,EAAAL,GAAA,CAAM;IAAA,EAAC;IAC7EvD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAElBpB,EAFkB,CAAAG,YAAA,EAAO,EACd,EACL;;;;;;IAmBJH,EADF,CAAAC,cAAA,cAA4D,iBACG;IAAnBD,EAAA,CAAAU,UAAA,mBAAAqD,sEAAA;MAAA/D,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgD,MAAA,EAAQ;IAAA,EAAC;IAC1DjE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,cAAO;IACfpB,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA1BD,EAAA,CAAAU,UAAA,mBAAAwD,sEAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAkD,aAAA,EAAe;IAAA,EAAC;IAClEnE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAChBpB,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,iBAA6D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0D,sEAAA;MAAApE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAoD,SAAA,EAAW;IAAA,EAAC;IAC1DrE,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,mBAAW;IAErBpB,EAFqB,CAAAG,YAAA,EAAO,EACjB,EACL;;;;;;IA3GVH,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAkE,4CAAA,kBAIuC;IAGzCtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAA6D,4DAAApC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAwD,YAAA,CAAAtC,MAAA,CAAoB;IAAA,EAAC,wBAAAuC,iEAAAvC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAChBD,MAAA,CAAA0D,YAAA,CAAAxC,MAAA,CAAoB;IAAA,EAAC,uBAAAyC,gEAAAzC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACtBD,MAAA,CAAA4D,WAAA,CAAA1C,MAAA,CAAmB;IAAA,EAAC,sBAAA2C,+DAAA3C,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACrBD,MAAA,CAAA8D,UAAA,CAAA5C,MAAA,CAAkB;IAAA,EAAC;IAIhCnC,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAAqC;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAA6C;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAoB,MAAA,IAAiD;IAC7EpB,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAsE,gEAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgE,YAAA,EAAc;IAAA,EAAC;IACnDjF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAuC1BD,EArCA,CAAAI,UAAA,KAAA8E,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP,KAAAC,6CAAA,kBAcF;IAMvDtF,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAmF,6CAAA,mBAA4D;IAc9DvF,EAAA,CAAAG,YAAA,EAAM;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IA1HuBH,EAAA,CAAAyB,WAAA,YAAAR,MAAA,CAAAuE,MAAA,CAAwB;IAM3BxF,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;IAU7B1C,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAC,MAAA,OAAuE;IACzExB,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAkE,QAAA,CAAqC;IACrCzF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAyE,UAAA,CAAAzE,MAAA,CAAA4B,eAAA,GAAA8C,SAAA,EAA6C;IAC5C3F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAgD,kBAAA,KAAA/B,MAAA,CAAA2E,YAAA,CAAA3E,MAAA,CAAA4B,eAAA,GAAAgD,KAAA,YAAiD;IAW1E7F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAW7C9F,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAM1C9F,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAI,OAAA,CAA+B;IAK/BjD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAcO/F,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuBZ/F,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuB5B/F,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAyB,WAAA,cAAAR,MAAA,CAAAuE,MAAA,CAA0B;;;;;IAK9DxF,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,qBAAc;IACtBpB,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAoB,MAAA,sBAAe;IAAApB,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;ADjJN,OAAM,MAAO6F,uBAAuB;EAsElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IArEhB,KAAA3D,OAAO,GAAY,EAAE;IACrB,KAAA4D,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAI3G,YAAY,EAAmC;IAE1E,KAAA4G,gBAAgB,GAAG,IAAI;IAEvB,KAAA7D,YAAY,GAAG,CAAC;IAChB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAkB,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA7E,aAAa,GAAe;MAC1B8E,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;;OAER;MACDA,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,IAAI,CAAC;KACrB;IAEO,KAAAC,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAAC9F,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+F,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACjC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACkC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAC/D,OAAO,GAAG,CACb;MACEa,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,eAAe;QACzBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,MAAM;QAChBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,gFAAgF;MAC1FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,cAAc;MACvB0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,QAAQ;QAClB6D,QAAQ,EAAE,QAAQ;QAClBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,IAAI;QACd6D,QAAQ,EAAE,KAAK;QACfjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,yBAAyB;MAClC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,CACF;IAED,IAAI,CAAC+E,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EAEA5D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAgD,UAAUA,CAAC4D,UAAkB;IAC3B,MAAMJ,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMM,IAAI,GAAG,IAAIN,IAAI,CAACK,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACR,GAAG,CAACS,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAjE,YAAYA,CAACkE,GAAW;IACtB,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;MAC7C,OAAO,GAAG;;IAEZ,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOF,GAAG,CAACG,QAAQ,EAAE;EACvB;EAEA9I,WAAWA,CAACH,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAACwE,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC0E,SAAS,CAAClJ,KAAK,CAAC;IACrBmJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAAC5H,OAAO,CAAC1B,KAAK,CAAC,EAAE;MACvB,IAAI,CAACwF,UAAU,CAAC+D,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAAC9H,OAAO,CAAC1B,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAiE,YAAYA,CAAA;IACV,IAAI,CAACO,MAAM,GAAG,KAAK;IACnB,IAAI,CAACiF,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAb,SAASA,CAAClJ,KAAa;IACrB,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAAC4F,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAAC8D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACrI,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC+F,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC5B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACwE,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAEAkG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACvI,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACiE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACwE,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAGAmG,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC7F,MAAM,EAAE;IAElB,QAAQ6F,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAAChG,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAAC4G,KAAiB;IAC5B,IAAI,IAAI,CAAC3E,UAAU,EAAE;IAErB,MAAM6E,IAAI,GAAIF,KAAK,CAACG,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACV,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEAtG,YAAYA,CAAC0G,KAAiB;IAC5B,IAAI,CAAC1E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGsE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC1C,IAAI,CAAC3E,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEAlC,WAAWA,CAACwG,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC1E,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAGqE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC5C,MAAMI,YAAY,GAAG,IAAI,CAAC/E,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAMiF,WAAW,GAAGvC,IAAI,CAACwC,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAAC/E,0BAA0B,EAAE;MACjD,IAAI8E,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACZ,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAACtE,UAAU,GAAG,KAAK;;EAE3B;EAEA5B,UAAUA,CAACqH,MAAkB;IAC3B,IAAI,CAACzF,UAAU,GAAG,KAAK;EACzB;EAEQgC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGMyB,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGlC,QAAQ,CAACmC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACxD,OAAO,CAAC0D,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQtB,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACxE,UAAU,EAAE;IAEtB,MAAM+F,IAAI,GAAG,IAAI,CAAC5F,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAI6F,IAAI,GAAG,GAAG;IAE1B,IAAIhD,IAAI,CAACwC,GAAG,CAACQ,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAAC7F,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAAClE,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACkE,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAAClE,YAAY,EAAE;;MAGrB,IAAI,CAACiE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAAC4D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,6BAA6B,IAAI,CAACpE,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnBgG,qBAAqB,CAAC,MAAM,IAAI,CAACxB,MAAM,EAAE,CAAC;;EAE9C;EAEAnF,WAAWA,CAAA;IACT,MAAMyE,KAAK,GAAG,IAAI,CAAC3H,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE2H,KAAK,EAAEmC,QAAQ,IAAInC,KAAK,CAACmC,QAAQ,CAAClE,MAAM,GAAG,CAAC,CAAC;EACzD;EAEA7E,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,eAAe,EAAE,CAAC8J,QAAQ,IAAI,EAAE;EAC9C;EAEAlJ,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAEkJ,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACxC;IACA,IAAI,CAAC5G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,WAAW,EAAEH,OAAO,CAACvJ,GAAG,CAAC,CAAC;EAClD;EAEAf,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAAC+D,WAAW,EAAE/E,MAAM,IAAI,mCAAmC;EACxE;EAEAS,iBAAiBA,CAAA;IACf8K,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAAC9G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAhJ,MAAMA,CAAA;IACJ,MAAM0I,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7BI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvC;MACA,IAAI,CAAC5G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,EAAE;UACXC,SAAS,EAAEL,OAAO,CAACvJ,GAAG;UACtB6J,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACA9J,WAAWA,CAAC6J,SAAiB;IAC3B;IACA,IAAI,CAACE,iBAAiB,CAACF,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAACjH,MAAM,CAAC+G,QAAQ,CAAC,CAAC,eAAe,EAAEE,SAAS,CAAC,CAAC;EACpD;EAEAG,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAACrH,MAAM,CAAC+G,QAAQ,CAAC,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACF,SAAiB,EAAEK,MAAc;IACzD;IACAT,OAAO,CAACC,GAAG,CAAC,iBAAiBQ,MAAM,WAAW,EAAEL,SAAS,CAAC;IAC1D;EACF;EAEAhJ,aAAaA,CAAA;IACX,MAAMwI,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;MAC3BI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;MAE3C,IAAI,CAACzG,eAAe,CAAClC,aAAa,CAAC2I,OAAO,CAACvJ,GAAG,CAAC,CAACkK,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDD,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEAxJ,SAASA,CAAA;IACP,MAAMsI,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;MAC3BI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MAEvC,IAAI,CAAC1G,WAAW,CAAC/B,SAAS,CAACyI,OAAO,CAACvJ,GAAG,EAAE,CAAC,EAAEwG,SAAS,EAAEA,SAAS,CAAC,CAAC0D,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CD,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACAvL,cAAcA,CAAC+I,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAAC0C,aAAa,KAAKhE,SAAS,EAAE;MAC9C,IAAI,CAACzC,iBAAiB,GAAG+D,KAAK,CAAC0C,aAAa;MAE5C;MACAhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAAC1F,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAAC0G,oBAAoB,EAAE;;EAE/B;EAEA5L,aAAaA,CAACgK,MAAW;IACvB;IACA,IAAI,CAAChF,qBAAqB,GAAG,IAAI;IACjC2F,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQgB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAACtL,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC,EAAE;MACxD,MAAM2G,YAAY,GAAG,IAAI,CAACvL,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC;MACzDyF,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,YAAY,CAAC1M,IAAI,CAACK,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACAsM,cAAcA,CAAA;IACZ,IAAI,CAAC7G,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACA0F,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAC3F,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;;;uBA9fWrB,uBAAuB,EAAAhG,EAAA,CAAAmO,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArO,EAAA,CAAAmO,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAvO,EAAA,CAAAmO,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAzO,EAAA,CAAAmO,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB3I,uBAAuB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB/O,EAAA,CAAAU,UAAA,qBAAAuO,mDAAA9M,MAAA;YAAA,OAAA6M,GAAA,CAAA5D,aAAA,CAAAjJ,MAAA,CAAqB;UAAA,UAAAnC,EAAA,CAAAkP,iBAAA,CAAE;;;;;;;;;;;;;;;;;;UC/CpClP,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAAI,UAAA,IAAA+O,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC;UA6CzDpP,EAAA,CAAAG,YAAA,EAAM;UAgINH,EA7HA,CAAAI,UAAA,IAAAiP,sCAAA,mBAAqE,IAAAC,sCAAA,iBA6HxB;;;UArLrCtP,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAvI,gBAAA,CAAsB;UAQEzG,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAAyO,GAAA,CAAAvI,gBAAA,CAAuB;UAgDAzG,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAxJ,MAAA,CAAY;UA6HpCxF,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAxJ,MAAA,CAAY;;;qBD5I/B1F,YAAY,EAAAyP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1P,cAAc,EAAA2P,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}