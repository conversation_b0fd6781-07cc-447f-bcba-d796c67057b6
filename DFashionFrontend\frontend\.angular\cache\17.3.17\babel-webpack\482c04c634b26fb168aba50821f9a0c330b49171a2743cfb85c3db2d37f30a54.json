{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction SuggestedForYouComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 19);\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 23);\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction SuggestedForYouComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵelement(3, \"div\", 30)(4, \"div\", 31)(5, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedForYouComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, SuggestedForYouComponent_div_9_div_2_Template, 6, 0, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SuggestedForYouComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"ion-icon\", 34);\n    i0.ɵɵelementStart(2, \"p\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 37);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction SuggestedForYouComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"ion-icon\", 39);\n    i0.ɵɵelementStart(2, \"h3\", 40);\n    i0.ɵɵtext(3, \"No Suggested Users\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 41);\n    i0.ɵɵtext(5, \"Suggested users will appear here when available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedForYouComponent_div_12_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"ion-icon\", 62);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedForYouComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_12_div_7_Template_div_click_0_listener() {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUserClick(user_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 51);\n    i0.ɵɵelement(2, \"img\", 52);\n    i0.ɵɵtemplate(3, SuggestedForYouComponent_div_12_div_7_div_3_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 54)(5, \"h3\", 55);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 57);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 58);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 59);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_12_div_7_Template_button_click_15_listener($event) {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowUser(user_r6, $event));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"ion-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", user_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r6.isInfluencer);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", user_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatFollowerCount(user_r6.followerCount || 0), \" followers\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.followedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", user_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", user_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction SuggestedForYouComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47);\n    i0.ɵɵlistener(\"mouseenter\", function SuggestedForYouComponent_div_12_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function SuggestedForYouComponent_div_12_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtemplate(7, SuggestedForYouComponent_div_12_div_7_Template, 19, 12, \"div\", 49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestedUsers)(\"ngForTrackBy\", ctx_r1.trackByUserId);\n  }\n}\nfunction SuggestedForYouComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"ion-icon\", 39);\n    i0.ɵɵelementStart(2, \"h3\", 40);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 41);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SuggestedForYouComponent {\n  constructor(router, productService) {\n    this.router = router;\n    this.productService = productService;\n    this.suggestedUsers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each user card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 5000; // 5 seconds for users\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 198;\n    this.sectionComments = 67;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    try {\n      this.loadSuggestedUsers();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    } catch (error) {\n      console.error('Error initializing suggested-for-you component:', error);\n      this.isLoading = false;\n      this.error = 'Failed to initialize component';\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadSuggestedUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Load real data from API\n        _this.subscription.add(_this.productService.getSuggestedUsers().subscribe({\n          next: response => {\n            if (response.success && response.data) {\n              _this.suggestedUsers = response.data.map(user => ({\n                _id: user.id || user._id,\n                id: user.id || user._id,\n                username: user.username,\n                fullName: user.fullName,\n                avatar: user.avatar || '/assets/images/default-avatar.svg',\n                followedBy: user.followedBy || '',\n                bio: user.bio || '',\n                isFollowing: user.isFollowing || false,\n                isInfluencer: user.isInfluencer || false,\n                isVerified: user.isVerified || false,\n                followerCount: user.followerCount || user.followers || 0,\n                followers: user.followers || user.followerCount || 0,\n                following: user.following || 0,\n                posts: user.posts || 0,\n                mutualFollowers: user.mutualFollowers || 0,\n                category: user.category || 'Fashion'\n              }));\n            } else {\n              _this.suggestedUsers = [];\n            }\n            _this.isLoading = false;\n            _this.updateSliderOnUsersLoad();\n          },\n          error: error => {\n            console.error('Error loading suggested users:', error);\n            _this.error = 'Failed to load suggested users';\n            _this.isLoading = false;\n            _this.suggestedUsers = [];\n          }\n        }));\n      } catch (error) {\n        console.error('Error loading suggested users:', error);\n        _this.error = 'Failed to load suggested users';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onUserClick(user) {\n    this.router.navigate(['/profile', user.username]);\n  }\n  onFollowUser(user, event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    if (user.isFollowing) {\n      user.followerCount = (user.followerCount || 0) + 1;\n    } else {\n      user.followerCount = Math.max((user.followerCount || 0) - 1, 0);\n    }\n  }\n  formatFollowerCount(count) {\n    if (!count || count === 0) {\n      return '0';\n    }\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n  trackByUserId(index, user) {\n    return user.id || user._id || index.toString();\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when users load\n  updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for suggested users section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Suggested for You',\n        text: 'Discover amazing fashion creators!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for suggested users');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function SuggestedForYouComponent_Factory(t) {\n      return new (t || SuggestedForYouComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuggestedForYouComponent,\n      selectors: [[\"app-suggested-for-you\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 6,\n      consts: [[1, \"suggested-users-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"people\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"users-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-user-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"empty-state\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"users-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"users-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"users-slider\"], [\"class\", \"user-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"user-card\", 3, \"click\"], [1, \"user-avatar-container\"], [\"loading\", \"lazy\", 1, \"user-avatar\", 3, \"src\", \"alt\"], [\"class\", \"influencer-badge\", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"username\"], [1, \"follower-count\"], [1, \"category-tag\"], [1, \"followed-by\"], [1, \"follow-btn\", 3, \"click\"], [1, \"influencer-badge\"], [\"name\", \"star\"], [1, \"empty-container\"]],\n      template: function SuggestedForYouComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, SuggestedForYouComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Suggested for you \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Discover amazing fashion creators\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, SuggestedForYouComponent_div_9_Template, 3, 2, \"div\", 7)(10, SuggestedForYouComponent_div_10_Template, 7, 1, \"div\", 8)(11, SuggestedForYouComponent_div_11_Template, 6, 0, \"div\", 9)(12, SuggestedForYouComponent_div_12_Template, 8, 6, \"div\", 10)(13, SuggestedForYouComponent_div_13_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, IonicModule, i4.IonIcon],\n      styles: [\".suggested-users-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(108, 92, 231, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: #6c5ce7;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(108, 92, 231, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(108, 92, 231, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 48, 64, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #6c5ce7 100%);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.users-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.users-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.users-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  width: 180px;\\n}\\n\\n.user-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.user-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 16px;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #6c5ce7;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 45px);\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: white;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  border: 2px solid white;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.user-info[_ngcontent-%COMP%]   .follower-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0 0 4px 0;\\n  font-weight: 600;\\n}\\n.user-info[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #6c5ce7;\\n  background: rgba(108, 92, 231, 0.1);\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 8px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .followed-by[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #999;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(25% - 15px);\\n    width: calc(25% - 15px);\\n    max-width: 200px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(20% - 16px);\\n    width: calc(20% - 16px);\\n    max-width: 180px;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(16.666% - 17px);\\n    width: calc(16.666% - 17px);\\n    max-width: 170px;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(14.285% - 18px);\\n    width: calc(14.285% - 18px);\\n    max-width: 160px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .users-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .users-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .users-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 160px;\\n    width: 160px;\\n    padding: 16px;\\n  }\\n  .user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 150px;\\n    width: 150px;\\n  }\\n}\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SuggestedForYouComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SuggestedForYouComponent_div_1_Template_button_click_5_listener", "openComments", "SuggestedForYouComponent_div_1_Template_button_click_9_listener", "shareSection", "SuggestedForYouComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "SuggestedForYouComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "SuggestedForYouComponent_div_9_div_2_Template", "ɵɵpureFunction0", "_c0", "SuggestedForYouComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "SuggestedForYouComponent_div_12_div_7_Template_div_click_0_listener", "user_r6", "_r5", "$implicit", "onUserClick", "SuggestedForYouComponent_div_12_div_7_div_3_Template", "SuggestedForYouComponent_div_12_div_7_Template_button_click_15_listener", "$event", "onFollowUser", "avatar", "ɵɵsanitizeUrl", "fullName", "isInfluencer", "ɵɵtextInterpolate1", "username", "formatFollowerCount", "followerCount", "category", "<PERSON><PERSON><PERSON>", "isFollowing", "SuggestedForYouComponent_div_12_Template_button_click_1_listener", "_r4", "slidePrev", "SuggestedForYouComponent_div_12_Template_button_click_3_listener", "slideNext", "SuggestedForYouComponent_div_12_Template_div_mouseenter_5_listener", "pauseAutoSlide", "SuggestedForYouComponent_div_12_Template_div_mouseleave_5_listener", "resumeAutoSlide", "SuggestedForYouComponent_div_12_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "suggestedUsers", "trackByUserId", "SuggestedForYouComponent", "constructor", "router", "productService", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "loadSuggestedUsers", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "console", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "add", "getSuggestedUsers", "subscribe", "next", "response", "success", "data", "map", "user", "_id", "id", "bio", "isVerified", "followers", "following", "posts", "mutualFollowers", "updateSliderOnUsersLoad", "navigate", "event", "stopPropagation", "Math", "max", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "restartAutoSlideAfterInteraction", "setTimeout", "log", "navigator", "share", "title", "text", "url", "location", "href", "clipboard", "writeText", "ɵɵdirectiveInject", "i1", "Router", "i2", "ProductService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SuggestedForYouComponent_Template", "rf", "ctx", "SuggestedForYouComponent_div_1_Template", "SuggestedForYouComponent_div_9_Template", "SuggestedForYouComponent_div_10_Template", "SuggestedForYouComponent_div_11_Template", "SuggestedForYouComponent_div_12_Template", "SuggestedForYouComponent_div_13_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { ProductService } from '../../../../core/services/product.service';\n\ninterface SuggestedUser {\n  _id?: string;\n  id?: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followedBy?: string;\n  bio?: string;\n  isFollowing: boolean;\n  isInfluencer?: boolean;\n  isVerified?: boolean;\n  followerCount?: number;\n  followers?: number;\n  following?: number;\n  posts?: number;\n  mutualFollowers?: number;\n  category?: string;\n}\n\n@Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})\nexport class SuggestedForYouComponent implements OnInit, OnDestroy {\n  suggestedUsers: SuggestedUser[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each user card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 5000; // 5 seconds for users\n  isAutoSliding = true;\n  isPaused = false;\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 198;\n  sectionComments = 67;\n  isMobile = false;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService\n  ) {}\n\n  ngOnInit() {\n    try {\n      this.loadSuggestedUsers();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    } catch (error) {\n      console.error('Error initializing suggested-for-you component:', error);\n      this.isLoading = false;\n      this.error = 'Failed to initialize component';\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadSuggestedUsers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Load real data from API\n      this.subscription.add(\n        this.productService.getSuggestedUsers().subscribe({\n          next: (response) => {\n            if (response.success && response.data) {\n              this.suggestedUsers = response.data.map((user: any) => ({\n                _id: user.id || user._id,\n                id: user.id || user._id,\n                username: user.username,\n                fullName: user.fullName,\n                avatar: user.avatar || '/assets/images/default-avatar.svg',\n                followedBy: user.followedBy || '',\n                bio: user.bio || '',\n                isFollowing: user.isFollowing || false,\n                isInfluencer: user.isInfluencer || false,\n                isVerified: user.isVerified || false,\n                followerCount: user.followerCount || user.followers || 0,\n                followers: user.followers || user.followerCount || 0,\n                following: user.following || 0,\n                posts: user.posts || 0,\n                mutualFollowers: user.mutualFollowers || 0,\n                category: user.category || 'Fashion'\n              }));\n            } else {\n              this.suggestedUsers = [];\n            }\n            this.isLoading = false;\n            this.updateSliderOnUsersLoad();\n          },\n          error: (error) => {\n            console.error('Error loading suggested users:', error);\n            this.error = 'Failed to load suggested users';\n            this.isLoading = false;\n            this.suggestedUsers = [];\n          }\n        })\n      );\n    } catch (error) {\n      console.error('Error loading suggested users:', error);\n      this.error = 'Failed to load suggested users';\n      this.isLoading = false;\n    }\n  }\n\n  onUserClick(user: SuggestedUser) {\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  onFollowUser(user: SuggestedUser, event: Event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n\n    if (user.isFollowing) {\n      user.followerCount = (user.followerCount || 0) + 1;\n    } else {\n      user.followerCount = Math.max((user.followerCount || 0) - 1, 0);\n    }\n  }\n\n  formatFollowerCount(count: number | undefined): string {\n    if (!count || count === 0) {\n      return '0';\n    }\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n\n  trackByUserId(index: number, user: SuggestedUser): string {\n    return user.id || user._id || index.toString();\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when users load\n  private updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for suggested users section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Suggested for You',\n        text: 'Discover amazing fashion creators!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for suggested users');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n\n\n}\n", "<div class=\"suggested-users-container\">\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"people\" class=\"title-icon\"></ion-icon>\n        Suggested for you\n      </h2>\n      <p class=\"section-subtitle\">Discover amazing fashion creators</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-user-card\">\n        <div class=\"loading-avatar\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- No Data State -->\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length === 0\" class=\"empty-state\">\n    <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Suggested Users</h3>\n    <p class=\"empty-message\">Suggested users will appear here when available</p>\n  </div>\n\n  <!-- Users Slider -->\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length > 0\" class=\"users-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n    \n    <!-- Slider Wrapper -->\n    <div class=\"users-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"users-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div \n          *ngFor=\"let user of suggestedUsers; trackBy: trackByUserId\" \n          class=\"user-card\"\n          (click)=\"onUserClick(user)\"\n        >\n          <!-- User Avatar -->\n          <div class=\"user-avatar-container\">\n            <img \n              [src]=\"user.avatar\"\n              [alt]=\"user.fullName\"\n              class=\"user-avatar\"\n              loading=\"lazy\"\n            />\n            <div *ngIf=\"user.isInfluencer\" class=\"influencer-badge\">\n              <ion-icon name=\"star\"></ion-icon>\n            </div>\n          </div>\n\n          <!-- User Info -->\n          <div class=\"user-info\">\n            <h3 class=\"user-name\">{{ user.fullName }}</h3>\n            <p class=\"username\">&#64;{{ user.username }}</p>\n            <p class=\"follower-count\">{{ formatFollowerCount(user.followerCount || 0) }} followers</p>\n            <p class=\"category-tag\">{{ user.category }}</p>\n            <p class=\"followed-by\">{{ user.followedBy }}</p>\n          </div>\n\n          <!-- Follow Button -->\n          <button \n            class=\"follow-btn\"\n            [class.following]=\"user.isFollowing\"\n            (click)=\"onFollowUser(user, $event)\"\n          >\n            <span>{{ user.isFollowing ? 'Following' : 'Follow' }}</span>\n            <ion-icon [name]=\"user.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div> <!-- End users-slider-wrapper -->\n  </div> <!-- End users-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Suggestions</h3>\n    <p class=\"empty-message\">Check back later for user suggestions</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;ICDxCC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,gEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,iEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IAuBxE7B,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,SAAA,cAAkC;IAClCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,6CAAA,kBAA8D;IASlE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAY;IAAZrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAY;;;;;;IAYtCjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,iEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAQtCrC,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAAU,SAAA,mBAA8D;IAC9DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,yBAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,sDAA+C;IAC1EX,EAD0E,CAAAY,YAAA,EAAI,EACxE;;;;;IA4BIZ,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAU,SAAA,mBAAiC;IACnCV,EAAA,CAAAY,YAAA,EAAM;;;;;;IAfVZ,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAoC,oEAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,WAAA,CAAAH,OAAA,CAAiB;IAAA,EAAC;IAG3BvC,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAU,SAAA,cAKE;IACFV,EAAA,CAAA8B,UAAA,IAAAa,oDAAA,kBAAwD;IAG1D3C,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,cAAuB,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC9CZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,GAAwB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAChDZ,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAC1FZ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAC/CZ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAW,MAAA,IAAqB;IAC9CX,EAD8C,CAAAY,YAAA,EAAI,EAC5C;IAGNZ,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA0C,wEAAAC,MAAA;MAAA,MAAAN,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwC,YAAA,CAAAP,OAAA,EAAAM,MAAA,CAA0B;IAAA,EAAC;IAEpC7C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAA+C;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5DZ,EAAA,CAAAU,SAAA,oBAAqE;IAEzEV,EADE,CAAAY,YAAA,EAAS,EACL;;;;;IA5BAZ,EAAA,CAAAqB,SAAA,GAAmB;IACnBrB,EADA,CAAAwB,UAAA,QAAAe,OAAA,CAAAQ,MAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAAmB,QAAAT,OAAA,CAAAU,QAAA,CACE;IAIjBjD,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,SAAAe,OAAA,CAAAW,YAAA,CAAuB;IAOPlD,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAU,QAAA,CAAmB;IACrBjD,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAAmD,kBAAA,MAAAZ,OAAA,CAAAa,QAAA,KAAwB;IAClBpD,EAAA,CAAAqB,SAAA,GAA4D;IAA5DrB,EAAA,CAAAmD,kBAAA,KAAA7C,MAAA,CAAA+C,mBAAA,CAAAd,OAAA,CAAAe,aAAA,qBAA4D;IAC9DtD,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAgB,QAAA,CAAmB;IACpBvD,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAiB,UAAA,CAAqB;IAM5CxD,EAAA,CAAAqB,SAAA,EAAoC;IAApCrB,EAAA,CAAAsB,WAAA,cAAAiB,OAAA,CAAAkB,WAAA,CAAoC;IAG9BzD,EAAA,CAAAqB,SAAA,GAA+C;IAA/CrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAkB,WAAA,0BAA+C;IAC3CzD,EAAA,CAAAqB,SAAA,EAA+C;IAA/CrB,EAAA,CAAAwB,UAAA,SAAAe,OAAA,CAAAkB,WAAA,uBAA+C;;;;;;IA5CjEzD,EAFF,CAAAC,cAAA,cAA8F,iBAEF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAwD,iEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsD,SAAA,EAAW;IAAA,EAAC;IACvD5D,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAA2D,iEAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,SAAA,EAAW;IAAA,EAAC;IACvD9D,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAmG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA6D,mEAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAA0D,cAAA,EAAgB;IAAA,EAAC,wBAAAC,mEAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA4D,eAAA,EAAiB;IAAA,EAAC;IAChGlE,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAA8B,UAAA,IAAAqC,8CAAA,oBAIC;IAmCPnE,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAjDsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA8D,YAAA,OAA+B;IAG/BpE,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA8D,YAAA,IAAA9D,MAAA,CAAA+D,QAAA,CAAqC;IAMnErE,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAsE,WAAA,8BAAAhE,MAAA,CAAAiE,WAAA,SAAuD;IAE5DvE,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAkE,cAAA,CAAmB,iBAAAlE,MAAA,CAAAmE,aAAA,CAAsB;;;;;IAyClEzE,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA8D;IAC9DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,4CAAqC;IAChEX,EADgE,CAAAY,YAAA,EAAI,EAC9D;;;ADpGR,OAAM,MAAO8D,wBAAwB;EA0BnCC,YACUC,MAAc,EACdC,cAA8B;IAD9B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IA3BxB,KAAAL,cAAc,GAAoB,EAAE;IACpC,KAAAM,SAAS,GAAG,IAAI;IAChB,KAAAzC,KAAK,GAAkB,IAAI;IACnB,KAAA0C,YAAY,GAAiB,IAAIjF,YAAY,EAAE;IAEvD;IACA,KAAAsE,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAS,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAZ,QAAQ,GAAG,CAAC;IAIZ,KAAAa,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAA7D,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAyD,QAAQ,GAAG,KAAK;EAKb;EAEHC,QAAQA,CAAA;IACN,IAAI;MACF,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAACC,iBAAiB,EAAE;KACzB,CAAC,OAAOrD,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE,IAAI,CAACyC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACzC,KAAK,GAAG,gCAAgC;;EAEjD;EAEAuD,WAAWA,CAAA;IACT,IAAI,CAACb,YAAY,CAACc,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcP,kBAAkBA,CAAA;IAAA,IAAAQ,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACjB,SAAS,GAAG,IAAI;QACrBiB,KAAI,CAAC1D,KAAK,GAAG,IAAI;QAEjB;QACA0D,KAAI,CAAChB,YAAY,CAACkB,GAAG,CACnBF,KAAI,CAAClB,cAAc,CAACqB,iBAAiB,EAAE,CAACC,SAAS,CAAC;UAChDC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;cACrCR,KAAI,CAACvB,cAAc,GAAG6B,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAEC,IAAS,KAAM;gBACtDC,GAAG,EAAED,IAAI,CAACE,EAAE,IAAIF,IAAI,CAACC,GAAG;gBACxBC,EAAE,EAAEF,IAAI,CAACE,EAAE,IAAIF,IAAI,CAACC,GAAG;gBACvBtD,QAAQ,EAAEqD,IAAI,CAACrD,QAAQ;gBACvBH,QAAQ,EAAEwD,IAAI,CAACxD,QAAQ;gBACvBF,MAAM,EAAE0D,IAAI,CAAC1D,MAAM,IAAI,mCAAmC;gBAC1DS,UAAU,EAAEiD,IAAI,CAACjD,UAAU,IAAI,EAAE;gBACjCoD,GAAG,EAAEH,IAAI,CAACG,GAAG,IAAI,EAAE;gBACnBnD,WAAW,EAAEgD,IAAI,CAAChD,WAAW,IAAI,KAAK;gBACtCP,YAAY,EAAEuD,IAAI,CAACvD,YAAY,IAAI,KAAK;gBACxC2D,UAAU,EAAEJ,IAAI,CAACI,UAAU,IAAI,KAAK;gBACpCvD,aAAa,EAAEmD,IAAI,CAACnD,aAAa,IAAImD,IAAI,CAACK,SAAS,IAAI,CAAC;gBACxDA,SAAS,EAAEL,IAAI,CAACK,SAAS,IAAIL,IAAI,CAACnD,aAAa,IAAI,CAAC;gBACpDyD,SAAS,EAAEN,IAAI,CAACM,SAAS,IAAI,CAAC;gBAC9BC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,CAAC;gBACtBC,eAAe,EAAER,IAAI,CAACQ,eAAe,IAAI,CAAC;gBAC1C1D,QAAQ,EAAEkD,IAAI,CAAClD,QAAQ,IAAI;eAC5B,CAAC,CAAC;aACJ,MAAM;cACLwC,KAAI,CAACvB,cAAc,GAAG,EAAE;;YAE1BuB,KAAI,CAACjB,SAAS,GAAG,KAAK;YACtBiB,KAAI,CAACmB,uBAAuB,EAAE;UAChC,CAAC;UACD7E,KAAK,EAAGA,KAAK,IAAI;YACfsD,OAAO,CAACtD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACtD0D,KAAI,CAAC1D,KAAK,GAAG,gCAAgC;YAC7C0D,KAAI,CAACjB,SAAS,GAAG,KAAK;YACtBiB,KAAI,CAACvB,cAAc,GAAG,EAAE;UAC1B;SACD,CAAC,CACH;OACF,CAAC,OAAOnC,KAAK,EAAE;QACdsD,OAAO,CAACtD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD0D,KAAI,CAAC1D,KAAK,GAAG,gCAAgC;QAC7C0D,KAAI,CAACjB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEApC,WAAWA,CAAC+D,IAAmB;IAC7B,IAAI,CAAC7B,MAAM,CAACuC,QAAQ,CAAC,CAAC,UAAU,EAAEV,IAAI,CAACrD,QAAQ,CAAC,CAAC;EACnD;EAEAN,YAAYA,CAAC2D,IAAmB,EAAEW,KAAY;IAC5CA,KAAK,CAACC,eAAe,EAAE;IACvBZ,IAAI,CAAChD,WAAW,GAAG,CAACgD,IAAI,CAAChD,WAAW;IAEpC,IAAIgD,IAAI,CAAChD,WAAW,EAAE;MACpBgD,IAAI,CAACnD,aAAa,GAAG,CAACmD,IAAI,CAACnD,aAAa,IAAI,CAAC,IAAI,CAAC;KACnD,MAAM;MACLmD,IAAI,CAACnD,aAAa,GAAGgE,IAAI,CAACC,GAAG,CAAC,CAACd,IAAI,CAACnD,aAAa,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;EAEnE;EAEAD,mBAAmBA,CAACmE,KAAyB;IAC3C,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;MACzB,OAAO,GAAG;;IAEZ,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAtF,OAAOA,CAAA;IACL,IAAI,CAACmD,kBAAkB,EAAE;EAC3B;EAEAd,aAAaA,CAACkD,KAAa,EAAElB,IAAmB;IAC9C,OAAOA,IAAI,CAACE,EAAE,IAAIF,IAAI,CAACC,GAAG,IAAIiB,KAAK,CAACD,QAAQ,EAAE;EAChD;EAEA;EACQE,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzC,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACU,aAAa,EAAE;IACpB,IAAI,CAAC+B,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAAC1C,QAAQ,IAAI,IAAI,CAACZ,cAAc,CAACuD,MAAM,GAAG,IAAI,CAAC9C,YAAY,EAAE;QACpE,IAAI,CAAC+C,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC9C,cAAc,CAAC;EACzB;EAEQY,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC+B,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC5D,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC8D,iBAAiB,EAAE;EAC1B;EAEAlE,cAAcA,CAAA;IACZ,IAAI,CAACoB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACU,aAAa,EAAE;EACtB;EAEA5B,eAAeA,CAAA;IACb,IAAI,CAACkB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACwC,cAAc,EAAE;EACvB;EAEA;EACQpC,wBAAwBA,CAAA;IAC9B,MAAM2C,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAACnD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIkD,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAACnD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIkD,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAACnD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACqD,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQzC,mBAAmBA,CAAA;IACzB2C,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC/C,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA8C,kBAAkBA,CAAA;IAChB,IAAI,CAACjE,QAAQ,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/C,cAAc,CAACuD,MAAM,GAAG,IAAI,CAAC9C,YAAY,CAAC;EAC7E;EAEArB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC8D,iBAAiB,EAAE;MACxB,IAAI,CAACM,gCAAgC,EAAE;;EAE3C;EAEA1E,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC8D,iBAAiB,EAAE;MACxB,IAAI,CAACM,gCAAgC,EAAE;;EAE3C;EAEQN,iBAAiBA,CAAA;IACvB,IAAI,CAAC3D,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACY,SAAS;EACxD;EAEQwD,gCAAgCA,CAAA;IACtC,IAAI,CAAC1C,aAAa,EAAE;IACpB2C,UAAU,CAAC,MAAK;MACd,IAAI,CAACb,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,uBAAuBA,CAAA;IAC7BuB,UAAU,CAAC,MAAK;MACd,IAAI,CAACH,kBAAkB,EAAE;MACzB,IAAI,CAAClE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACqD,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAnH,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACV6E,OAAO,CAAC+C,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEA1H,YAAYA,CAAA;IACV,IAAI2H,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,oCAAoC;QAC1CC,GAAG,EAAEX,MAAM,CAACY,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACLN,SAAS,CAACO,SAAS,CAACC,SAAS,CAACf,MAAM,CAACY,QAAQ,CAACC,IAAI,CAAC;MACnDtD,OAAO,CAAC+C,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAtH,eAAeA,CAAA;IACbuE,OAAO,CAAC+C,GAAG,CAAC,0CAA0C,CAAC;EACzD;EAEAhH,WAAWA,CAAC8F,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEQhC,iBAAiBA,CAAA;IACvB,IAAI,CAACL,QAAQ,GAAG+C,MAAM,CAACC,UAAU,IAAI,GAAG;EAC1C;;;uBA7RW3D,wBAAwB,EAAA1E,EAAA,CAAAoJ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtJ,EAAA,CAAAoJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxB9E,wBAAwB;MAAA+E,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3J,EAAA,CAAA4J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCrClK,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAAsI,uCAAA,kBAAoD;UAiChDpK,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAsD;UACtDV,EAAA,CAAAW,MAAA,0BACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,wCAAiC;UAEjEX,EAFiE,CAAAY,YAAA,EAAI,EAC7D,EACF;UAwFNZ,EArFA,CAAA8B,UAAA,IAAAuI,uCAAA,iBAAiD,KAAAC,wCAAA,iBAcQ,KAAAC,wCAAA,iBAU4B,KAAAC,wCAAA,kBAOS,KAAAC,wCAAA,kBAsDL;UAK3FzK,EAAA,CAAAY,YAAA,EAAM;;;UApIgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA2I,GAAA,CAAA9E,QAAA,CAAc;UA0C5CrF,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA2I,GAAA,CAAArF,SAAA,CAAe;UAcf9E,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA2I,GAAA,CAAA9H,KAAA,KAAA8H,GAAA,CAAArF,SAAA,CAAyB;UAUzB9E,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA2I,GAAA,CAAArF,SAAA,KAAAqF,GAAA,CAAA9H,KAAA,IAAA8H,GAAA,CAAA3F,cAAA,CAAAuD,MAAA,OAAyD;UAOzD/H,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAA2I,GAAA,CAAArF,SAAA,KAAAqF,GAAA,CAAA9H,KAAA,IAAA8H,GAAA,CAAA3F,cAAA,CAAAuD,MAAA,KAAuD;UAsDvD/H,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA2I,GAAA,CAAArF,SAAA,KAAAqF,GAAA,CAAA9H,KAAA,IAAA8H,GAAA,CAAA3F,cAAA,CAAAuD,MAAA,OAAyD;;;qBDpGrDlI,YAAY,EAAA6K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7K,WAAW,EAAA8K,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}