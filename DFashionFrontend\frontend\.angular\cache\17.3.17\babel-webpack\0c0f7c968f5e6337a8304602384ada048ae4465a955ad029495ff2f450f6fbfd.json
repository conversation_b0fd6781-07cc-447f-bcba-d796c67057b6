{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/mobile-optimization.service\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = [\"*\"];\nfunction MobileLayoutComponent_header_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n}\nfunction MobileLayoutComponent_header_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 36);\n  }\n}\nfunction MobileLayoutComponent_header_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"header\", 45)(1, \"div\", 46)(2, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleMenu());\n    });\n    i0.ɵɵtemplate(3, MobileLayoutComponent_header_1_i_3_Template, 1, 0, \"i\", 48)(4, MobileLayoutComponent_header_1_i_4_Template, 1, 0, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50);\n    i0.ɵɵelement(6, \"img\", 51);\n    i0.ɵɵelementStart(7, \"span\", 52);\n    i0.ɵɵtext(8, \"DFashion\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 53)(10, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(11, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 56);\n    i0.ɵɵelement(13, \"i\", 57);\n    i0.ɵɵtemplate(14, MobileLayoutComponent_header_1_span_14_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 59);\n    i0.ɵɵelement(16, \"i\", 60);\n    i0.ɵɵtemplate(17, MobileLayoutComponent_header_1_span_17_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"div\", 62)(20, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function MobileLayoutComponent_header_1_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function MobileLayoutComponent_header_1_Template_input_keyup_enter_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelement(22, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(24, \"i\", 36);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isMenuOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMenuOpen);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSearchOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n  }\n}\nfunction MobileLayoutComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵelement(2, \"img\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentUser.avatar || \"assets/images/default-avatar.svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n  }\n}\nfunction MobileLayoutComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 75)(4, \"h3\");\n    i0.ɵɵtext(5, \"Welcome to DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Sign in for personalized experience\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 76)(9, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(10, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(12, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MobileLayoutComponent_div_36_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_div_36_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"div\", 80);\n    i0.ɵɵelementStart(2, \"a\", 81);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(3, \"i\", 74);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"My Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 82);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(7, \"i\", 83);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"My Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"a\", 84);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(11, \"i\", 60);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MobileLayoutComponent_div_36_span_14_Template, 2, 1, \"span\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"a\", 86);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(16, \"i\", 57);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, MobileLayoutComponent_div_36_span_19_Template, 2, 1, \"span\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 87);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_a_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(21, \"i\", 88);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"div\", 80);\n    i0.ɵɵelementStart(25, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_36_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelement(26, \"i\", 90);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n  }\n}\nfunction MobileLayoutComponent_nav_52_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_nav_52_i_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 74);\n  }\n}\nfunction MobileLayoutComponent_nav_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 92)(1, \"a\", 93);\n    i0.ɵɵelement(2, \"i\", 9);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 94);\n    i0.ɵɵelement(6, \"i\", 11);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Shop\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_nav_52_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleCreateMenu());\n    });\n    i0.ɵɵelementStart(10, \"div\", 96);\n    i0.ɵɵelement(11, \"i\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Create\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"a\", 98);\n    i0.ɵɵelement(15, \"i\", 60);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, MobileLayoutComponent_nav_52_span_18_Template, 2, 1, \"span\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"a\", 100)(20, \"div\", 69);\n    i0.ɵɵtemplate(21, MobileLayoutComponent_nav_52_i_21_Template, 1, 0, \"i\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Profile\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/categories\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCreateMenuOpen);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/wishlist\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/profile\"));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getCurrentUserAvatar());\n  }\n}\nfunction MobileLayoutComponent_footer_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"footer\", 103)(1, \"div\", 104)(2, \"div\", 105)(3, \"a\", 106);\n    i0.ɵɵtext(4, \"About\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 107);\n    i0.ɵɵtext(6, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 108);\n    i0.ɵɵtext(8, \"Privacy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 109);\n    i0.ɵɵtext(10, \"Terms\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 110)(12, \"p\");\n    i0.ɵɵtext(13, \"\\u00A9 2024 DFashion. All rights reserved.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class MobileLayoutComponent {\n  constructor(mobileService, authService, cartService, wishlistService) {\n    this.mobileService = mobileService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.showHeader = true;\n    this.showFooter = true;\n    this.showBottomNav = true;\n    this.menuToggle = new EventEmitter();\n    this.deviceInfo = null;\n    this.breakpoints = null;\n    this.isKeyboardOpen = false;\n    this.currentUser = null;\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n    this.isMenuOpen = false;\n    this.isSearchOpen = false;\n    this.isCreateMenuOpen = false;\n    this.searchQuery = '';\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to device info\n    this.subscriptions.push(this.mobileService.getDeviceInfo$().subscribe(info => {\n      this.deviceInfo = info;\n      this.updateLayoutForDevice();\n    }));\n    // Subscribe to viewport breakpoints\n    this.subscriptions.push(this.mobileService.getViewportBreakpoints$().subscribe(breakpoints => {\n      this.breakpoints = breakpoints;\n      this.updateLayoutForBreakpoint();\n    }));\n    // Subscribe to keyboard state\n    this.subscriptions.push(this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n      this.isKeyboardOpen = isOpen;\n      this.handleKeyboardState(isOpen);\n    }));\n    // Subscribe to auth state\n    this.subscriptions.push(this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        this.loadUserCounts();\n      } else {\n        this.cartCount = 0;\n        this.wishlistCount = 0;\n      }\n    }));\n    // Load initial counts\n    this.loadUserCounts();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  updateLayoutForDevice() {\n    if (!this.deviceInfo) return;\n    // Apply device-specific optimizations\n    if (this.deviceInfo.isMobile) {\n      this.enableMobileOptimizations();\n    } else {\n      this.disableMobileOptimizations();\n    }\n    // Handle orientation changes\n    if (this.deviceInfo.orientation === 'landscape' && this.deviceInfo.isMobile) {\n      this.handleLandscapeMode();\n    } else {\n      this.handlePortraitMode();\n    }\n  }\n  updateLayoutForBreakpoint() {\n    if (!this.breakpoints) return;\n    // Adjust layout based on breakpoints\n    if (this.breakpoints.xs || this.breakpoints.sm) {\n      this.showBottomNav = true;\n      this.enableCompactMode();\n    } else {\n      this.showBottomNav = false;\n      this.disableCompactMode();\n    }\n  }\n  handleKeyboardState(isOpen) {\n    if (isOpen) {\n      // Hide bottom navigation when keyboard is open\n      document.body.classList.add('keyboard-open');\n    } else {\n      document.body.classList.remove('keyboard-open');\n    }\n  }\n  enableMobileOptimizations() {\n    // Enable touch-friendly interactions\n    document.body.classList.add('mobile-device');\n    // Disable hover effects on mobile\n    if (!this.mobileService.supportsHover()) {\n      document.body.classList.add('no-hover');\n    }\n    // Enable GPU acceleration for smooth scrolling\n    const scrollElements = document.querySelectorAll('.scroll-container');\n    scrollElements.forEach(element => {\n      this.mobileService.enableGPUAcceleration(element);\n    });\n  }\n  disableMobileOptimizations() {\n    document.body.classList.remove('mobile-device', 'no-hover');\n  }\n  handleLandscapeMode() {\n    document.body.classList.add('landscape-mode');\n  }\n  handlePortraitMode() {\n    document.body.classList.remove('landscape-mode');\n  }\n  enableCompactMode() {\n    document.body.classList.add('compact-mode');\n  }\n  disableCompactMode() {\n    document.body.classList.remove('compact-mode');\n  }\n  loadUserCounts() {\n    if (!this.currentUser) return;\n    // Set default counts for now\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n  }\n  // Menu Methods\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n    this.menuToggle.emit(this.isMenuOpen);\n    if (this.isMenuOpen) {\n      this.mobileService.disableBodyScroll();\n    } else {\n      this.mobileService.enableBodyScroll();\n    }\n  }\n  closeMenu() {\n    this.isMenuOpen = false;\n    this.menuToggle.emit(false);\n    this.mobileService.enableBodyScroll();\n  }\n  // Search Methods\n  toggleSearch() {\n    this.isSearchOpen = !this.isSearchOpen;\n    if (this.isSearchOpen) {\n      setTimeout(() => {\n        const searchInput = document.querySelector('.mobile-search-input');\n        if (searchInput) {\n          searchInput.focus();\n        }\n      }, 100);\n    }\n  }\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      // Navigate to search results\n      console.log('Searching for:', this.searchQuery);\n      this.isSearchOpen = false;\n      this.searchQuery = '';\n    }\n  }\n  // Navigation Methods\n  navigateToProfile() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  navigateToOrders() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  navigateToSettings() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  logout() {\n    // Simple logout without subscription\n    this.closeMenu();\n  }\n  // Utility Methods\n  getTotalCount() {\n    return this.cartCount + this.wishlistCount;\n  }\n  formatCount(count) {\n    if (count > 99) return '99+';\n    return count.toString();\n  }\n  isCurrentRoute(route) {\n    return window.location.pathname === route;\n  }\n  // Touch Event Handlers\n  onTouchStart(event) {\n    // Handle touch start for custom gestures\n  }\n  onTouchMove(event) {\n    // Handle touch move for custom gestures\n  }\n  onTouchEnd(event) {\n    // Handle touch end for custom gestures\n  }\n  // Create Menu Methods\n  toggleCreateMenu() {\n    this.isCreateMenuOpen = !this.isCreateMenuOpen;\n    if (this.isCreateMenuOpen) {\n      this.closeMenu();\n      this.isSearchOpen = false;\n    }\n  }\n  closeCreateMenu() {\n    this.isCreateMenuOpen = false;\n  }\n  createReel() {\n    this.closeCreateMenu();\n    // Navigate to create reel page\n    console.log('Creating reel...');\n    // TODO: Implement navigation to reel creation\n  }\n  createStory() {\n    this.closeCreateMenu();\n    // Navigate to create story page\n    console.log('Creating story...');\n    // TODO: Implement navigation to story creation\n  }\n  createPost() {\n    this.closeCreateMenu();\n    // Navigate to create post page\n    console.log('Creating post...');\n    // TODO: Implement navigation to post creation\n  }\n  // Get current user avatar\n  getCurrentUserAvatar() {\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  // Performance Optimization\n  trackByIndex(index) {\n    return index;\n  }\n  static {\n    this.ɵfac = function MobileLayoutComponent_Factory(t) {\n      return new (t || MobileLayoutComponent)(i0.ɵɵdirectiveInject(i1.MobileOptimizationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MobileLayoutComponent,\n      selectors: [[\"app-mobile-layout\"]],\n      inputs: {\n        showHeader: \"showHeader\",\n        showFooter: \"showFooter\",\n        showBottomNav: \"showBottomNav\"\n      },\n      outputs: {\n        menuToggle: \"menuToggle\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 86,\n      vars: 32,\n      consts: [[1, \"mobile-layout\"], [\"class\", \"mobile-header\", 4, \"ngIf\"], [1, \"mobile-menu\"], [1, \"menu-overlay\", 3, \"click\"], [1, \"menu-content\"], [\"class\", \"menu-profile\", 4, \"ngIf\"], [\"class\", \"menu-guest\", 4, \"ngIf\"], [1, \"menu-nav\"], [\"routerLink\", \"/\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/categories\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-th-large\"], [\"routerLink\", \"/trending-now\", 1, \"menu-item\", 3, \"click\"], [\"src\", \"assets/svg/trending-now.svg\", \"alt\", \"Trending Now\", 1, \"menu-icon\"], [\"routerLink\", \"/featured-brands\", 1, \"menu-item\", 3, \"click\"], [\"src\", \"assets/svg/featured-brands.svg\", \"alt\", \"Featured Brands\", 1, \"menu-icon\"], [\"routerLink\", \"/new-arrivals\", 1, \"menu-item\", 3, \"click\"], [\"src\", \"assets/svg/new-arrivals.svg\", \"alt\", \"New Arrivals\", 1, \"menu-icon\"], [\"routerLink\", \"/trending\", 1, \"menu-item\", 3, \"click\"], [\"src\", \"assets/svg/trending.svg\", \"alt\", \"Trending\", 1, \"menu-icon\"], [\"routerLink\", \"/offers\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-percent\"], [\"class\", \"menu-section\", 4, \"ngIf\"], [1, \"menu-footer\"], [1, \"app-info\"], [1, \"social-links\"], [\"href\", \"#\", 1, \"social-link\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-facebook\"], [1, \"fab\", \"fa-twitter\"], [1, \"mobile-main\"], [\"class\", \"mobile-bottom-nav\", 4, \"ngIf\"], [1, \"create-menu-overlay\", 3, \"click\"], [1, \"create-menu\", 3, \"click\"], [1, \"create-menu-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"create-options\"], [1, \"create-option\", 3, \"click\"], [1, \"option-icon\"], [\"src\", \"assets/svg/create-reel.svg\", \"alt\", \"Create Reel\"], [1, \"option-content\"], [\"src\", \"assets/svg/create-story.svg\", \"alt\", \"Create Story\"], [\"src\", \"assets/svg/create-post.svg\", \"alt\", \"Create Post\"], [\"class\", \"mobile-footer\", 4, \"ngIf\"], [1, \"mobile-header\"], [1, \"header-content\"], [1, \"header-btn\", \"menu-btn\", 3, \"click\"], [\"class\", \"fas fa-bars\", 4, \"ngIf\"], [\"class\", \"fas fa-times\", 4, \"ngIf\"], [\"routerLink\", \"/\", 1, \"header-logo\"], [\"src\", \"assets/images/logo.svg\", \"alt\", \"DFashion\", 1, \"logo-image\"], [1, \"logo-text\"], [1, \"header-actions\"], [1, \"header-btn\", \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"routerLink\", \"/cart\", 1, \"header-btn\", \"cart-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"badge\", 4, \"ngIf\"], [\"routerLink\", \"/wishlist\", 1, \"header-btn\", \"wishlist-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"mobile-search\"], [1, \"search-container\"], [\"type\", \"text\", \"placeholder\", \"Search for products, brands...\", 1, \"mobile-search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-submit-btn\", 3, \"click\"], [1, \"search-close-btn\", 3, \"click\"], [1, \"fas\", \"fa-bars\"], [1, \"badge\"], [1, \"menu-profile\"], [1, \"profile-avatar\"], [3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"menu-guest\"], [1, \"guest-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"guest-info\"], [1, \"guest-actions\"], [\"routerLink\", \"/login\", 1, \"btn-primary\", 3, \"click\"], [\"routerLink\", \"/register\", 1, \"btn-secondary\", 3, \"click\"], [1, \"menu-section\"], [1, \"menu-divider\"], [\"routerLink\", \"/profile\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/orders\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-box\"], [\"routerLink\", \"/wishlist\", 1, \"menu-item\", 3, \"click\"], [\"class\", \"menu-badge\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/settings\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-cog\"], [1, \"menu-item\", \"logout-btn\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"menu-badge\"], [1, \"mobile-bottom-nav\"], [\"routerLink\", \"/\", 1, \"nav-item\"], [\"routerLink\", \"/categories\", 1, \"nav-item\"], [1, \"nav-item\", \"create-nav\", 3, \"click\"], [1, \"create-btn\"], [1, \"fas\", \"fa-plus\"], [\"routerLink\", \"/wishlist\", 1, \"nav-item\"], [\"class\", \"nav-badge\", 4, \"ngIf\"], [\"routerLink\", \"/profile\", 1, \"nav-item\", \"profile-nav\"], [\"class\", \"fas fa-user\", 4, \"ngIf\"], [1, \"nav-badge\"], [1, \"mobile-footer\"], [1, \"footer-content\"], [1, \"footer-links\"], [\"href\", \"/about\"], [\"href\", \"/contact\"], [\"href\", \"/privacy\"], [\"href\", \"/terms\"], [1, \"footer-copyright\"]],\n      template: function MobileLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, MobileLayoutComponent_header_1_Template, 25, 7, \"header\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_div_click_3_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, MobileLayoutComponent_div_5_Template, 8, 4, \"div\", 5)(6, MobileLayoutComponent_div_6_Template, 13, 0, \"div\", 6);\n          i0.ɵɵelementStart(7, \"nav\", 7)(8, \"a\", 8);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_8_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementStart(10, \"span\");\n          i0.ɵɵtext(11, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_12_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementStart(14, \"span\");\n          i0.ɵɵtext(15, \"Categories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"a\", 12);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_16_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(17, \"img\", 13);\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"Trending Now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_20_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(21, \"img\", 15);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"Featured Brands\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_24_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(25, \"img\", 17);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"New Arrivals\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_28_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(29, \"img\", 19);\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"Trending\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"a\", 20);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_32_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(33, \"i\", 21);\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"Offers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, MobileLayoutComponent_div_36_Template, 29, 2, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 23)(38, \"div\", 24)(39, \"p\");\n          i0.ɵɵtext(40, \"DFashion v1.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"\\u00A9 2024 All rights reserved\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 25)(44, \"a\", 26);\n          i0.ɵɵelement(45, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"a\", 26);\n          i0.ɵɵelement(47, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"a\", 26);\n          i0.ɵɵelement(49, \"i\", 29);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(50, \"main\", 30);\n          i0.ɵɵprojection(51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, MobileLayoutComponent_nav_52_Template, 24, 14, \"nav\", 31);\n          i0.ɵɵelementStart(53, \"div\", 32);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_div_click_53_listener() {\n            return ctx.closeCreateMenu();\n          });\n          i0.ɵɵelementStart(54, \"div\", 33);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_div_click_54_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(55, \"div\", 34)(56, \"h3\");\n          i0.ɵɵtext(57, \"Create New\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_button_click_58_listener() {\n            return ctx.closeCreateMenu();\n          });\n          i0.ɵɵelement(59, \"i\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 37)(61, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_button_click_61_listener() {\n            return ctx.createReel();\n          });\n          i0.ɵɵelementStart(62, \"div\", 39);\n          i0.ɵɵelement(63, \"img\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 41)(65, \"h4\");\n          i0.ɵɵtext(66, \"Reel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"p\");\n          i0.ɵɵtext(68, \"Create a short video\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_button_click_69_listener() {\n            return ctx.createStory();\n          });\n          i0.ɵɵelementStart(70, \"div\", 39);\n          i0.ɵɵelement(71, \"img\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 41)(73, \"h4\");\n          i0.ɵɵtext(74, \"Story\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\");\n          i0.ɵɵtext(76, \"Share a moment\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_button_click_77_listener() {\n            return ctx.createPost();\n          });\n          i0.ɵɵelementStart(78, \"div\", 39);\n          i0.ɵɵelement(79, \"img\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 41)(81, \"h4\");\n          i0.ɵɵtext(82, \"Post\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"p\");\n          i0.ɵɵtext(84, \"Share a photo or video\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(85, MobileLayoutComponent_footer_85_Template, 14, 0, \"footer\", 44);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"menu-open\", ctx.isMenuOpen)(\"search-open\", ctx.isSearchOpen)(\"keyboard-open\", ctx.isKeyboardOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/categories\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/trending-now\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/featured-brands\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/new-arrivals\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/trending\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/offers\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.showBottomNav && !ctx.isKeyboardOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isCreateMenuOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isCreateMenuOpen);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.showFooter && !ctx.showBottomNav);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, RouterModule, i6.RouterLink, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\".mobile-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  position: relative;\\n  overflow-x: hidden;\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  z-index: 1000;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding-top: env(safe-area-inset-top);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  height: 56px;\\n}\\n\\n.header-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  background: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.header-btn[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n.header-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.header-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #333;\\n}\\n\\n.header-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n.header-logo[_ngcontent-%COMP%]   .logo-image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 6px;\\n}\\n.header-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #333;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.mobile-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  transform: translateY(-100%);\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.mobile-search.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  gap: 8px;\\n}\\n\\n.mobile-search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: 1px solid #ddd;\\n  border-radius: 25px;\\n  font-size: 16px;\\n  outline: none;\\n}\\n.mobile-search-input[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n}\\n\\n.search-submit-btn[_ngcontent-%COMP%], .search-close-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  background: #667eea;\\n  color: white;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.search-submit-btn[_ngcontent-%COMP%]:active, .search-close-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.search-close-btn[_ngcontent-%COMP%] {\\n  background: #f0f0f0;\\n  color: #666;\\n}\\n\\n.mobile-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 2000;\\n  visibility: hidden;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%] {\\n  visibility: visible;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%]   .menu-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%]   .menu-content[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n\\n.menu-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.menu-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 280px;\\n  background: white;\\n  transform: translateX(-100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: env(safe-area-inset-top);\\n  padding-bottom: env(safe-area-inset-bottom);\\n}\\n\\n.menu-profile[_ngcontent-%COMP%] {\\n  padding: 24px 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n}\\n.profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  opacity: 0.8;\\n  margin: 0;\\n}\\n\\n.menu-guest[_ngcontent-%COMP%] {\\n  padding: 24px 20px;\\n  background: #f8f9fa;\\n  text-align: center;\\n}\\n\\n.guest-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: #ddd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 16px;\\n}\\n.guest-avatar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #666;\\n}\\n\\n.guest-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.guest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n}\\n.guest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.guest-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%], .guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #5a6fd8;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #667eea;\\n  border: 1px solid #667eea;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #f0f2ff;\\n}\\n\\n.menu-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 0;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px 20px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  cursor: pointer;\\n}\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n.menu-item.active[_ngcontent-%COMP%] {\\n  background: #e7f3ff;\\n  color: #667eea;\\n}\\n.menu-item.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background: #667eea;\\n}\\n.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.menu-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  object-fit: contain;\\n  filter: brightness(0) saturate(100%) invert(20%) sepia(8%) saturate(1000%) hue-rotate(0deg) brightness(95%) contrast(95%);\\n  transition: filter 0.3s ease;\\n}\\n.menu-item.active[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(1352%) hue-rotate(215deg) brightness(119%) contrast(119%);\\n}\\n.menu-item[_ngcontent-%COMP%]:hover   .menu-icon[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(1352%) hue-rotate(215deg) brightness(119%) contrast(119%);\\n}\\n.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.menu-item.logout-btn[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.menu-item.logout-btn[_ngcontent-%COMP%]:hover {\\n  background: #fff5f5;\\n}\\n\\n.menu-badge[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n}\\n\\n.menu-section[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e0e0e0;\\n  margin: 8px 20px;\\n}\\n\\n.menu-footer[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-top: 1px solid #e0e0e0;\\n  background: #f8f9fa;\\n}\\n\\n.app-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.app-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 4px 0;\\n}\\n\\n.social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 16px;\\n}\\n\\n.social-link[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #666;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.social-link[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.mobile-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: calc(56px + env(safe-area-inset-top));\\n  padding-bottom: 100px;\\n  padding-left: 16px;\\n  padding-right: 16px;\\n  min-height: calc(100vh - 156px);\\n  box-sizing: border-box;\\n}\\n.keyboard-open[_ngcontent-%COMP%]   .mobile-main[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.mobile-bottom-nav[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-top: 1px solid #e0e0e0;\\n  display: flex;\\n  z-index: 1000;\\n  padding-bottom: env(safe-area-inset-bottom);\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 8px 4px;\\n  color: #666;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  min-height: 60px;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n.nav-item.active[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.nav-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 4px;\\n  transition: transform 0.3s ease;\\n}\\n.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n.nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 50%;\\n  transform: translateX(50%);\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 8px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 8px;\\n  min-width: 14px;\\n  text-align: center;\\n  line-height: 1;\\n}\\n\\n.profile-nav[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #ddd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 4px;\\n  transition: all 0.3s ease;\\n}\\n.profile-nav[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0;\\n}\\n.profile-nav.active[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  transform: scale(1.1);\\n}\\n\\n.create-nav[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.create-nav[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 4px;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n.create-nav[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 18px;\\n  margin: 0;\\n  font-weight: bold;\\n}\\n.create-nav.active[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%], .create-nav[_ngcontent-%COMP%]:hover   .create-btn[_ngcontent-%COMP%] {\\n  transform: scale(1.15) rotate(45deg);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);\\n  background: linear-gradient(45deg, #764ba2, #667eea);\\n}\\n.create-nav[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n\\n.create-menu-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 2000;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.create-menu-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.create-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-radius: 20px 20px 0 0;\\n  padding: 20px;\\n  padding-bottom: calc(20px + env(safe-area-inset-bottom));\\n  transform: translateY(100%);\\n  transition: transform 0.3s ease;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n}\\n.create-menu.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n\\n.create-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.create-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.create-menu-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  background: #f0f0f0;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.create-menu-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #e0e0e0;\\n}\\n.create-menu-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n.create-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.create-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  border: none;\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: left;\\n}\\n.create-option[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-2px);\\n}\\n.create-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.create-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n.create-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.create-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.create-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n.mobile-footer[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-top: 1px solid #e0e0e0;\\n  padding: 20px 16px;\\n  padding-bottom: calc(20px + env(safe-area-inset-bottom));\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 20px;\\n  margin-bottom: 16px;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 14px;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #667eea;\\n}\\n\\n.footer-copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 480px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .menu-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 320px;\\n  }\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 6px 2px;\\n    min-height: 56px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n@media (orientation: landscape) and (max-height: 500px) {\\n  .mobile-main[_ngcontent-%COMP%] {\\n    padding-top: 48px;\\n    padding-bottom: 0;\\n  }\\n  .mobile-bottom-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .menu-profile[_ngcontent-%COMP%], .menu-guest[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .profile-avatar[_ngcontent-%COMP%], .guest-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .mobile-header[_ngcontent-%COMP%], .mobile-search[_ngcontent-%COMP%], .menu-content[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%], .mobile-footer[_ngcontent-%COMP%] {\\n    background: #1a1a1a;\\n    border-color: #333;\\n  }\\n  .header-btn[_ngcontent-%COMP%], .menu-item[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n  .header-btn[_ngcontent-%COMP%]:hover, .menu-item[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n  }\\n  .mobile-search-input[_ngcontent-%COMP%] {\\n    background: #333;\\n    border-color: #555;\\n    color: #fff;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%] {\\n    transition: none !important;\\n    animation: none !important;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .mobile-header[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%] {\\n    border-width: 2px;\\n  }\\n  .badge[_ngcontent-%COMP%], .nav-badge[_ngcontent-%COMP%], .menu-badge[_ngcontent-%COMP%] {\\n    border: 1px solid #000;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "RouterModule", "FormsModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "formatCount", "cartCount", "wishlistCount", "ɵɵlistener", "MobileLayoutComponent_header_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "toggleMenu", "ɵɵtemplate", "MobileLayoutComponent_header_1_i_3_Template", "MobileLayoutComponent_header_1_i_4_Template", "MobileLayoutComponent_header_1_Template_button_click_10_listener", "toggleSearch", "MobileLayoutComponent_header_1_span_14_Template", "MobileLayoutComponent_header_1_span_17_Template", "ɵɵtwoWayListener", "MobileLayoutComponent_header_1_Template_input_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "searchQuery", "MobileLayoutComponent_header_1_Template_input_keyup_enter_20_listener", "onSearchSubmit", "MobileLayoutComponent_header_1_Template_button_click_21_listener", "MobileLayoutComponent_header_1_Template_button_click_23_listener", "ɵɵproperty", "isMenuOpen", "ɵɵclassProp", "isSearchOpen", "ɵɵtwoWayProperty", "currentUser", "avatar", "ɵɵsanitizeUrl", "fullName", "email", "MobileLayoutComponent_div_6_Template_button_click_9_listener", "_r3", "closeMenu", "MobileLayoutComponent_div_6_Template_button_click_11_listener", "MobileLayoutComponent_div_36_Template_a_click_2_listener", "_r4", "MobileLayoutComponent_div_36_Template_a_click_6_listener", "MobileLayoutComponent_div_36_Template_a_click_10_listener", "MobileLayoutComponent_div_36_span_14_Template", "MobileLayoutComponent_div_36_Template_a_click_15_listener", "MobileLayoutComponent_div_36_span_19_Template", "MobileLayoutComponent_div_36_Template_a_click_20_listener", "MobileLayoutComponent_div_36_Template_button_click_25_listener", "logout", "MobileLayoutComponent_nav_52_Template_button_click_9_listener", "_r5", "toggleCreateMenu", "MobileLayoutComponent_nav_52_span_18_Template", "MobileLayoutComponent_nav_52_i_21_Template", "isCurrentRoute", "isCreateMenuOpen", "ɵɵstyleProp", "getCurrentUserAvatar", "MobileLayoutComponent", "constructor", "mobileService", "authService", "cartService", "wishlistService", "showHeader", "showFooter", "showBottomNav", "menuToggle", "deviceInfo", "breakpoints", "isKeyboardOpen", "subscriptions", "ngOnInit", "push", "getDeviceInfo$", "subscribe", "info", "updateLayoutForDevice", "getViewportBreakpoints$", "updateLayoutForBreakpoint", "getIsKeyboardOpen$", "isOpen", "handleKeyboardState", "currentUser$", "user", "loadUserCounts", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "isMobile", "enableMobileOptimizations", "disableMobileOptimizations", "orientation", "handleLandscapeMode", "handlePortraitMode", "xs", "sm", "enableCompactMode", "disableCompactMode", "document", "body", "classList", "add", "remove", "supportsHover", "scrollElements", "querySelectorAll", "element", "enableGPUAcceleration", "emit", "disableBodyScroll", "enableBodyScroll", "setTimeout", "searchInput", "querySelector", "focus", "trim", "console", "log", "navigateToProfile", "navigateToOrders", "navigateToSettings", "getTotalCount", "count", "toString", "route", "window", "location", "pathname", "onTouchStart", "event", "onTouchMove", "onTouchEnd", "closeCreateMenu", "createReel", "createStory", "createPost", "trackByIndex", "index", "ɵɵdirectiveInject", "i1", "MobileOptimizationService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "MobileLayoutComponent_Template", "rf", "ctx", "MobileLayoutComponent_header_1_Template", "MobileLayoutComponent_Template_div_click_3_listener", "MobileLayoutComponent_div_5_Template", "MobileLayoutComponent_div_6_Template", "MobileLayoutComponent_Template_a_click_8_listener", "MobileLayoutComponent_Template_a_click_12_listener", "MobileLayoutComponent_Template_a_click_16_listener", "MobileLayoutComponent_Template_a_click_20_listener", "MobileLayoutComponent_Template_a_click_24_listener", "MobileLayoutComponent_Template_a_click_28_listener", "MobileLayoutComponent_Template_a_click_32_listener", "MobileLayoutComponent_div_36_Template", "ɵɵprojection", "MobileLayoutComponent_nav_52_Template", "MobileLayoutComponent_Template_div_click_53_listener", "MobileLayoutComponent_Template_div_click_54_listener", "stopPropagation", "MobileLayoutComponent_Template_button_click_58_listener", "MobileLayoutComponent_Template_button_click_61_listener", "MobileLayoutComponent_Template_button_click_69_listener", "MobileLayoutComponent_Template_button_click_77_listener", "MobileLayoutComponent_footer_85_Template", "i5", "NgIf", "i6", "RouterLink", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\shared\\components\\mobile-layout\\mobile-layout.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\shared\\components\\mobile-layout\\mobile-layout.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { Subscription } from 'rxjs';\n\nimport { MobileOptimizationService, DeviceInfo, ViewportBreakpoints } from '../../../core/services/mobile-optimization.service';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistService } from '../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-mobile-layout',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  templateUrl: './mobile-layout.component.html',\n  styleUrls: ['./mobile-layout.component.scss']\n})\nexport class MobileLayoutComponent implements OnInit, OnDestroy {\n  @Input() showHeader = true;\n  @Input() showFooter = true;\n  @Input() showBottomNav = true;\n  @Output() menuToggle = new EventEmitter<boolean>();\n\n  deviceInfo: DeviceInfo | null = null;\n  breakpoints: ViewportBreakpoints | null = null;\n  isKeyboardOpen = false;\n  currentUser: any = null;\n  \n  cartCount = 0;\n  wishlistCount = 0;\n  \n  isMenuOpen = false;\n  isSearchOpen = false;\n  isCreateMenuOpen = false;\n  searchQuery = '';\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private mobileService: MobileOptimizationService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to device info\n    this.subscriptions.push(\n      this.mobileService.getDeviceInfo$().subscribe(info => {\n        this.deviceInfo = info;\n        this.updateLayoutForDevice();\n      })\n    );\n\n    // Subscribe to viewport breakpoints\n    this.subscriptions.push(\n      this.mobileService.getViewportBreakpoints$().subscribe(breakpoints => {\n        this.breakpoints = breakpoints;\n        this.updateLayoutForBreakpoint();\n      })\n    );\n\n    // Subscribe to keyboard state\n    this.subscriptions.push(\n      this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n        this.isKeyboardOpen = isOpen;\n        this.handleKeyboardState(isOpen);\n      })\n    );\n\n    // Subscribe to auth state\n    this.subscriptions.push(\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        if (user) {\n          this.loadUserCounts();\n        } else {\n          this.cartCount = 0;\n          this.wishlistCount = 0;\n        }\n      })\n    );\n\n    // Load initial counts\n    this.loadUserCounts();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private updateLayoutForDevice() {\n    if (!this.deviceInfo) return;\n\n    // Apply device-specific optimizations\n    if (this.deviceInfo.isMobile) {\n      this.enableMobileOptimizations();\n    } else {\n      this.disableMobileOptimizations();\n    }\n\n    // Handle orientation changes\n    if (this.deviceInfo.orientation === 'landscape' && this.deviceInfo.isMobile) {\n      this.handleLandscapeMode();\n    } else {\n      this.handlePortraitMode();\n    }\n  }\n\n  private updateLayoutForBreakpoint() {\n    if (!this.breakpoints) return;\n\n    // Adjust layout based on breakpoints\n    if (this.breakpoints.xs || this.breakpoints.sm) {\n      this.showBottomNav = true;\n      this.enableCompactMode();\n    } else {\n      this.showBottomNav = false;\n      this.disableCompactMode();\n    }\n  }\n\n  private handleKeyboardState(isOpen: boolean) {\n    if (isOpen) {\n      // Hide bottom navigation when keyboard is open\n      document.body.classList.add('keyboard-open');\n    } else {\n      document.body.classList.remove('keyboard-open');\n    }\n  }\n\n  private enableMobileOptimizations() {\n    // Enable touch-friendly interactions\n    document.body.classList.add('mobile-device');\n    \n    // Disable hover effects on mobile\n    if (!this.mobileService.supportsHover()) {\n      document.body.classList.add('no-hover');\n    }\n\n    // Enable GPU acceleration for smooth scrolling\n    const scrollElements = document.querySelectorAll('.scroll-container');\n    scrollElements.forEach(element => {\n      this.mobileService.enableGPUAcceleration(element as HTMLElement);\n    });\n  }\n\n  private disableMobileOptimizations() {\n    document.body.classList.remove('mobile-device', 'no-hover');\n  }\n\n  private handleLandscapeMode() {\n    document.body.classList.add('landscape-mode');\n  }\n\n  private handlePortraitMode() {\n    document.body.classList.remove('landscape-mode');\n  }\n\n  private enableCompactMode() {\n    document.body.classList.add('compact-mode');\n  }\n\n  private disableCompactMode() {\n    document.body.classList.remove('compact-mode');\n  }\n\n  private loadUserCounts() {\n    if (!this.currentUser) return;\n\n    // Set default counts for now\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n  }\n\n  // Menu Methods\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n    this.menuToggle.emit(this.isMenuOpen);\n    \n    if (this.isMenuOpen) {\n      this.mobileService.disableBodyScroll();\n    } else {\n      this.mobileService.enableBodyScroll();\n    }\n  }\n\n  closeMenu() {\n    this.isMenuOpen = false;\n    this.menuToggle.emit(false);\n    this.mobileService.enableBodyScroll();\n  }\n\n  // Search Methods\n  toggleSearch() {\n    this.isSearchOpen = !this.isSearchOpen;\n    \n    if (this.isSearchOpen) {\n      setTimeout(() => {\n        const searchInput = document.querySelector('.mobile-search-input') as HTMLInputElement;\n        if (searchInput) {\n          searchInput.focus();\n        }\n      }, 100);\n    }\n  }\n\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      // Navigate to search results\n      console.log('Searching for:', this.searchQuery);\n      this.isSearchOpen = false;\n      this.searchQuery = '';\n    }\n  }\n\n  // Navigation Methods\n  navigateToProfile() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  navigateToOrders() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  navigateToSettings() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  logout() {\n    // Simple logout without subscription\n    this.closeMenu();\n  }\n\n  // Utility Methods\n  getTotalCount(): number {\n    return this.cartCount + this.wishlistCount;\n  }\n\n  formatCount(count: number): string {\n    if (count > 99) return '99+';\n    return count.toString();\n  }\n\n  isCurrentRoute(route: string): boolean {\n    return window.location.pathname === route;\n  }\n\n  // Touch Event Handlers\n  onTouchStart(event: TouchEvent) {\n    // Handle touch start for custom gestures\n  }\n\n  onTouchMove(event: TouchEvent) {\n    // Handle touch move for custom gestures\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    // Handle touch end for custom gestures\n  }\n\n  // Create Menu Methods\n  toggleCreateMenu() {\n    this.isCreateMenuOpen = !this.isCreateMenuOpen;\n    if (this.isCreateMenuOpen) {\n      this.closeMenu();\n      this.isSearchOpen = false;\n    }\n  }\n\n  closeCreateMenu() {\n    this.isCreateMenuOpen = false;\n  }\n\n  createReel() {\n    this.closeCreateMenu();\n    // Navigate to create reel page\n    console.log('Creating reel...');\n    // TODO: Implement navigation to reel creation\n  }\n\n  createStory() {\n    this.closeCreateMenu();\n    // Navigate to create story page\n    console.log('Creating story...');\n    // TODO: Implement navigation to story creation\n  }\n\n  createPost() {\n    this.closeCreateMenu();\n    // Navigate to create post page\n    console.log('Creating post...');\n    // TODO: Implement navigation to post creation\n  }\n\n  // Get current user avatar\n  getCurrentUserAvatar(): string {\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  // Performance Optimization\n  trackByIndex(index: number): number {\n    return index;\n  }\n}\n", "<div class=\"mobile-layout\" \n     [class.menu-open]=\"isMenuOpen\"\n     [class.search-open]=\"isSearchOpen\"\n     [class.keyboard-open]=\"isKeyboardOpen\">\n\n  <!-- Mobile Header -->\n  <header *ngIf=\"showHeader\" class=\"mobile-header\">\n    <div class=\"header-content\">\n      <!-- <PERSON>u <PERSON> -->\n      <button class=\"header-btn menu-btn\" (click)=\"toggleMenu()\">\n        <i class=\"fas fa-bars\" *ngIf=\"!isMenuOpen\"></i>\n        <i class=\"fas fa-times\" *ngIf=\"isMenuOpen\"></i>\n      </button>\n\n      <!-- Logo -->\n      <div class=\"header-logo\" routerLink=\"/\">\n        <img src=\"assets/images/logo.svg\" alt=\"DFashion\" class=\"logo-image\">\n        <span class=\"logo-text\">DFashion</span>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Search Button -->\n        <button class=\"header-btn search-btn\" (click)=\"toggleSearch()\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n\n        <!-- Cart <PERSON>ton -->\n        <button class=\"header-btn cart-btn\" routerLink=\"/cart\">\n          <i class=\"fas fa-shopping-cart\"></i>\n          <span *ngIf=\"cartCount > 0\" class=\"badge\">{{ formatCount(cartCount) }}</span>\n        </button>\n\n        <!-- Wishlist Button -->\n        <button class=\"header-btn wishlist-btn\" routerLink=\"/wishlist\">\n          <i class=\"fas fa-heart\"></i>\n          <span *ngIf=\"wishlistCount > 0\" class=\"badge\">{{ formatCount(wishlistCount) }}</span>\n        </button>\n      </div>\n    </div>\n\n    <!-- Mobile Search Bar -->\n    <div class=\"mobile-search\" [class.active]=\"isSearchOpen\">\n      <div class=\"search-container\">\n        <input \n          type=\"text\" \n          class=\"mobile-search-input\"\n          [(ngModel)]=\"searchQuery\"\n          (keyup.enter)=\"onSearchSubmit()\"\n          placeholder=\"Search for products, brands...\">\n        <button class=\"search-submit-btn\" (click)=\"onSearchSubmit()\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n        <button class=\"search-close-btn\" (click)=\"toggleSearch()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Mobile Side Menu -->\n  <div class=\"mobile-menu\" [class.active]=\"isMenuOpen\">\n    <div class=\"menu-overlay\" (click)=\"closeMenu()\"></div>\n    <div class=\"menu-content\">\n      <!-- User Profile Section -->\n      <div class=\"menu-profile\" *ngIf=\"currentUser\">\n        <div class=\"profile-avatar\">\n          <img [src]=\"currentUser.avatar || 'assets/images/default-avatar.svg'\"\n               [alt]=\"currentUser.fullName\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>{{ currentUser.fullName }}</h3>\n          <p>{{ currentUser.email }}</p>\n        </div>\n      </div>\n\n      <!-- Guest Section -->\n      <div class=\"menu-guest\" *ngIf=\"!currentUser\">\n        <div class=\"guest-avatar\">\n          <i class=\"fas fa-user\"></i>\n        </div>\n        <div class=\"guest-info\">\n          <h3>Welcome to DFashion</h3>\n          <p>Sign in for personalized experience</p>\n        </div>\n        <div class=\"guest-actions\">\n          <button class=\"btn-primary\" routerLink=\"/login\" (click)=\"closeMenu()\">Sign In</button>\n          <button class=\"btn-secondary\" routerLink=\"/register\" (click)=\"closeMenu()\">Sign Up</button>\n        </div>\n      </div>\n\n      <!-- Menu Items -->\n      <nav class=\"menu-nav\">\n        <a routerLink=\"/\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/')\">\n          <i class=\"fas fa-home\"></i>\n          <span>Home</span>\n        </a>\n        \n        <a routerLink=\"/categories\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/categories')\">\n          <i class=\"fas fa-th-large\"></i>\n          <span>Categories</span>\n        </a>\n        \n        <a routerLink=\"/trending-now\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/trending-now')\">\n          <img src=\"assets/svg/trending-now.svg\" alt=\"Trending Now\" class=\"menu-icon\">\n          <span>Trending Now</span>\n        </a>\n\n        <a routerLink=\"/featured-brands\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/featured-brands')\">\n          <img src=\"assets/svg/featured-brands.svg\" alt=\"Featured Brands\" class=\"menu-icon\">\n          <span>Featured Brands</span>\n        </a>\n\n        <a routerLink=\"/new-arrivals\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/new-arrivals')\">\n          <img src=\"assets/svg/new-arrivals.svg\" alt=\"New Arrivals\" class=\"menu-icon\">\n          <span>New Arrivals</span>\n        </a>\n\n        <a routerLink=\"/trending\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/trending')\">\n          <img src=\"assets/svg/trending.svg\" alt=\"Trending\" class=\"menu-icon\">\n          <span>Trending</span>\n        </a>\n        \n        <a routerLink=\"/offers\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/offers')\">\n          <i class=\"fas fa-percent\"></i>\n          <span>Offers</span>\n        </a>\n\n        <!-- Authenticated User Menu Items -->\n        <div *ngIf=\"currentUser\" class=\"menu-section\">\n          <div class=\"menu-divider\"></div>\n          \n          <a routerLink=\"/profile\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-user\"></i>\n            <span>My Profile</span>\n          </a>\n          \n          <a routerLink=\"/orders\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-box\"></i>\n            <span>My Orders</span>\n          </a>\n          \n          <a routerLink=\"/wishlist\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n            <span *ngIf=\"wishlistCount > 0\" class=\"menu-badge\">{{ formatCount(wishlistCount) }}</span>\n          </a>\n          \n          <a routerLink=\"/cart\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Cart</span>\n            <span *ngIf=\"cartCount > 0\" class=\"menu-badge\">{{ formatCount(cartCount) }}</span>\n          </a>\n          \n          <a routerLink=\"/settings\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-cog\"></i>\n            <span>Settings</span>\n          </a>\n          \n          <div class=\"menu-divider\"></div>\n          \n          <button class=\"menu-item logout-btn\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i>\n            <span>Logout</span>\n          </button>\n        </div>\n      </nav>\n\n      <!-- App Info -->\n      <div class=\"menu-footer\">\n        <div class=\"app-info\">\n          <p>DFashion v1.0</p>\n          <p>© 2024 All rights reserved</p>\n        </div>\n        <div class=\"social-links\">\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-instagram\"></i>\n          </a>\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-facebook\"></i>\n          </a>\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-twitter\"></i>\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <main class=\"mobile-main\">\n    <ng-content></ng-content>\n  </main>\n\n  <!-- Mobile Bottom Navigation -->\n  <nav *ngIf=\"showBottomNav && !isKeyboardOpen\" class=\"mobile-bottom-nav\">\n    <a routerLink=\"/\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/')\">\n      <i class=\"fas fa-home\"></i>\n      <span>Home</span>\n    </a>\n    \n    <a routerLink=\"/categories\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/categories')\">\n      <i class=\"fas fa-th-large\"></i>\n      <span>Shop</span>\n    </a>\n    \n    <button class=\"nav-item create-nav\" (click)=\"toggleCreateMenu()\" [class.active]=\"isCreateMenuOpen\">\n      <div class=\"create-btn\">\n        <i class=\"fas fa-plus\"></i>\n      </div>\n      <span>Create</span>\n    </button>\n    \n    <a routerLink=\"/wishlist\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/wishlist')\">\n      <i class=\"fas fa-heart\"></i>\n      <span>Wishlist</span>\n      <span *ngIf=\"wishlistCount > 0\" class=\"nav-badge\">{{ formatCount(wishlistCount) }}</span>\n    </a>\n    \n    <a routerLink=\"/profile\" class=\"nav-item profile-nav\" [class.active]=\"isCurrentRoute('/profile')\">\n      <div class=\"profile-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\">\n        <i class=\"fas fa-user\" *ngIf=\"!getCurrentUserAvatar()\"></i>\n      </div>\n      <span>Profile</span>\n    </a>\n  </nav>\n\n  <!-- Create Menu Popup -->\n  <div class=\"create-menu-overlay\" [class.active]=\"isCreateMenuOpen\" (click)=\"closeCreateMenu()\">\n    <div class=\"create-menu\" [class.active]=\"isCreateMenuOpen\" (click)=\"$event.stopPropagation()\">\n      <div class=\"create-menu-header\">\n        <h3>Create New</h3>\n        <button class=\"close-btn\" (click)=\"closeCreateMenu()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <div class=\"create-options\">\n        <button class=\"create-option\" (click)=\"createReel()\">\n          <div class=\"option-icon\">\n            <img src=\"assets/svg/create-reel.svg\" alt=\"Create Reel\">\n          </div>\n          <div class=\"option-content\">\n            <h4>Reel</h4>\n            <p>Create a short video</p>\n          </div>\n        </button>\n\n        <button class=\"create-option\" (click)=\"createStory()\">\n          <div class=\"option-icon\">\n            <img src=\"assets/svg/create-story.svg\" alt=\"Create Story\">\n          </div>\n          <div class=\"option-content\">\n            <h4>Story</h4>\n            <p>Share a moment</p>\n          </div>\n        </button>\n\n        <button class=\"create-option\" (click)=\"createPost()\">\n          <div class=\"option-icon\">\n            <img src=\"assets/svg/create-post.svg\" alt=\"Create Post\">\n          </div>\n          <div class=\"option-content\">\n            <h4>Post</h4>\n            <p>Share a photo or video</p>\n          </div>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Footer -->\n  <footer *ngIf=\"showFooter && !showBottomNav\" class=\"mobile-footer\">\n    <div class=\"footer-content\">\n      <div class=\"footer-links\">\n        <a href=\"/about\">About</a>\n        <a href=\"/contact\">Contact</a>\n        <a href=\"/privacy\">Privacy</a>\n        <a href=\"/terms\">Terms</a>\n      </div>\n      <div class=\"footer-copyright\">\n        <p>© 2024 DFashion. All rights reserved.</p>\n      </div>\n    </div>\n  </footer>\n</div>\n"], "mappings": "AAAA,SAAsDA,YAAY,QAAQ,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICOpCC,EAAA,CAAAC,SAAA,YAA+C;;;;;IAC/CD,EAAA,CAAAC,SAAA,YAA+C;;;;;IAmB7CD,EAAA,CAAAE,cAAA,eAA0C;IAAAF,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAE,SAAA,EAA4B;;;;;IAMtET,EAAA,CAAAE,cAAA,eAA8C;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;;IA3BlFV,EAHJ,CAAAE,cAAA,iBAAiD,cACnB,iBAEiC;IAAvBF,EAAA,CAAAW,UAAA,mBAAAC,gEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,UAAA,EAAY;IAAA,EAAC;IAExDjB,EADA,CAAAkB,UAAA,IAAAC,2CAAA,gBAA2C,IAAAC,2CAAA,gBACA;IAC7CpB,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,cAAwC;IACtCF,EAAA,CAAAC,SAAA,cAAoE;IACpED,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAClCH,EADkC,CAAAI,YAAA,EAAO,EACnC;IAKJJ,EAFF,CAAAE,cAAA,cAA4B,kBAEqC;IAAzBF,EAAA,CAAAW,UAAA,mBAAAU,iEAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAC5DtB,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAAuD;IACrDF,EAAA,CAAAC,SAAA,aAAoC;IACpCD,EAAA,CAAAkB,UAAA,KAAAK,+CAAA,mBAA0C;IAC5CvB,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAA+D;IAC7DF,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAkB,UAAA,KAAAM,+CAAA,mBAA8C;IAGpDxB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAKFJ,EAFJ,CAAAE,cAAA,eAAyD,eACzB,iBAMmB;IAF7CF,EAAA,CAAAyB,gBAAA,2BAAAC,wEAAAC,MAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA4B,kBAAA,CAAArB,MAAA,CAAAsB,WAAA,EAAAF,MAAA,MAAApB,MAAA,CAAAsB,WAAA,GAAAF,MAAA;MAAA,OAAA3B,EAAA,CAAAgB,WAAA,CAAAW,MAAA;IAAA,EAAyB;IACzB3B,EAAA,CAAAW,UAAA,yBAAAmB,sEAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAeT,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IAJlC/B,EAAA,CAAAI,YAAA,EAK+C;IAC/CJ,EAAA,CAAAE,cAAA,kBAA6D;IAA3BF,EAAA,CAAAW,UAAA,mBAAAqB,iEAAA;MAAAhC,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IAC1D/B,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAA0D;IAAzBF,EAAA,CAAAW,UAAA,mBAAAsB,iEAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IACvDtB,EAAA,CAAAC,SAAA,aAA4B;IAIpCD,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACC;;;;IAhDqBJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAkC,UAAA,UAAA3B,MAAA,CAAA4B,UAAA,CAAiB;IAChBnC,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAA4B,UAAA,CAAgB;IAmBhCnC,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAE,SAAA,KAAmB;IAMnBT,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAMTV,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAA8B,YAAA,CAA6B;IAKlDrC,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAsC,gBAAA,YAAA/B,MAAA,CAAAsB,WAAA,CAAyB;;;;;IAmB3B7B,EADF,CAAAE,cAAA,cAA8C,cAChB;IAC1BF,EAAA,CAAAC,SAAA,cACkC;IACpCD,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,GAAuB;IAE9BH,EAF8B,CAAAI,YAAA,EAAI,EAC1B,EACF;;;;IAPGJ,EAAA,CAAAK,SAAA,GAAgE;IAChEL,EADA,CAAAkC,UAAA,QAAA3B,MAAA,CAAAgC,WAAA,CAAAC,MAAA,wCAAAxC,EAAA,CAAAyC,aAAA,CAAgE,QAAAlC,MAAA,CAAAgC,WAAA,CAAAG,QAAA,CACpC;IAG7B1C,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAgC,WAAA,CAAAG,QAAA,CAA0B;IAC3B1C,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAgC,WAAA,CAAAI,KAAA,CAAuB;;;;;;IAM5B3C,EADF,CAAAE,cAAA,cAA6C,cACjB;IACxBF,EAAA,CAAAC,SAAA,YAA2B;IAC7BD,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,cAAwB,SAClB;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,0CAAmC;IACxCH,EADwC,CAAAI,YAAA,EAAI,EACtC;IAEJJ,EADF,CAAAE,cAAA,cAA2B,iBAC6C;IAAtBF,EAAA,CAAAW,UAAA,mBAAAiC,6DAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAAC9C,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtFJ,EAAA,CAAAE,cAAA,kBAA2E;IAAtBF,EAAA,CAAAW,UAAA,mBAAAoC,8DAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAAC9C,EAAA,CAAAG,MAAA,eAAO;IAEtFH,EAFsF,CAAAI,YAAA,EAAS,EACvF,EACF;;;;;IAwDAJ,EAAA,CAAAE,cAAA,eAAmD;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;IAMnFV,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAE,SAAA,EAA4B;;;;;;IAtB/ET,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAC,SAAA,cAAgC;IAEhCD,EAAA,CAAAE,cAAA,YAAiE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAqC,yDAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC5C9C,EAAA,CAAAC,SAAA,YAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAClBH,EADkB,CAAAI,YAAA,EAAO,EACrB;IAEJJ,EAAA,CAAAE,cAAA,YAAgE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAuC,yDAAA;MAAAlD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC3C9C,EAAA,CAAAC,SAAA,YAA0B;IAC1BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IACjBH,EADiB,CAAAI,YAAA,EAAO,EACpB;IAEJJ,EAAA,CAAAE,cAAA,aAAkE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAwC,0DAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC7C9C,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAkB,UAAA,KAAAkC,6CAAA,mBAAmD;IACrDpD,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAE,cAAA,aAA8D;IAAxCF,EAAA,CAAAW,UAAA,mBAAA0C,0DAAA;MAAArD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IACzC9C,EAAA,CAAAC,SAAA,aAAoC;IACpCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjBJ,EAAA,CAAAkB,UAAA,KAAAoC,6CAAA,mBAA+C;IACjDtD,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAE,cAAA,aAAkE;IAAxCF,EAAA,CAAAW,UAAA,mBAAA4C,0DAAA;MAAAvD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC7C9C,EAAA,CAAAC,SAAA,aAA0B;IAC1BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACnB;IAEJJ,EAAA,CAAAC,SAAA,eAAgC;IAEhCD,EAAA,CAAAE,cAAA,kBAAwD;IAAnBF,EAAA,CAAAW,UAAA,mBAAA6C,+DAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAkD,MAAA,EAAQ;IAAA,EAAC;IACrDzD,EAAA,CAAAC,SAAA,aAAmC;IACnCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAEhBH,EAFgB,CAAAI,YAAA,EAAO,EACZ,EACL;;;;IApBKJ,EAAA,CAAAK,SAAA,IAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAMvBV,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAE,SAAA,KAAmB;;;;;IAiEhCT,EAAA,CAAAE,cAAA,gBAAkD;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;IAKhFV,EAAA,CAAAC,SAAA,YAA2D;;;;;;IAzB/DD,EADF,CAAAE,cAAA,cAAwE,YACE;IACtEF,EAAA,CAAAC,SAAA,WAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,WAAI;IACZH,EADY,CAAAI,YAAA,EAAO,EACf;IAEJJ,EAAA,CAAAE,cAAA,YAA4F;IAC1FF,EAAA,CAAAC,SAAA,YAA+B;IAC/BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,WAAI;IACZH,EADY,CAAAI,YAAA,EAAO,EACf;IAEJJ,EAAA,CAAAE,cAAA,iBAAmG;IAA/DF,EAAA,CAAAW,UAAA,mBAAA+C,8DAAA;MAAA1D,EAAA,CAAAa,aAAA,CAAA8C,GAAA;MAAA,MAAApD,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAqD,gBAAA,EAAkB;IAAA,EAAC;IAC9D5D,EAAA,CAAAE,cAAA,eAAwB;IACtBF,EAAA,CAAAC,SAAA,aAA2B;IAC7BD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,cAAM;IACdH,EADc,CAAAI,YAAA,EAAO,EACZ;IAETJ,EAAA,CAAAE,cAAA,aAAwF;IACtFF,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAkB,UAAA,KAAA2C,6CAAA,mBAAkD;IACpD7D,EAAA,CAAAI,YAAA,EAAI;IAGFJ,EADF,CAAAE,cAAA,cAAkG,eACH;IAC3FF,EAAA,CAAAkB,UAAA,KAAA4C,0CAAA,iBAAuD;IACzD9D,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAEjBH,EAFiB,CAAAI,YAAA,EAAO,EAClB,EACA;;;;IA7B+BJ,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAwD,cAAA,MAAoC;IAK1B/D,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAwD,cAAA,gBAA8C;IAK1B/D,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAyD,gBAAA,CAAiC;IAOvDhE,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAwD,cAAA,cAA4C;IAG9E/D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAGsBV,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAwD,cAAA,aAA2C;IACnE/D,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAiE,WAAA,8BAAA1D,MAAA,CAAA2D,oBAAA,SAAgE;IAClElE,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAkC,UAAA,UAAA3B,MAAA,CAAA2D,oBAAA,GAA6B;;;;;IAsDrDlE,EAHN,CAAAE,cAAA,kBAAmE,eACrC,eACA,aACP;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC1BJ,EAAA,CAAAE,cAAA,aAAmB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9BJ,EAAA,CAAAE,cAAA,aAAmB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9BJ,EAAA,CAAAE,cAAA,aAAiB;IAAAF,EAAA,CAAAG,MAAA,aAAK;IACxBH,EADwB,CAAAI,YAAA,EAAI,EACtB;IAEJJ,EADF,CAAAE,cAAA,gBAA8B,SACzB;IAAAF,EAAA,CAAAG,MAAA,kDAAqC;IAG9CH,EAH8C,CAAAI,YAAA,EAAI,EACxC,EACF,EACC;;;AD1QX,OAAM,MAAO+D,qBAAqB;EAqBhCC,YACUC,aAAwC,EACxCC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAxBhB,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,aAAa,GAAG,IAAI;IACnB,KAAAC,UAAU,GAAG,IAAIhF,YAAY,EAAW;IAElD,KAAAiF,UAAU,GAAsB,IAAI;IACpC,KAAAC,WAAW,GAA+B,IAAI;IAC9C,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAxC,WAAW,GAAQ,IAAI;IAEvB,KAAA9B,SAAS,GAAG,CAAC;IACb,KAAAC,aAAa,GAAG,CAAC;IAEjB,KAAAyB,UAAU,GAAG,KAAK;IAClB,KAAAE,YAAY,GAAG,KAAK;IACpB,KAAA2B,gBAAgB,GAAG,KAAK;IACxB,KAAAnC,WAAW,GAAG,EAAE;IAER,KAAAmD,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACc,cAAc,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACnD,IAAI,CAACR,UAAU,GAAGQ,IAAI;MACtB,IAAI,CAACC,qBAAqB,EAAE;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACN,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACkB,uBAAuB,EAAE,CAACH,SAAS,CAACN,WAAW,IAAG;MACnE,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACU,yBAAyB,EAAE;IAClC,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACR,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACoB,kBAAkB,EAAE,CAACL,SAAS,CAACM,MAAM,IAAG;MACzD,IAAI,CAACX,cAAc,GAAGW,MAAM;MAC5B,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAAC;IAClC,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACV,aAAa,CAACE,IAAI,CACrB,IAAI,CAACZ,WAAW,CAACsB,YAAY,CAACR,SAAS,CAACS,IAAI,IAAG;MAC7C,IAAI,CAACtD,WAAW,GAAGsD,IAAI;MACvB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM;QACL,IAAI,CAACrF,SAAS,GAAG,CAAC;QAClB,IAAI,CAACC,aAAa,GAAG,CAAC;;IAE1B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACoF,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,aAAa,CAACgB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQZ,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE;IAEtB;IACA,IAAI,IAAI,CAACA,UAAU,CAACsB,QAAQ,EAAE;MAC5B,IAAI,CAACC,yBAAyB,EAAE;KACjC,MAAM;MACL,IAAI,CAACC,0BAA0B,EAAE;;IAGnC;IACA,IAAI,IAAI,CAACxB,UAAU,CAACyB,WAAW,KAAK,WAAW,IAAI,IAAI,CAACzB,UAAU,CAACsB,QAAQ,EAAE;MAC3E,IAAI,CAACI,mBAAmB,EAAE;KAC3B,MAAM;MACL,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEQhB,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;IAEvB;IACA,IAAI,IAAI,CAACA,WAAW,CAAC2B,EAAE,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,EAAE,EAAE;MAC9C,IAAI,CAAC/B,aAAa,GAAG,IAAI;MACzB,IAAI,CAACgC,iBAAiB,EAAE;KACzB,MAAM;MACL,IAAI,CAAChC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACiC,kBAAkB,EAAE;;EAE7B;EAEQjB,mBAAmBA,CAACD,MAAe;IACzC,IAAIA,MAAM,EAAE;MACV;MACAmB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;KAC7C,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;;EAEnD;EAEQb,yBAAyBA,CAAA;IAC/B;IACAS,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAE5C;IACA,IAAI,CAAC,IAAI,CAAC3C,aAAa,CAAC6C,aAAa,EAAE,EAAE;MACvCL,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;IAGzC;IACA,MAAMG,cAAc,GAAGN,QAAQ,CAACO,gBAAgB,CAAC,mBAAmB,CAAC;IACrED,cAAc,CAACnB,OAAO,CAACqB,OAAO,IAAG;MAC/B,IAAI,CAAChD,aAAa,CAACiD,qBAAqB,CAACD,OAAsB,CAAC;IAClE,CAAC,CAAC;EACJ;EAEQhB,0BAA0BA,CAAA;IAChCQ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC;EAC7D;EAEQV,mBAAmBA,CAAA;IACzBM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAC/C;EAEQR,kBAAkBA,CAAA;IACxBK,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;EAClD;EAEQN,iBAAiBA,CAAA;IACvBE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7C;EAEQJ,kBAAkBA,CAAA;IACxBC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,cAAc,CAAC;EAChD;EAEQnB,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvD,WAAW,EAAE;IAEvB;IACA,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;EAEA;EACAO,UAAUA,CAAA;IACR,IAAI,CAACkB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACyC,UAAU,CAAC2C,IAAI,CAAC,IAAI,CAACpF,UAAU,CAAC;IAErC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnB,IAAI,CAACkC,aAAa,CAACmD,iBAAiB,EAAE;KACvC,MAAM;MACL,IAAI,CAACnD,aAAa,CAACoD,gBAAgB,EAAE;;EAEzC;EAEA3E,SAASA,CAAA;IACP,IAAI,CAACX,UAAU,GAAG,KAAK;IACvB,IAAI,CAACyC,UAAU,CAAC2C,IAAI,CAAC,KAAK,CAAC;IAC3B,IAAI,CAAClD,aAAa,CAACoD,gBAAgB,EAAE;EACvC;EAEA;EACAnG,YAAYA,CAAA;IACV,IAAI,CAACe,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACA,YAAY,EAAE;MACrBqF,UAAU,CAAC,MAAK;QACd,MAAMC,WAAW,GAAGd,QAAQ,CAACe,aAAa,CAAC,sBAAsB,CAAqB;QACtF,IAAID,WAAW,EAAE;UACfA,WAAW,CAACE,KAAK,EAAE;;MAEvB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA9F,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACF,WAAW,CAACiG,IAAI,EAAE,EAAE;MAC3B;MACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACnG,WAAW,CAAC;MAC/C,IAAI,CAACQ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACR,WAAW,GAAG,EAAE;;EAEzB;EAEA;EACAoG,iBAAiBA,CAAA;IACf,IAAI,CAACnF,SAAS,EAAE;IAChB;EACF;EAEAoF,gBAAgBA,CAAA;IACd,IAAI,CAACpF,SAAS,EAAE;IAChB;EACF;EAEAqF,kBAAkBA,CAAA;IAChB,IAAI,CAACrF,SAAS,EAAE;IAChB;EACF;EAEAW,MAAMA,CAAA;IACJ;IACA,IAAI,CAACX,SAAS,EAAE;EAClB;EAEA;EACAsF,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC3H,SAAS,GAAG,IAAI,CAACC,aAAa;EAC5C;EAEAF,WAAWA,CAAC6H,KAAa;IACvB,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,KAAK;IAC5B,OAAOA,KAAK,CAACC,QAAQ,EAAE;EACzB;EAEAvE,cAAcA,CAACwE,KAAa;IAC1B,OAAOC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKH,KAAK;EAC3C;EAEA;EACAI,YAAYA,CAACC,KAAiB;IAC5B;EAAA;EAGFC,WAAWA,CAACD,KAAiB;IAC3B;EAAA;EAGFE,UAAUA,CAACF,KAAiB;IAC1B;EAAA;EAGF;EACAhF,gBAAgBA,CAAA;IACd,IAAI,CAACI,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,IAAI,CAACA,gBAAgB,EAAE;MACzB,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACT,YAAY,GAAG,KAAK;;EAE7B;EAEA0G,eAAeA,CAAA;IACb,IAAI,CAAC/E,gBAAgB,GAAG,KAAK;EAC/B;EAEAgF,UAAUA,CAAA;IACR,IAAI,CAACD,eAAe,EAAE;IACtB;IACAhB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEAiB,WAAWA,CAAA;IACT,IAAI,CAACF,eAAe,EAAE;IACtB;IACAhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC;EACF;EAEAkB,UAAUA,CAAA;IACR,IAAI,CAACH,eAAe,EAAE;IACtB;IACAhB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACA9D,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3B,WAAW,EAAEC,MAAM,IAAI,mCAAmC;EACxE;EAEA;EACA2G,YAAYA,CAACC,KAAa;IACxB,OAAOA,KAAK;EACd;;;uBAjSWjF,qBAAqB,EAAAnE,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,yBAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAArB1F,qBAAqB;MAAA2F,SAAA;MAAAC,MAAA;QAAAtF,UAAA;QAAAC,UAAA;QAAAC,aAAA;MAAA;MAAAqF,OAAA;QAAApF,UAAA;MAAA;MAAAqF,UAAA;MAAAC,QAAA,GAAAlK,EAAA,CAAAmK,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClBlC3K,EAAA,CAAAE,cAAA,aAG4C;UAG1CF,EAAA,CAAAkB,UAAA,IAAA2J,uCAAA,qBAAiD;UAwD/C7K,EADF,CAAAE,cAAA,aAAqD,aACH;UAAtBF,EAAA,CAAAW,UAAA,mBAAAmK,oDAAA;YAAA,OAASF,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UAAC9C,EAAA,CAAAI,YAAA,EAAM;UACtDJ,EAAA,CAAAE,cAAA,aAA0B;UAcxBF,EAZA,CAAAkB,UAAA,IAAA6J,oCAAA,iBAA8C,IAAAC,oCAAA,kBAYD;UAgB3ChL,EADF,CAAAE,cAAA,aAAsB,WAC2E;UAA7EF,EAAA,CAAAW,UAAA,mBAAAsK,kDAAA;YAAA,OAASL,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UACrC9C,EAAA,CAAAC,SAAA,WAA2B;UAC3BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,YAAI;UACZH,EADY,CAAAI,YAAA,EAAO,EACf;UAEJJ,EAAA,CAAAE,cAAA,aAAmH;UAAvFF,EAAA,CAAAW,UAAA,mBAAAuK,mDAAA;YAAA,OAASN,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UAC/C9C,EAAA,CAAAC,SAAA,aAA+B;UAC/BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,kBAAU;UAClBH,EADkB,CAAAI,YAAA,EAAO,EACrB;UAEJJ,EAAA,CAAAE,cAAA,aAAuH;UAAzFF,EAAA,CAAAW,UAAA,mBAAAwK,mDAAA;YAAA,OAASP,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UACjD9C,EAAA,CAAAC,SAAA,eAA4E;UAC5ED,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UACpBH,EADoB,CAAAI,YAAA,EAAO,EACvB;UAEJJ,EAAA,CAAAE,cAAA,aAA6H;UAA5FF,EAAA,CAAAW,UAAA,mBAAAyK,mDAAA;YAAA,OAASR,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UACpD9C,EAAA,CAAAC,SAAA,eAAkF;UAClFD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,uBAAe;UACvBH,EADuB,CAAAI,YAAA,EAAO,EAC1B;UAEJJ,EAAA,CAAAE,cAAA,aAAuH;UAAzFF,EAAA,CAAAW,UAAA,mBAAA0K,mDAAA;YAAA,OAAST,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UACjD9C,EAAA,CAAAC,SAAA,eAA4E;UAC5ED,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UACpBH,EADoB,CAAAI,YAAA,EAAO,EACvB;UAEJJ,EAAA,CAAAE,cAAA,aAA+G;UAArFF,EAAA,CAAAW,UAAA,mBAAA2K,mDAAA;YAAA,OAASV,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UAC7C9C,EAAA,CAAAC,SAAA,eAAoE;UACpED,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAChBH,EADgB,CAAAI,YAAA,EAAO,EACnB;UAEJJ,EAAA,CAAAE,cAAA,aAA2G;UAAnFF,EAAA,CAAAW,UAAA,mBAAA4K,mDAAA;YAAA,OAASX,GAAA,CAAA9H,SAAA,EAAW;UAAA,EAAC;UAC3C9C,EAAA,CAAAC,SAAA,aAA8B;UAC9BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,cAAM;UACdH,EADc,CAAAI,YAAA,EAAO,EACjB;UAGJJ,EAAA,CAAAkB,UAAA,KAAAsK,qCAAA,mBAA8C;UAqChDxL,EAAA,CAAAI,YAAA,EAAM;UAKFJ,EAFJ,CAAAE,cAAA,eAAyB,eACD,SACjB;UAAAF,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpBJ,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAG,MAAA,uCAA0B;UAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;UAEJJ,EADF,CAAAE,cAAA,eAA0B,aACQ;UAC9BF,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAE,cAAA,aAAgC;UAC9BF,EAAA,CAAAC,SAAA,aAA+B;UACjCD,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAE,cAAA,aAAgC;UAC9BF,EAAA,CAAAC,SAAA,aAA8B;UAKxCD,EAJQ,CAAAI,YAAA,EAAI,EACA,EACF,EACF,EACF;UAGNJ,EAAA,CAAAE,cAAA,gBAA0B;UACxBF,EAAA,CAAAyL,YAAA,IAAyB;UAC3BzL,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAkB,UAAA,KAAAwK,qCAAA,oBAAwE;UAiCxE1L,EAAA,CAAAE,cAAA,eAA+F;UAA5BF,EAAA,CAAAW,UAAA,mBAAAgL,qDAAA;YAAA,OAASf,GAAA,CAAA7B,eAAA,EAAiB;UAAA,EAAC;UAC5F/I,EAAA,CAAAE,cAAA,eAA8F;UAAnCF,EAAA,CAAAW,UAAA,mBAAAiL,qDAAAjK,MAAA;YAAA,OAASA,MAAA,CAAAkK,eAAA,EAAwB;UAAA,EAAC;UAEzF7L,EADF,CAAAE,cAAA,eAAgC,UAC1B;UAAAF,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnBJ,EAAA,CAAAE,cAAA,kBAAsD;UAA5BF,EAAA,CAAAW,UAAA,mBAAAmL,wDAAA;YAAA,OAASlB,GAAA,CAAA7B,eAAA,EAAiB;UAAA,EAAC;UACnD/I,EAAA,CAAAC,SAAA,aAA4B;UAEhCD,EADE,CAAAI,YAAA,EAAS,EACL;UAGJJ,EADF,CAAAE,cAAA,eAA4B,kBAC2B;UAAvBF,EAAA,CAAAW,UAAA,mBAAAoL,wDAAA;YAAA,OAASnB,GAAA,CAAA5B,UAAA,EAAY;UAAA,EAAC;UAClDhJ,EAAA,CAAAE,cAAA,eAAyB;UACvBF,EAAA,CAAAC,SAAA,eAAwD;UAC1DD,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAE,cAAA,eAA4B,UACtB;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAG,MAAA,4BAAoB;UAE3BH,EAF2B,CAAAI,YAAA,EAAI,EACvB,EACC;UAETJ,EAAA,CAAAE,cAAA,kBAAsD;UAAxBF,EAAA,CAAAW,UAAA,mBAAAqL,wDAAA;YAAA,OAASpB,GAAA,CAAA3B,WAAA,EAAa;UAAA,EAAC;UACnDjJ,EAAA,CAAAE,cAAA,eAAyB;UACvBF,EAAA,CAAAC,SAAA,eAA0D;UAC5DD,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAE,cAAA,eAA4B,UACtB;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAG,MAAA,sBAAc;UAErBH,EAFqB,CAAAI,YAAA,EAAI,EACjB,EACC;UAETJ,EAAA,CAAAE,cAAA,kBAAqD;UAAvBF,EAAA,CAAAW,UAAA,mBAAAsL,wDAAA;YAAA,OAASrB,GAAA,CAAA1B,UAAA,EAAY;UAAA,EAAC;UAClDlJ,EAAA,CAAAE,cAAA,eAAyB;UACvBF,EAAA,CAAAC,SAAA,eAAwD;UAC1DD,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAE,cAAA,eAA4B,UACtB;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAG,MAAA,8BAAsB;UAKnCH,EALmC,CAAAI,YAAA,EAAI,EACzB,EACC,EACL,EACF,EACF;UAGNJ,EAAA,CAAAkB,UAAA,KAAAgL,wCAAA,sBAAmE;UAarElM,EAAA,CAAAI,YAAA,EAAM;;;UA1RDJ,EAFA,CAAAoC,WAAA,cAAAwI,GAAA,CAAAzI,UAAA,CAA8B,gBAAAyI,GAAA,CAAAvI,YAAA,CACI,kBAAAuI,GAAA,CAAA7F,cAAA,CACI;UAGhC/E,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA0I,GAAA,CAAAnG,UAAA,CAAgB;UAuDAzE,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAAzI,UAAA,CAA2B;UAIrBnC,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAkC,UAAA,SAAA0I,GAAA,CAAArI,WAAA,CAAiB;UAYnBvC,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAkC,UAAA,UAAA0I,GAAA,CAAArI,WAAA,CAAkB;UAgBiBvC,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,MAAoC;UAK1B/D,EAAA,CAAAK,SAAA,GAA8C;UAA9CL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,gBAA8C;UAK5C/D,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,kBAAgD;UAK7C/D,EAAA,CAAAK,SAAA,GAAmD;UAAnDL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,qBAAmD;UAKtD/D,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,kBAAgD;UAKpD/D,EAAA,CAAAK,SAAA,GAA4C;UAA5CL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,cAA4C;UAK9C/D,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA7G,cAAA,YAA0C;UAMpG/D,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAkC,UAAA,SAAA0I,GAAA,CAAArI,WAAA,CAAiB;UAkEvBvC,EAAA,CAAAK,SAAA,IAAsC;UAAtCL,EAAA,CAAAkC,UAAA,SAAA0I,GAAA,CAAAjG,aAAA,KAAAiG,GAAA,CAAA7F,cAAA,CAAsC;UAiCX/E,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA5G,gBAAA,CAAiC;UACvChE,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAoC,WAAA,WAAAwI,GAAA,CAAA5G,gBAAA,CAAiC;UA2CnDhE,EAAA,CAAAK,SAAA,IAAkC;UAAlCL,EAAA,CAAAkC,UAAA,SAAA0I,GAAA,CAAAlG,UAAA,KAAAkG,GAAA,CAAAjG,aAAA,CAAkC;;;qBDlQjC9E,YAAY,EAAAsM,EAAA,CAAAC,IAAA,EAAEtM,YAAY,EAAAuM,EAAA,CAAAC,UAAA,EAAEvM,WAAW,EAAAwM,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}