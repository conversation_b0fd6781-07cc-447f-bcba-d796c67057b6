{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nfunction HomeComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h1\", 12);\n    i0.ɵɵtext(3, \"DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14);\n    i0.ɵɵelement(6, \"ion-icon\", 15);\n    i0.ɵɵelementStart(7, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTabMenu());\n    });\n    i0.ɵɵelement(8, \"ion-icon\", 17);\n    i0.ɵɵtemplate(9, HomeComponent_div_1_div_9_Template, 2, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSidebar());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasNotifications);\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵelement(5, \"app-feed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 25);\n    i0.ɵɵelement(7, \"app-sidebar\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"app-view-add-stories\")(4, \"app-feed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"app-sidebar\", 26);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"h3\");\n    i0.ɵɵtext(3, \"Discover\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 34);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 35)(6, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"trending\"));\n    });\n    i0.ɵɵelementStart(7, \"div\", 37);\n    i0.ɵɵelement(8, \"ion-icon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 39);\n    i0.ɵɵtext(10, \"Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 40);\n    i0.ɵɵtext(12, \"Hot products right now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"brands\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 41);\n    i0.ɵɵelement(15, \"ion-icon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 39);\n    i0.ɵɵtext(17, \"Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 40);\n    i0.ɵɵtext(19, \"Top fashion brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"arrivals\"));\n    });\n    i0.ɵɵelementStart(21, \"div\", 43);\n    i0.ɵɵelement(22, \"ion-icon\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 39);\n    i0.ɵɵtext(24, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 40);\n    i0.ɵɵtext(26, \"Latest arrivals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"suggested\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 45);\n    i0.ɵɵelement(29, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 39);\n    i0.ɵɵtext(31, \"For You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 40);\n    i0.ɵɵtext(33, \"Personalized picks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"influencers\"));\n    });\n    i0.ɵɵelementStart(35, \"div\", 47);\n    i0.ɵɵelement(36, \"ion-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 39);\n    i0.ɵɵtext(38, \"Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 40);\n    i0.ɵɵtext(40, \"Top fashion creators\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_6_Template_div_click_41_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"categories\"));\n    });\n    i0.ɵɵelementStart(42, \"div\", 49);\n    i0.ɵɵelement(43, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 39);\n    i0.ɵɵtext(45, \"Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 40);\n    i0.ɵɵtext(47, \"Browse by category\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵelement(4, \"img\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"h3\");\n    i0.ɵɵtext(7, \"Your Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"@username\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"ion-icon\", 34);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_7_Template_ion_icon_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 57);\n    i0.ɵɵelement(12, \"app-sidebar\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-trending-products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-featured-brands\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-new-arrivals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-suggested-for-you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-top-fashion-influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_8_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵelement(2, \"ion-icon\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r8.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r8.name);\n  }\n}\nfunction HomeComponent_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 62);\n    i0.ɵɵtemplate(2, HomeComponent_div_8_div_11_div_2_Template, 5, 2, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 34);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebarContent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 60);\n    i0.ɵɵtemplate(6, HomeComponent_div_8_div_6_Template, 2, 0, \"div\", 61)(7, HomeComponent_div_8_div_7_Template, 2, 0, \"div\", 61)(8, HomeComponent_div_8_div_8_Template, 2, 0, \"div\", 61)(9, HomeComponent_div_8_div_9_Template, 2, 0, \"div\", 61)(10, HomeComponent_div_8_div_10_Template, 2, 0, \"div\", 61)(11, HomeComponent_div_8_div_11_Template, 3, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarContentOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentSidebarTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"trending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"brands\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"arrivals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"suggested\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"influencers\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"categories\");\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵelement(2, \"ion-icon\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵelement(4, \"ion-icon\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 70);\n    i0.ɵɵelement(6, \"ion-icon\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 70);\n    i0.ɵɵelement(8, \"ion-icon\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 70)(10, \"div\", 74);\n    i0.ɵɵelement(11, \"img\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.isMobile = false;\n    this.isSidebarOpen = false;\n    this.isTabMenuOpen = false;\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.currentSidebarTitle = '';\n    this.hasNotifications = true; // Example notification state\n    this.window = window; // For template access\n    // TikTok-style interaction states\n    this.isLiked = false;\n    // Instagram Stories Data - Enhanced for responsive design and mobile app\n    this.instagramStories = [{\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }];\n    // Categories Data\n    this.categories = [{\n      name: 'Women',\n      icon: 'woman'\n    }, {\n      name: 'Men',\n      icon: 'man'\n    }, {\n      name: 'Kids',\n      icon: 'happy'\n    }, {\n      name: 'Shoes',\n      icon: 'footsteps'\n    }, {\n      name: 'Bags',\n      icon: 'bag'\n    }, {\n      name: 'Accessories',\n      icon: 'watch'\n    }, {\n      name: 'Beauty',\n      icon: 'flower'\n    }, {\n      name: 'Sports',\n      icon: 'fitness'\n    }];\n    this.preventScroll = e => {\n      if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n        e.preventDefault();\n      }\n    };\n  }\n  ngOnInit() {\n    console.log('🏠 HomeComponent: ngOnInit called');\n    this.checkScreenSize();\n    console.log('🏠 HomeComponent initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length,\n      windowWidth: window.innerWidth,\n      windowHeight: window.innerHeight\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, {\n      passive: false\n    });\n  }\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n  onResize(event) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n  checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n  toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n  openSidebarTab(tabType) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n    // Set title based on tab type\n    const titles = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n  viewStory(story) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n  trackByStoryId(index, story) {\n    return story.id || index;\n  }\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event, story) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n  onStoryTouchEnd(event, story) {\n    story.touching = false;\n  }\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function HomeComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 15,\n      consts: [[1, \"home-container\"], [\"class\", \"mobile-header instagram-style\", 4, \"ngIf\"], [\"class\", \"web-layout\", 4, \"ngIf\"], [\"class\", \"mobile-layout\", 4, \"ngIf\"], [\"class\", \"tab-menu-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"sidebar-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"instagram-tab-menu\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"mobile-sidebar\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"sidebar-content-modal\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"instagram-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-header\", \"instagram-style\"], [1, \"header-left\"], [1, \"app-logo\"], [\"name\", \"chevron-down\", 1, \"logo-dropdown\"], [1, \"header-right\"], [\"name\", \"heart-outline\", 1, \"header-icon\"], [1, \"menu-icon-container\", 3, \"click\"], [\"name\", \"grid-outline\", 1, \"header-icon\", \"menu-icon\"], [\"class\", \"notification-dot\", 4, \"ngIf\"], [\"name\", \"menu-outline\", 1, \"header-icon\", \"menu-icon\"], [1, \"notification-dot\"], [1, \"web-layout\"], [1, \"stories-section-container\"], [1, \"two-column-layout\"], [1, \"post-section\"], [1, \"sidebar-section\"], [1, \"desktop-sidebar\"], [1, \"mobile-layout\"], [1, \"content-grid\"], [1, \"main-content\"], [1, \"tab-menu-overlay\", 3, \"click\"], [1, \"sidebar-overlay\", 3, \"click\"], [1, \"instagram-tab-menu\"], [1, \"tab-menu-header\"], [\"name\", \"close-outline\", 1, \"close-icon\", 3, \"click\"], [1, \"tab-menu-grid\"], [1, \"tab-item\", 3, \"click\"], [1, \"tab-icon\", \"trending\"], [\"name\", \"trending-up\"], [1, \"tab-label\"], [1, \"tab-tooltip\"], [1, \"tab-icon\", \"brands\"], [\"name\", \"diamond\"], [1, \"tab-icon\", \"arrivals\"], [\"name\", \"sparkles\"], [1, \"tab-icon\", \"suggested\"], [\"name\", \"heart\"], [1, \"tab-icon\", \"influencers\"], [\"name\", \"people\"], [1, \"tab-icon\", \"categories\"], [\"name\", \"grid\"], [1, \"mobile-sidebar\"], [1, \"sidebar-header\"], [1, \"user-profile\"], [1, \"profile-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Profile\"], [1, \"profile-info\"], [1, \"sidebar-content\"], [1, \"sidebar-content-modal\"], [1, \"modal-header\"], [1, \"modal-content\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\"], [1, \"category-icon\"], [3, \"name\"], [1, \"instagram-bottom-nav\"], [1, \"nav-item\", \"active\"], [\"name\", \"home\"], [1, \"nav-item\"], [\"name\", \"search\"], [\"name\", \"add-circle-outline\"], [\"name\", \"play-circle-outline\"], [1, \"profile-avatar-nav\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 12, 1, \"div\", 1)(2, HomeComponent_div_2_Template, 8, 0, \"div\", 2)(3, HomeComponent_div_3_Template, 6, 0, \"div\", 3)(4, HomeComponent_div_4_Template, 1, 2, \"div\", 4)(5, HomeComponent_div_5_Template, 1, 2, \"div\", 5)(6, HomeComponent_div_6_Template, 48, 2, \"div\", 6)(7, HomeComponent_div_7_Template, 13, 2, \"div\", 7)(8, HomeComponent_div_8_Template, 12, 9, \"div\", 8)(9, HomeComponent_div_9_Template, 12, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mobile-instagram\", ctx.isMobile)(\"sidebar-open\", ctx.isSidebarOpen)(\"mobile\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IonicModule, i2.IonIcon, ViewAddStoriesComponent, FeedComponent, SidebarComponent, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n  position: relative;\\n  background: #ffffff;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  color: #262626 !important;\\n  padding: 0 !important;\\n  min-height: 100vh !important;\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 60px;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 1001; \\n\\n  padding: 0 16px;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.mobile-header.instagram-style[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 400;\\n  color: #262626;\\n  margin: 0;\\n  font-family: \\\"Billabong\\\", cursive, -apple-system, BlinkMacSystemFont, sans-serif;\\n  letter-spacing: 0.5px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #262626;\\n  margin-top: 2px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  cursor: pointer;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 8px;\\n  height: 8px;\\n  background: #ff3040;\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n\\n.instagram-stories-section[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 60px;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 999;\\n  padding: 12px 0;\\n  height: 100px; \\n\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  \\n\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  overflow-y: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: 100%;\\n  align-items: center;\\n  min-width: max-content;\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 998; \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  overscroll-behavior-x: contain;\\n  scroll-snap-type: x proximity;\\n  \\n\\n  will-change: scroll-position;\\n  transform: translateZ(0); \\n\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 10px;\\n    scroll-snap-type: x mandatory;\\n    \\n\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n    overscroll-behavior-x: contain;\\n    \\n\\n    contain: layout style paint;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 8px;\\n    \\n\\n    scroll-padding-left: 8px;\\n    scroll-padding-right: 8px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 6px;\\n    gap: 6px;\\n    \\n\\n    scroll-padding-left: 6px;\\n    scroll-padding-right: 6px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 6px;\\n  min-width: 70px;\\n  max-width: 70px;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n  scroll-snap-align: start;\\n  scroll-snap-stop: normal;\\n  position: relative;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n    max-width: 65px;\\n    gap: 5px;\\n    \\n\\n    padding: 4px;\\n    margin: -4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n    max-width: 60px;\\n    gap: 4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 55px;\\n    max-width: 55px;\\n    gap: 3px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 50px;\\n    max-width: 50px;\\n    gap: 2px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border: 2px solid #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n  position: relative;\\n  flex-shrink: 0;\\n  transition: all 0.2s ease;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%] {\\n  border: 2px solid transparent;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  padding: 2px;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed) {\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%] {\\n  border: 2px solid #c7c7c7;\\n  background: #c7c7c7;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_storyTouchFeedback 0.2s ease;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n  opacity: 0.8;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -2;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_storyRingGradient 3s infinite;\\n  filter: blur(2px);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  display: block;\\n  transition: transform 0.2s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 55px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  font-weight: 400;\\n  margin-top: 4px;\\n  \\n\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  text-rendering: optimizeLegibility;\\n  \\n\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 65px;\\n    font-weight: 500; \\n\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 60px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    max-width: 55px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 7px;\\n    max-width: 50px;\\n    line-height: 1;\\n    font-weight: 600; \\n\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0.7);\\n  }\\n  70% {\\n    transform: scale(1.05) translateZ(0);\\n    box-shadow: 0 0 0 10px rgba(240, 148, 51, 0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyRingGradient {\\n  0% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  25% {\\n    background: linear-gradient(90deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  50% {\\n    background: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  75% {\\n    background: linear-gradient(180deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  100% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyTouchFeedback {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n  }\\n  50% {\\n    transform: scale(0.95) translateZ(0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n  }\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-top: 1px solid #dbdbdb;\\n  justify-content: space-around;\\n  align-items: center;\\n  padding: 8px 0;\\n  z-index: 1000;\\n  height: 60px;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);\\n  padding-bottom: max(8px, env(safe-area-inset-bottom));\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  min-width: 44px;\\n  min-height: 44px;\\n  position: relative;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #262626;\\n  transform: scale(1.1);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  transition: all 0.2s ease;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 1px solid #8e8e8e;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%] {\\n  border: 2px solid #262626;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 2px;\\n  right: 2px;\\n  background: #ff3040;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.tab-menu-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1500;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.tab-menu-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.instagram-tab-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #000000;\\n  border-top-left-radius: 20px;\\n  border-top-right-radius: 20px;\\n  z-index: 1600;\\n  transform: translateY(100%);\\n  transition: transform 0.3s ease;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n.instagram-tab-menu.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px 16px;\\n  border-bottom: 1px solid #262626;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 24px;\\n  padding: 24px;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  padding: 16px;\\n  border-radius: 16px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  transform: scale(1.05);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4, #44a08d);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea, #fed6e3);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  text-align: center;\\n  line-height: 1.3;\\n}\\n\\n.sidebar-content-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: #000000;\\n  z-index: 1700;\\n  transform: translateX(100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n}\\n.sidebar-content-modal.active[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background: #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #262626;\\n  z-index: 10;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  background: #000000;\\n  color: white;\\n  min-height: calc(100vh - 80px);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     * {\\n  background-color: transparent !important;\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item {\\n  background: #1a1a1a !important;\\n  border: 1px solid #262626 !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark {\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white {\\n  background: #1a1a1a !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n  padding: 24px;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 20px;\\n  background: #1a1a1a;\\n  border-radius: 16px;\\n  border: 1px solid #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background: #262626;\\n  transform: scale(1.02);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n\\n.web-layout[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 80px 20px 20px 20px;\\n  min-height: calc(100vh - 100px);\\n}\\n@media (min-width: 769px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .web-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-section-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 20px;\\n}\\n\\n.two-column-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n\\n.post-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n@media (min-width: 1025px) {\\n  .sidebar-section[_ngcontent-%COMP%] {\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n  }\\n}\\n\\n@media (min-width: 769px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .mobile-layout[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  background: #ffffff;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.desktop-sidebar[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.sidebar-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.65);\\n  z-index: 200;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.sidebar-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.mobile-sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -100%;\\n  width: 85%;\\n  max-width: 400px;\\n  height: 100%;\\n  background: #ffffff;\\n  z-index: 300;\\n  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.mobile-sidebar.active[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 16px;\\n  border-bottom: 1px solid #dbdbdb;\\n  background: #fafafa;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  padding: 8px;\\n  margin: -8px;\\n  transition: color 0.2s ease;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 100%;\\n    padding: 0 16px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    max-width: 768px;\\n    margin: 0 auto;\\n    padding: 0 24px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 1025px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block !important;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    padding-top: 80px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 80px 0 0 0;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  min-height: 100vh !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 100px !important;\\n  padding: 8px 0 !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  \\n\\n  transform: translateZ(0) !important;\\n  will-change: scroll-position !important;\\n  contain: layout style paint !important;\\n  \\n\\n  backdrop-filter: blur(10px) !important;\\n  -webkit-backdrop-filter: blur(10px) !important;\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95) !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n  \\n\\n  grid-template-columns: 1fr !important;\\n  padding: 160px 0 60px 0 !important; \\n\\n  background: #ffffff !important;\\n  gap: 0 !important;\\n  margin: 0 !important;\\n  max-width: 100% !important;\\n  min-height: calc(100vh - 220px) !important;\\n  overflow-x: hidden !important;\\n  position: relative !important;\\n  z-index: 1 !important; \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n@media (min-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 16px 60px 16px !important; \\n\\n    max-width: 768px !important;\\n    margin: 0 auto !important; \\n\\n    gap: 16px !important;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 300px !important; \\n\\n    padding: 160px 24px 60px 24px !important;\\n    max-width: 1200px !important;\\n    gap: 32px !important;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px !important; \\n\\n    padding: 160px 32px 60px 32px !important;\\n    max-width: 1400px !important;\\n    gap: 40px !important;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 48px 60px 48px !important;\\n    max-width: 1600px !important;\\n    gap: 48px !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-top: 1px solid #dbdbdb !important;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n    padding: 0 !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0;\\n    margin: 0;\\n    background: #fafafa;\\n  }\\n  .content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    background: white;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin: 0;\\n    padding: 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 60px !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 12px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) and (min-width: 1025px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    min-height: 100vh;\\n    padding: 0 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 160px 0 60px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    color: #262626 !important;\\n    gap: 0;\\n    padding: 0 8px 40px 8px;\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    box-sizing: border-box;\\n    overflow: visible !important;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 90%;\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .sidebar-overlay[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.8);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 95%;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    overflow-x: hidden !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 360px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 80px 0 !important;\\n    min-height: calc(100vh - 240px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 4px 50px 4px !important;\\n    overflow: visible !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%] {\\n    display: block !important;\\n    width: 100% !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 425px) and (min-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 100px 0 !important;\\n    min-height: calc(100vh - 260px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 6px 60px 6px !important;\\n    overflow: visible !important;\\n    position: relative;\\n  }\\n  app-feed[_ngcontent-%COMP%] {\\n    padding-bottom: 40px !important;\\n    overflow: visible !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 120px 0 !important;\\n    min-height: calc(100vh - 280px) !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0 2px 70px 2px !important;\\n    overflow: visible !important;\\n  }\\n  app-feed[_ngcontent-%COMP%] {\\n    padding-bottom: 50px !important;\\n    overflow: visible !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: none !important;\\n    visibility: hidden !important;\\n    opacity: 0 !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    padding: 20px 20px 0 20px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n    padding: 0;\\n    margin: 0 auto;\\n    padding-top: 0;\\n    \\n\\n    grid-template-columns: 1fr 300px;\\n    gap: 32px;\\n    max-width: 1000px;\\n    \\n\\n    \\n\\n    \\n\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 350px;\\n    gap: 36px;\\n    max-width: 1200px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1200px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px;\\n    gap: 40px;\\n    max-width: 1400px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1440px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 450px;\\n    gap: 48px;\\n    max-width: 1600px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "ViewAddStoriesComponent", "FeedComponent", "SidebarComponent", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_div_1_Template_div_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleTabMenu", "ɵɵtemplate", "HomeComponent_div_1_div_9_Template", "HomeComponent_div_1_Template_div_click_10_listener", "toggleSidebar", "ɵɵadvance", "ɵɵproperty", "hasNotifications", "HomeComponent_div_4_Template_div_click_0_listener", "_r3", "closeTabMenu", "ɵɵclassProp", "isTabMenuOpen", "HomeComponent_div_5_Template_div_click_0_listener", "_r4", "closeSidebar", "isSidebarOpen", "HomeComponent_div_6_Template_ion_icon_click_4_listener", "_r5", "HomeComponent_div_6_Template_div_click_6_listener", "openSidebarTab", "HomeComponent_div_6_Template_div_click_13_listener", "HomeComponent_div_6_Template_div_click_20_listener", "HomeComponent_div_6_Template_div_click_27_listener", "HomeComponent_div_6_Template_div_click_34_listener", "HomeComponent_div_6_Template_div_click_41_listener", "HomeComponent_div_7_Template_ion_icon_click_10_listener", "_r6", "category_r8", "icon", "ɵɵtextInterpolate", "name", "HomeComponent_div_8_div_11_div_2_Template", "categories", "HomeComponent_div_8_Template_ion_icon_click_4_listener", "_r7", "closeSidebarContent", "HomeComponent_div_8_div_6_Template", "HomeComponent_div_8_div_7_Template", "HomeComponent_div_8_div_8_Template", "HomeComponent_div_8_div_9_Template", "HomeComponent_div_8_div_10_Template", "HomeComponent_div_8_div_11_Template", "isSidebarContentOpen", "currentSidebarTitle", "currentSidebarTab", "HomeComponent", "constructor", "isMobile", "window", "isLiked", "instagramStories", "id", "username", "avatar", "hasStory", "viewed", "touching", "preventScroll", "e", "preventDefault", "ngOnInit", "console", "log", "checkScreenSize", "length", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "document", "addEventListener", "passive", "ngOnDestroy", "removeEventListener", "onResize", "event", "width", "userAgent", "navigator", "isMobileUserAgent", "test", "height", "toggleBodyScroll", "body", "style", "overflow", "tabType", "titles", "toggleLike", "openComments", "shareContent", "share", "title", "text", "url", "location", "href", "openMusic", "createStory", "viewStory", "story", "trackByStoryId", "index", "onStoryTouchStart", "vibrate", "onStoryTouchEnd", "onLikeClick", "onCommentClick", "onShareClick", "onBookmarkClick", "navigateToTrending", "navigateToNewArrivals", "navigateToOffers", "navigateToCategories", "navigateToWishlist", "navigateToCart", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_resize_HostBindingHandler", "$event", "ɵɵresolveWindow", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_div_3_Template", "HomeComponent_div_4_Template", "HomeComponent_div_5_Template", "HomeComponent_div_6_Template", "HomeComponent_div_7_Template", "HomeComponent_div_8_Template", "HomeComponent_div_9_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IonicModule,\n    ViewAddStoriesComponent,\n    FeedComponent,\n    SidebarComponent,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent\n  ],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  isMobile = false;\n  isSidebarOpen = false;\n  isTabMenuOpen = false;\n  isSidebarContentOpen = false;\n  currentSidebarTab = '';\n  currentSidebarTitle = '';\n  hasNotifications = true; // Example notification state\n  window = window; // For template access\n\n  // TikTok-style interaction states\n  isLiked = false;\n\n  // Instagram Stories Data - Enhanced for responsive design and mobile app\n  instagramStories = [\n    {\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }\n  ];\n\n  // Categories Data\n  categories = [\n    { name: 'Women', icon: 'woman' },\n    { name: 'Men', icon: 'man' },\n    { name: 'Kids', icon: 'happy' },\n    { name: 'Shoes', icon: 'footsteps' },\n    { name: 'Bags', icon: 'bag' },\n    { name: 'Accessories', icon: 'watch' },\n    { name: 'Beauty', icon: 'flower' },\n    { name: 'Sports', icon: 'fitness' }\n  ];\n\n  constructor() {}\n\n  ngOnInit() {\n    console.log('🏠 HomeComponent: ngOnInit called');\n    this.checkScreenSize();\n    console.log('🏠 HomeComponent initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length,\n      windowWidth: window.innerWidth,\n      windowHeight: window.innerHeight\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, { passive: false });\n  }\n\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n\n  private checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  private toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n\n  private preventScroll = (e: TouchEvent) => {\n    if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n      e.preventDefault();\n    }\n  }\n\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  openSidebarTab(tabType: string) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n\n    // Set title based on tab type\n    const titles: { [key: string]: string } = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n\n  viewStory(story: any) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n\n  trackByStoryId(index: number, story: any): any {\n    return story.id || index;\n  }\n\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event: TouchEvent, story: any) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n\n  onStoryTouchEnd(event: TouchEvent, story: any) {\n    story.touching = false;\n  }\n\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n}\n", "<div class=\"home-container\" [class.mobile-instagram]=\"isMobile\" [class.sidebar-open]=\"isSidebarOpen\" [class.mobile]=\"isMobile\">\n  <!-- Mobile Header with Instagram-like styling -->\n  <div class=\"mobile-header instagram-style\" *ngIf=\"isMobile\">\n    <div class=\"header-left\">\n      <h1 class=\"app-logo\">DFashion</h1>\n      <ion-icon name=\"chevron-down\" class=\"logo-dropdown\"></ion-icon>\n    </div>\n    <div class=\"header-right\">\n      <ion-icon name=\"heart-outline\" class=\"header-icon\"></ion-icon>\n      <div class=\"menu-icon-container\" (click)=\"toggleTabMenu()\">\n        <ion-icon name=\"grid-outline\" class=\"header-icon menu-icon\"></ion-icon>\n        <div class=\"notification-dot\" *ngIf=\"hasNotifications\">3</div>\n      </div>\n      <div class=\"menu-icon-container\" (click)=\"toggleSidebar()\">\n        <ion-icon name=\"menu-outline\" class=\"header-icon menu-icon\"></ion-icon>\n      </div>\n    </div>\n  </div>\n\n  <!-- Removed separate mobile stories section - using unified responsive stories component -->\n\n\n  <!-- Web Layout: Stories Section + Two Column Layout -->\n  <div class=\"web-layout\" *ngIf=\"!isMobile\">\n    <!-- Stories Section (Full Width) -->\n    <div class=\"stories-section-container\">\n      <app-view-add-stories></app-view-add-stories>\n    </div>\n\n    <!-- Two Column Layout -->\n    <div class=\"two-column-layout\">\n      <!-- Post Section (Left Column) -->\n      <div class=\"post-section\">\n        <app-feed></app-feed>\n      </div>\n\n      <!-- Sidebar Section (Right Column) -->\n      <div class=\"sidebar-section\">\n        <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Layout: Original Layout -->\n  <div class=\"mobile-layout\" *ngIf=\"isMobile\">\n    <div class=\"content-grid\">\n      <!-- Main Feed -->\n      <div class=\"main-content\">\n        <!-- Responsive Stories Component (Works on all screen sizes) -->\n        <app-view-add-stories></app-view-add-stories>\n        <!-- Instagram-style Feed with Posts and Reels -->\n        <app-feed></app-feed>\n      </div>\n\n      <!-- Desktop Sidebar -->\n      <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Mobile Tab Menu Overlay -->\n  <div class=\"tab-menu-overlay\"\n       [class.active]=\"isTabMenuOpen\"\n       (click)=\"closeTabMenu()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Mobile Sidebar Overlay -->\n  <div class=\"sidebar-overlay\"\n       [class.active]=\"isSidebarOpen\"\n       (click)=\"closeSidebar()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Instagram-Style Tab Menu -->\n  <div class=\"instagram-tab-menu\"\n       [class.active]=\"isTabMenuOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"tab-menu-header\">\n      <h3>Discover</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeTabMenu()\"></ion-icon>\n    </div>\n\n    <div class=\"tab-menu-grid\">\n      <!-- Trending Products Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('trending')\">\n        <div class=\"tab-icon trending\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Trending</span>\n        <div class=\"tab-tooltip\">Hot products right now</div>\n      </div>\n\n      <!-- Featured Brands Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('brands')\">\n        <div class=\"tab-icon brands\">\n          <ion-icon name=\"diamond\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Brands</span>\n        <div class=\"tab-tooltip\">Top fashion brands</div>\n      </div>\n\n      <!-- New Arrivals Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('arrivals')\">\n        <div class=\"tab-icon arrivals\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">New</span>\n        <div class=\"tab-tooltip\">Latest arrivals</div>\n      </div>\n\n      <!-- Suggested for You Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('suggested')\">\n        <div class=\"tab-icon suggested\">\n          <ion-icon name=\"heart\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">For You</span>\n        <div class=\"tab-tooltip\">Personalized picks</div>\n      </div>\n\n      <!-- Fashion Influencers Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('influencers')\">\n        <div class=\"tab-icon influencers\">\n          <ion-icon name=\"people\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Influencers</span>\n        <div class=\"tab-tooltip\">Top fashion creators</div>\n      </div>\n\n      <!-- Categories Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('categories')\">\n        <div class=\"tab-icon categories\">\n          <ion-icon name=\"grid\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Categories</span>\n        <div class=\"tab-tooltip\">Browse by category</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <div class=\"mobile-sidebar\"\n       [class.active]=\"isSidebarOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"sidebar-header\">\n      <div class=\"user-profile\">\n        <div class=\"profile-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>Your Profile</h3>\n          <p>&#64;username</p>\n        </div>\n      </div>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebar()\"></ion-icon>\n    </div>\n\n    <div class=\"sidebar-content\">\n      <app-sidebar></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Sidebar Content Modal -->\n  <div class=\"sidebar-content-modal\"\n       [class.active]=\"isSidebarContentOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"modal-header\">\n      <h3>{{currentSidebarTitle}}</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebarContent()\"></ion-icon>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Trending Products Section -->\n      <div *ngIf=\"currentSidebarTab === 'trending'\" class=\"sidebar-section\">\n        <app-trending-products></app-trending-products>\n      </div>\n\n      <!-- Featured Brands Section -->\n      <div *ngIf=\"currentSidebarTab === 'brands'\" class=\"sidebar-section\">\n        <app-featured-brands></app-featured-brands>\n      </div>\n\n      <!-- New Arrivals Section -->\n      <div *ngIf=\"currentSidebarTab === 'arrivals'\" class=\"sidebar-section\">\n        <app-new-arrivals></app-new-arrivals>\n      </div>\n\n      <!-- Suggested for you -->\n      <div *ngIf=\"currentSidebarTab === 'suggested'\" class=\"sidebar-section\">\n        <app-suggested-for-you></app-suggested-for-you>\n      </div>\n\n      <!-- Top Fashion Influencers -->\n      <div *ngIf=\"currentSidebarTab === 'influencers'\" class=\"sidebar-section\">\n        <app-top-fashion-influencers></app-top-fashion-influencers>\n      </div>\n\n      <!-- Categories (placeholder) -->\n      <div *ngIf=\"currentSidebarTab === 'categories'\" class=\"sidebar-section\">\n        <div class=\"categories-grid\">\n          <div class=\"category-item\" *ngFor=\"let category of categories\">\n            <div class=\"category-icon\">\n              <ion-icon [name]=\"category.icon\"></ion-icon>\n            </div>\n            <span>{{category.name}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Bottom Navigation (Mobile Only) -->\n  <div class=\"instagram-bottom-nav\" *ngIf=\"isMobile\">\n    <div class=\"nav-item active\">\n      <ion-icon name=\"home\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"search\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"add-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"play-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <div class=\"profile-avatar-nav\">\n        <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,uBAAuB,QAAQ,4DAA4D;AACpG,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,8BAA8B,QAAQ,4EAA4E;;;;;;ICAnHC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPhEH,EAFJ,CAAAC,cAAA,cAA4D,cACjC,aACF;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAI,SAAA,mBAA+D;IACjEJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,mBAA8D;IAC9DJ,EAAA,CAAAC,cAAA,cAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAC,kDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACxDZ,EAAA,CAAAI,SAAA,mBAAuE;IACvEJ,EAAA,CAAAa,UAAA,IAAAC,kCAAA,kBAAuD;IACzDd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAU,mDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,aAAA,EAAe;IAAA,EAAC;IACxDhB,EAAA,CAAAI,SAAA,oBAAuE;IAG7EJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAN+BH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAU,gBAAA,CAAsB;;;;;IAczDnB,EAFF,CAAAC,cAAA,cAA0C,cAED;IACrCD,EAAA,CAAAI,SAAA,2BAA6C;IAC/CJ,EAAA,CAAAG,YAAA,EAAM;IAKJH,EAFF,CAAAC,cAAA,cAA+B,cAEH;IACxBD,EAAA,CAAAI,SAAA,eAAqB;IACvBJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAI,SAAA,sBAAmD;IAGzDJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAMFH,EAHJ,CAAAC,cAAA,cAA4C,cAChB,cAEE;IAIxBD,EAFA,CAAAI,SAAA,2BAA6C,eAExB;IACvBJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,SAAA,sBAAmD;IAEvDJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAe,kDAAA;MAAApB,EAAA,CAAAO,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAE7BtB,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAe,aAAA,CAA8B;;;;;;IAMnCxB,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAoB,kDAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,YAAA,EAAc;IAAA,EAAC;IAE7B3B,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAmB,aAAA,CAA8B;;;;;;IAU/B5B,EAJJ,CAAAC,cAAA,cAEsB,cACS,SACvB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,mBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAwB,uDAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAC5EtB,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAIJH,EAFF,CAAAC,cAAA,cAA2B,cAEkC;IAArCD,EAAA,CAAAK,UAAA,mBAAA0B,kDAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDhC,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,mBAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EACjD;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAAnCD,EAAA,CAAAK,UAAA,mBAAA4B,mDAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACtDhC,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,oBAAoC;IACtCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IAArCD,EAAA,CAAAK,UAAA,mBAAA6B,mDAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDhC,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,oBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IAGNH,EAAA,CAAAC,cAAA,eAA4D;IAAtCD,EAAA,CAAAK,UAAA,mBAAA8B,mDAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,WAAW,CAAC;IAAA,EAAC;IACzDhC,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAI,SAAA,oBAAkC;IACpCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA8D;IAAxCD,EAAA,CAAAK,UAAA,mBAAA+B,mDAAA;MAAApC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,aAAa,CAAC;IAAA,EAAC;IAC3DhC,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAI,SAAA,oBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;IAGNH,EAAA,CAAAC,cAAA,eAA6D;IAAvCD,EAAA,CAAAK,UAAA,mBAAAgC,mDAAA;MAAArC,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,cAAA,CAAe,YAAY,CAAC;IAAA,EAAC;IAC1DhC,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAI,SAAA,oBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAGjDF,EAHiD,CAAAG,YAAA,EAAM,EAC7C,EACF,EACF;;;;IA9DDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAe,aAAA,CAA8B;;;;;;IAsE7BxB,EALN,CAAAC,cAAA,cAEsB,cACQ,cACA,cACI;IAC1BD,EAAA,CAAAI,SAAA,cAA0D;IAC5DJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gBAAa;IAEpBF,EAFoB,CAAAG,YAAA,EAAI,EAChB,EACF;IACNH,EAAA,CAAAC,cAAA,oBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAiC,wDAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,YAAA,EAAc;IAAA,EAAC;IAC5E3B,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAENH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,mBAA2B;IAE/BJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlBDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAAmB,aAAA,CAA8B;;;;;IA+B/B5B,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,SAAA,0BAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,kCAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAMAH,EADF,CAAAC,cAAA,cAA+D,cAClC;IACzBD,EAAA,CAAAI,SAAA,mBAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;;;;IAHQH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAsB,WAAA,CAAAC,IAAA,CAAsB;IAE5BzC,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA0C,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAiB;;;;;IAL3B3C,EADF,CAAAC,cAAA,cAAwE,cACzC;IAC3BD,EAAA,CAAAa,UAAA,IAAA+B,yCAAA,kBAA+D;IAOnE5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAP8CH,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,YAAAT,MAAA,CAAAoC,UAAA,CAAa;;;;;;IAjCjE7C,EAJJ,CAAAC,cAAA,cAEsB,cACM,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,mBAAkF;IAAhCD,EAAA,CAAAK,UAAA,mBAAAyC,uDAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,mBAAA,EAAqB;IAAA,EAAC;IACnFhD,EADoF,CAAAG,YAAA,EAAW,EACzF;IAENH,EAAA,CAAAC,cAAA,cAA2B;IA2BzBD,EAzBA,CAAAa,UAAA,IAAAoC,kCAAA,kBAAsE,IAAAC,kCAAA,kBAKF,IAAAC,kCAAA,kBAKE,IAAAC,kCAAA,kBAKC,KAAAC,mCAAA,kBAKE,KAAAC,mCAAA,kBAKD;IAW5EtD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7CDH,EAAA,CAAAuB,WAAA,WAAAd,MAAA,CAAA8C,oBAAA,CAAqC;IAGlCvD,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA0C,iBAAA,CAAAjC,MAAA,CAAA+C,mBAAA,CAAuB;IAMrBxD,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,gBAAsC;IAKtCzD,EAAA,CAAAiB,SAAA,EAAoC;IAApCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,cAAoC;IAKpCzD,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,gBAAsC;IAKtCzD,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,iBAAuC;IAKvCzD,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,mBAAyC;IAKzCzD,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAgD,iBAAA,kBAAwC;;;;;IAehDzD,EADF,CAAAC,cAAA,cAAmD,cACpB;IAC3BD,EAAA,CAAAI,SAAA,mBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAgD;IAClDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,eACY;IAC9BD,EAAA,CAAAI,SAAA,eAA0D;IAGhEJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;ADtMR,OAAM,MAAOuD,aAAa;EA6FxBC,YAAA;IA5FA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAhC,aAAa,GAAG,KAAK;IACrB,KAAAJ,aAAa,GAAG,KAAK;IACrB,KAAA+B,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,iBAAiB,GAAG,EAAE;IACtB,KAAAD,mBAAmB,GAAG,EAAE;IACxB,KAAArC,gBAAgB,GAAG,IAAI,CAAC,CAAC;IACzB,KAAA0C,MAAM,GAAGA,MAAM,CAAC,CAAC;IAEjB;IACA,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAC,gBAAgB,GAAG,CACjB;MACEC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,mCAAmC;MAC3CC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,CACF;IAED;IACA,KAAAxB,UAAU,GAAG,CACX;MAAEF,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAO,CAAE,EAChC;MAAEE,IAAI,EAAE,KAAK;MAAEF,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAEE,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE;IAAO,CAAE,EAC/B;MAAEE,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAW,CAAE,EACpC;MAAEE,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAEE,IAAI,EAAE,aAAa;MAAEF,IAAI,EAAE;IAAO,CAAE,EACtC;MAAEE,IAAI,EAAE,QAAQ;MAAEF,IAAI,EAAE;IAAQ,CAAE,EAClC;MAAEE,IAAI,EAAE,QAAQ;MAAEF,IAAI,EAAE;IAAS,CAAE,CACpC;IAiEO,KAAA6B,aAAa,GAAIC,CAAa,IAAI;MACxC,IAAI,IAAI,CAAC3C,aAAa,IAAI,IAAI,CAACJ,aAAa,IAAI,IAAI,CAAC+B,oBAAoB,EAAE;QACzEgB,CAAC,CAACC,cAAc,EAAE;;IAEtB,CAAC;EAnEc;EAEfC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACC,eAAe,EAAE;IACtBF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3Cf,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBG,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACc,MAAM;MAC9CC,WAAW,EAAEjB,MAAM,CAACkB,UAAU;MAC9BC,YAAY,EAAEnB,MAAM,CAACoB;KACtB,CAAC;IACF;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACb,aAAa,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAE,CAAC;EAChF;EAEAC,WAAWA,CAAA;IACTH,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAChB,aAAa,CAAC;EAC/D;EAGAiB,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACZ,eAAe,EAAE;IACtB,IAAI,CAAC,IAAI,CAAChB,QAAQ,IAAI,IAAI,CAAChC,aAAa,EAAE;MACxC,IAAI,CAACD,YAAY,EAAE;;EAEvB;EAEQiD,eAAeA,CAAA;IACrB;IACA,MAAMa,KAAK,GAAG5B,MAAM,CAACkB,UAAU;IAC/B,MAAMW,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAAC9B,QAAQ,GAAG6B,KAAK,IAAI,GAAG,IAAIG,iBAAiB;IAEjDlB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChCc,KAAK,EAAEA,KAAK;MACZK,MAAM,EAAEjC,MAAM,CAACoB,WAAW;MAC1BrB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgC,iBAAiB,EAAEA,iBAAiB;MACpCF,SAAS,EAAEA;KACZ,CAAC;EACJ;EAEA1E,aAAaA,CAAA;IACX,IAAI,CAACY,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACmE,gBAAgB,EAAE;EACzB;EAEApE,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACmE,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACnE,aAAa,EAAE;MACtBsD,QAAQ,CAACc,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;KACxC,MAAM;MACLhB,QAAQ,CAACc,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;;EAErC;EAQA;EACAtF,aAAaA,CAAA;IACX,IAAI,CAACY,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACuE,gBAAgB,EAAE;EACzB;EAEAzE,YAAYA,CAAA;IACV,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACuE,gBAAgB,EAAE;EACzB;EAEA/D,cAAcA,CAACmE,OAAe;IAC5B,IAAI,CAAC1C,iBAAiB,GAAG0C,OAAO;IAChC,IAAI,CAAC5C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC/B,aAAa,GAAG,KAAK;IAE1B;IACA,MAAM4E,MAAM,GAA8B;MACxC,UAAU,EAAE,mBAAmB;MAC/B,QAAQ,EAAE,iBAAiB;MAC3B,UAAU,EAAE,cAAc;MAC1B,WAAW,EAAE,mBAAmB;MAChC,aAAa,EAAE,qBAAqB;MACpC,YAAY,EAAE;KACf;IAED,IAAI,CAAC5C,mBAAmB,GAAG4C,MAAM,CAACD,OAAO,CAAC,IAAI,UAAU;IACxD,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEA/C,mBAAmBA,CAAA;IACjB,IAAI,CAACO,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACsC,gBAAgB,EAAE;EACzB;EAEA;EACAM,UAAUA,CAAA;IACR,IAAI,CAACvC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;IACAY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,OAAO,CAAC;EAC5C;EAEAwC,YAAYA,CAAA;IACV;IACA5B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA4B,YAAYA,CAAA;IACV;IACA7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAIgB,SAAS,CAACa,KAAK,EAAE;MACnBb,SAAS,CAACa,KAAK,CAAC;QACdC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE9C,MAAM,CAAC+C,QAAQ,CAACC;OACtB,CAAC;;EAEN;EAEAC,SAASA,CAAA;IACP;IACApC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA;EACAoC,WAAWA,CAAA;IACTrC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAqC,SAASA,CAACC,KAAU;IAClBvC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEsC,KAAK,CAAC;IACjC;EACF;EAEAC,cAAcA,CAACC,KAAa,EAAEF,KAAU;IACtC,OAAOA,KAAK,CAACjD,EAAE,IAAImD,KAAK;EAC1B;EAEA;EACAC,iBAAiBA,CAAC5B,KAAiB,EAAEyB,KAAU;IAC7CA,KAAK,CAAC5C,QAAQ,GAAG,IAAI;IACrB;IACA,IAAI,SAAS,IAAIsB,SAAS,EAAE;MAC1BA,SAAS,CAAC0B,OAAO,CAAC,EAAE,CAAC;;EAEzB;EAEAC,eAAeA,CAAC9B,KAAiB,EAAEyB,KAAU;IAC3CA,KAAK,CAAC5C,QAAQ,GAAG,KAAK;EACxB;EAEA;EACAkD,WAAWA,CAAA;IACT,IAAI,CAACzD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5BY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,OAAO,CAAC;IAC1C;EACF;EAEA0D,cAAcA,CAAA;IACZ9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;EACF;EAEA8C,YAAYA,CAAA;IACV/C,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B;EACF;EAEA+C,eAAeA,CAAA;IACbhD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACAgD,kBAAkBA,CAAA;IAChBjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAiD,qBAAqBA,CAAA;IACnBlD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEAkD,gBAAgBA,CAAA;IACdnD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;EACF;EAEAmD,oBAAoBA,CAAA;IAClBpD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;EAEAoD,kBAAkBA,CAAA;IAChBrD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAqD,cAAcA,CAAA;IACZtD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;;;uBAlTWjB,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAuE,SAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAbpI,EAAA,CAAAK,UAAA,oBAAAiI,wCAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA9C,QAAA,CAAAgD,MAAA,CAAgB;UAAA,UAAAvI,EAAA,CAAAwI,eAAA,CAAH;;;;;;;;;;UC/B1BxI,EAAA,CAAAC,cAAA,aAA+H;UAmN7HD,EAjNA,CAAAa,UAAA,IAAA4H,4BAAA,kBAA4D,IAAAC,4BAAA,iBAqBlB,IAAAC,4BAAA,iBAqBE,IAAAC,4BAAA,iBAmBtB,IAAAC,4BAAA,iBAOA,IAAAC,4BAAA,kBAMA,IAAAC,4BAAA,kBAkEA,IAAAC,4BAAA,kBAsBA,IAAAC,4BAAA,kBA+C6B;UAmBrDjJ,EAAA,CAAAG,YAAA,EAAM;;;UAtO+FH,EAAzE,CAAAuB,WAAA,qBAAA8G,GAAA,CAAAzE,QAAA,CAAmC,iBAAAyE,GAAA,CAAAzG,aAAA,CAAqC,WAAAyG,GAAA,CAAAzE,QAAA,CAA0B;UAEhF5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAqBjC5D,EAAA,CAAAiB,SAAA,EAAe;UAAfjB,EAAA,CAAAkB,UAAA,UAAAmH,GAAA,CAAAzE,QAAA,CAAe;UAqBZ5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAmBpC5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAOd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAMd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAkEd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UAsBd5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;UA+Ce5D,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAAzE,QAAA,CAAc;;;qBDlM/CtE,YAAY,EAAA4J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7J,WAAW,EAAA8J,EAAA,CAAAC,OAAA,EACX9J,uBAAuB,EACvBC,aAAa,EACbC,gBAAgB,EAChBC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B;MAAAwJ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}