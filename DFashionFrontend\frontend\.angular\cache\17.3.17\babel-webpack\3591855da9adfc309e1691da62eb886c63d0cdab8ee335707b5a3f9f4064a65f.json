{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nfunction HomeComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h1\", 15);\n    i0.ɵɵtext(3, \"DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ion-icon\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17);\n    i0.ɵɵelement(6, \"ion-icon\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTabMenu());\n    });\n    i0.ɵɵelement(8, \"ion-icon\", 20);\n    i0.ɵɵtemplate(9, HomeComponent_div_1_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSidebar());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasNotifications);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 37);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 38);\n  }\n}\nfunction HomeComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_9_Template_div_click_0_listener() {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewStory(story_r5));\n    })(\"touchstart\", function HomeComponent_div_2_div_9_Template_div_touchstart_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchStart($event, story_r5));\n    })(\"touchend\", function HomeComponent_div_2_div_9_Template_div_touchend_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchEnd($event, story_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 27);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵtemplate(3, HomeComponent_div_2_div_9_div_3_Template, 1, 0, \"div\", 35)(4, HomeComponent_div_2_div_9_div_4_Template, 1, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-story\", story_r5.hasStory)(\"viewed\", story_r5.viewed)(\"touching\", story_r5.touching);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", story_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r5.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.username);\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createStory());\n    });\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵelement(4, \"img\", 28);\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵelement(6, \"ion-icon\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8, \"Your story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, HomeComponent_div_2_div_9_Template, 7, 11, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.instagramStories)(\"ngForTrackBy\", ctx_r1.trackByStoryId);\n  }\n}\nfunction HomeComponent_app_view_add_stories_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-view-add-stories\");\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h3\");\n    i0.ɵɵtext(3, \"Discover\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"trending\"));\n    });\n    i0.ɵɵelementStart(7, \"div\", 46);\n    i0.ɵɵelement(8, \"ion-icon\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 48);\n    i0.ɵɵtext(10, \"Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 49);\n    i0.ɵɵtext(12, \"Hot products right now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"brands\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 50);\n    i0.ɵɵelement(15, \"ion-icon\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 48);\n    i0.ɵɵtext(17, \"Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 49);\n    i0.ɵɵtext(19, \"Top fashion brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"arrivals\"));\n    });\n    i0.ɵɵelementStart(21, \"div\", 52);\n    i0.ɵɵelement(22, \"ion-icon\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 48);\n    i0.ɵɵtext(24, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 49);\n    i0.ɵɵtext(26, \"Latest arrivals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"suggested\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 54);\n    i0.ɵɵelement(29, \"ion-icon\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 48);\n    i0.ɵɵtext(31, \"For You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 49);\n    i0.ɵɵtext(33, \"Personalized picks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"influencers\"));\n    });\n    i0.ɵɵelementStart(35, \"div\", 56);\n    i0.ɵɵelement(36, \"ion-icon\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 48);\n    i0.ɵɵtext(38, \"Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 49);\n    i0.ɵɵtext(40, \"Top fashion creators\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_41_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"categories\"));\n    });\n    i0.ɵɵelementStart(42, \"div\", 58);\n    i0.ɵɵelement(43, \"ion-icon\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 48);\n    i0.ɵɵtext(45, \"Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 49);\n    i0.ɵɵtext(47, \"Browse by category\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"div\", 63);\n    i0.ɵɵelement(4, \"img\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"h3\");\n    i0.ɵɵtext(7, \"Your Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"@username\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_11_Template_ion_icon_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 66);\n    i0.ɵɵelement(12, \"app-sidebar\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-trending-products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-featured-brands\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-new-arrivals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-suggested-for-you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-top-fashion-influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"ion-icon\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r11.name);\n  }\n}\nfunction HomeComponent_div_12_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵtemplate(2, HomeComponent_div_12_div_11_div_2_Template, 5, 2, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction HomeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_12_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebarContent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 69);\n    i0.ɵɵtemplate(6, HomeComponent_div_12_div_6_Template, 2, 0, \"div\", 70)(7, HomeComponent_div_12_div_7_Template, 2, 0, \"div\", 70)(8, HomeComponent_div_12_div_8_Template, 2, 0, \"div\", 70)(9, HomeComponent_div_12_div_9_Template, 2, 0, \"div\", 70)(10, HomeComponent_div_12_div_10_Template, 2, 0, \"div\", 70)(11, HomeComponent_div_12_div_11_Template, 3, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarContentOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentSidebarTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"trending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"brands\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"arrivals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"suggested\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"influencers\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"categories\");\n  }\n}\nfunction HomeComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"ion-icon\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 80);\n    i0.ɵɵelement(4, \"ion-icon\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 80);\n    i0.ɵɵelement(6, \"ion-icon\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 80);\n    i0.ɵɵelement(8, \"ion-icon\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"div\", 84);\n    i0.ɵɵelement(11, \"img\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.isMobile = false;\n    this.isSidebarOpen = false;\n    this.isTabMenuOpen = false;\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.currentSidebarTitle = '';\n    this.hasNotifications = true; // Example notification state\n    this.window = window; // For template access\n    // TikTok-style interaction states\n    this.isLiked = false;\n    // Instagram Stories Data - Enhanced for responsive design\n    this.instagramStories = [{\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }, {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }, {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true\n    }, {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }, {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }, {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true\n    }, {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }, {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }];\n    // Categories Data\n    this.categories = [{\n      name: 'Women',\n      icon: 'woman'\n    }, {\n      name: 'Men',\n      icon: 'man'\n    }, {\n      name: 'Kids',\n      icon: 'happy'\n    }, {\n      name: 'Shoes',\n      icon: 'footsteps'\n    }, {\n      name: 'Bags',\n      icon: 'bag'\n    }, {\n      name: 'Accessories',\n      icon: 'watch'\n    }, {\n      name: 'Beauty',\n      icon: 'flower'\n    }, {\n      name: 'Sports',\n      icon: 'fitness'\n    }];\n    this.preventScroll = e => {\n      if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n        e.preventDefault();\n      }\n    };\n  }\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, {\n      passive: false\n    });\n  }\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n  onResize(event) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n  checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n  toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n  openSidebarTab(tabType) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n    // Set title based on tab type\n    const titles = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n  viewStory(story) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n  trackByStoryId(index, story) {\n    return story.id || index;\n  }\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event, story) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n  onStoryTouchEnd(event, story) {\n    story.touching = false;\n  }\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function HomeComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 15,\n      consts: [[1, \"home-container\"], [\"class\", \"mobile-header instagram-style\", 4, \"ngIf\"], [\"class\", \"instagram-stories-section\", 4, \"ngIf\"], [1, \"content-grid\"], [1, \"main-content\"], [4, \"ngIf\"], [1, \"desktop-sidebar\"], [\"class\", \"tab-menu-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"sidebar-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"instagram-tab-menu\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"mobile-sidebar\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"sidebar-content-modal\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"instagram-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-header\", \"instagram-style\"], [1, \"header-left\"], [1, \"app-logo\"], [\"name\", \"chevron-down\", 1, \"logo-dropdown\"], [1, \"header-right\"], [\"name\", \"heart-outline\", 1, \"header-icon\"], [1, \"menu-icon-container\", 3, \"click\"], [\"name\", \"grid-outline\", 1, \"header-icon\", \"menu-icon\"], [\"class\", \"notification-dot\", 4, \"ngIf\"], [\"name\", \"menu-outline\", 1, \"header-icon\", \"menu-icon\"], [1, \"notification-dot\"], [1, \"instagram-stories-section\"], [1, \"stories-container\"], [1, \"story-item\", \"your-story\", 3, \"click\"], [1, \"story-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Your story\"], [1, \"add-story-btn\"], [\"name\", \"add\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", \"touchstart\", \"touchend\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"story-item\", 3, \"click\", \"touchstart\", \"touchend\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [\"class\", \"story-ring\", 4, \"ngIf\"], [\"class\", \"story-gradient-ring\", 4, \"ngIf\"], [1, \"story-ring\"], [1, \"story-gradient-ring\"], [1, \"tab-menu-overlay\", 3, \"click\"], [1, \"sidebar-overlay\", 3, \"click\"], [1, \"instagram-tab-menu\"], [1, \"tab-menu-header\"], [\"name\", \"close-outline\", 1, \"close-icon\", 3, \"click\"], [1, \"tab-menu-grid\"], [1, \"tab-item\", 3, \"click\"], [1, \"tab-icon\", \"trending\"], [\"name\", \"trending-up\"], [1, \"tab-label\"], [1, \"tab-tooltip\"], [1, \"tab-icon\", \"brands\"], [\"name\", \"diamond\"], [1, \"tab-icon\", \"arrivals\"], [\"name\", \"sparkles\"], [1, \"tab-icon\", \"suggested\"], [\"name\", \"heart\"], [1, \"tab-icon\", \"influencers\"], [\"name\", \"people\"], [1, \"tab-icon\", \"categories\"], [\"name\", \"grid\"], [1, \"mobile-sidebar\"], [1, \"sidebar-header\"], [1, \"user-profile\"], [1, \"profile-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Profile\"], [1, \"profile-info\"], [1, \"sidebar-content\"], [1, \"sidebar-content-modal\"], [1, \"modal-header\"], [1, \"modal-content\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\"], [1, \"category-icon\"], [3, \"name\"], [1, \"instagram-bottom-nav\"], [1, \"nav-item\", \"active\"], [\"name\", \"home\"], [1, \"nav-item\"], [\"name\", \"search\"], [\"name\", \"add-circle-outline\"], [\"name\", \"play-circle-outline\"], [1, \"profile-avatar-nav\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 12, 1, \"div\", 1)(2, HomeComponent_div_2_Template, 10, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵtemplate(5, HomeComponent_app_view_add_stories_5_Template, 1, 0, \"app-view-add-stories\", 5);\n          i0.ɵɵelement(6, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"app-sidebar\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HomeComponent_div_8_Template, 1, 2, \"div\", 7)(9, HomeComponent_div_9_Template, 1, 2, \"div\", 8)(10, HomeComponent_div_10_Template, 48, 2, \"div\", 9)(11, HomeComponent_div_11_Template, 13, 2, \"div\", 10)(12, HomeComponent_div_12_Template, 12, 9, \"div\", 11)(13, HomeComponent_div_13_Template, 12, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mobile-instagram\", ctx.isMobile)(\"sidebar-open\", ctx.isSidebarOpen)(\"mobile\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IonicModule, i2.IonIcon, ViewAddStoriesComponent, FeedComponent, SidebarComponent, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n  position: relative;\\n  background: #ffffff;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  color: #262626 !important;\\n  padding: 0 !important;\\n  min-height: 100vh !important;\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 60px;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 1001; \\n\\n  padding: 0 16px;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.mobile-header.instagram-style[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 400;\\n  color: #262626;\\n  margin: 0;\\n  font-family: \\\"Billabong\\\", cursive, -apple-system, BlinkMacSystemFont, sans-serif;\\n  letter-spacing: 0.5px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #262626;\\n  margin-top: 2px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  cursor: pointer;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 8px;\\n  height: 8px;\\n  background: #ff3040;\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 60px;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 999;\\n  padding: 12px 0;\\n  height: 100px; \\n\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  \\n\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  overflow-y: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: 100%;\\n  align-items: center;\\n  min-width: max-content;\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 998; \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  overscroll-behavior-x: contain;\\n  scroll-snap-type: x proximity;\\n  \\n\\n  will-change: scroll-position;\\n  transform: translateZ(0); \\n\\n  \\n\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 10px;\\n    scroll-snap-type: x mandatory;\\n    \\n\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n    overscroll-behavior-x: contain;\\n    \\n\\n    contain: layout style paint;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 8px;\\n    \\n\\n    scroll-padding-left: 8px;\\n    scroll-padding-right: 8px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 6px;\\n    gap: 6px;\\n    \\n\\n    scroll-padding-left: 6px;\\n    scroll-padding-right: 6px;\\n  }\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 6px;\\n  min-width: 70px;\\n  max-width: 70px;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n  scroll-snap-align: start;\\n  scroll-snap-stop: normal;\\n  position: relative;\\n  \\n\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n    max-width: 65px;\\n    gap: 5px;\\n    \\n\\n    padding: 4px;\\n    margin: -4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n    max-width: 60px;\\n    gap: 4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 55px;\\n    max-width: 55px;\\n    gap: 3px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 50px;\\n    max-width: 50px;\\n    gap: 2px;\\n  }\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border: 2px solid #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n  position: relative;\\n  flex-shrink: 0;\\n  transition: all 0.2s ease;\\n  \\n\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%] {\\n  border: 2px solid transparent;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  padding: 2px;\\n  \\n\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed) {\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%] {\\n  border: 2px solid #c7c7c7;\\n  background: #c7c7c7;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_storyTouchFeedback 0.2s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n  opacity: 0.8;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -2;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_storyRingGradient 3s infinite;\\n  filter: blur(2px);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  display: block;\\n  transition: transform 0.2s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 55px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  font-weight: 400;\\n  margin-top: 4px;\\n  \\n\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  text-rendering: optimizeLegibility;\\n  \\n\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 65px;\\n    font-weight: 500; \\n\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 60px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    max-width: 55px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 7px;\\n    max-width: 50px;\\n    line-height: 1;\\n    font-weight: 600; \\n\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0.7);\\n  }\\n  70% {\\n    transform: scale(1.05) translateZ(0);\\n    box-shadow: 0 0 0 10px rgba(240, 148, 51, 0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyRingGradient {\\n  0% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  25% {\\n    background: linear-gradient(90deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  50% {\\n    background: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  75% {\\n    background: linear-gradient(180deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  100% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyTouchFeedback {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n  }\\n  50% {\\n    transform: scale(0.95) translateZ(0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n  }\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-top: 1px solid #dbdbdb;\\n  justify-content: space-around;\\n  align-items: center;\\n  padding: 8px 0;\\n  z-index: 1000;\\n  height: 60px;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);\\n  padding-bottom: max(8px, env(safe-area-inset-bottom));\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  min-width: 44px;\\n  min-height: 44px;\\n  position: relative;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #262626;\\n  transform: scale(1.1);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  transition: all 0.2s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 1px solid #8e8e8e;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%] {\\n  border: 2px solid #262626;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 2px;\\n  right: 2px;\\n  background: #ff3040;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n.content-grid[_ngcontent-%COMP%]   .tab-menu-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1500;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .tab-menu-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #000000;\\n  border-top-left-radius: 20px;\\n  border-top-right-radius: 20px;\\n  z-index: 1600;\\n  transform: translateY(100%);\\n  transition: transform 0.3s ease;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px 16px;\\n  border-bottom: 1px solid #262626;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 24px;\\n  padding: 24px;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  padding: 16px;\\n  border-radius: 16px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  transform: scale(1.05);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4, #44a08d);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea, #fed6e3);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  text-align: center;\\n  line-height: 1.3;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: #000000;\\n  z-index: 1700;\\n  transform: translateX(100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal.active[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background: #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #262626;\\n  z-index: 10;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  background: #000000;\\n  color: white;\\n  min-height: calc(100vh - 80px);\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     * {\\n  background-color: transparent !important;\\n  color: white !important;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item {\\n  background: #1a1a1a !important;\\n  border: 1px solid #262626 !important;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark {\\n  color: white !important;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white {\\n  background: #1a1a1a !important;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n  padding: 24px;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 20px;\\n  background: #1a1a1a;\\n  border-radius: 16px;\\n  border: 1px solid #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background: #262626;\\n  transform: scale(1.02);\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  background: #ffffff;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.desktop-sidebar[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.sidebar-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.65);\\n  z-index: 200;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.sidebar-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.mobile-sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -100%;\\n  width: 85%;\\n  max-width: 400px;\\n  height: 100%;\\n  background: #ffffff;\\n  z-index: 300;\\n  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.mobile-sidebar.active[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 16px;\\n  border-bottom: 1px solid #dbdbdb;\\n  background: #fafafa;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  padding: 8px;\\n  margin: -8px;\\n  transition: color 0.2s ease;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 100%;\\n    padding: 0 16px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    max-width: 768px;\\n    margin: 0 auto;\\n    padding: 0 24px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    padding-top: 80px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 80px 0 0 0;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  min-height: 100vh !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 100px !important;\\n  padding: 8px 0 !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  \\n\\n  transform: translateZ(0) !important;\\n  will-change: scroll-position !important;\\n  contain: layout style paint !important;\\n  \\n\\n  backdrop-filter: blur(10px) !important;\\n  -webkit-backdrop-filter: blur(10px) !important;\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95) !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n  \\n\\n  grid-template-columns: 1fr !important;\\n  padding: 160px 0 60px 0 !important; \\n\\n  background: #ffffff !important;\\n  gap: 0 !important;\\n  margin: 0 !important;\\n  max-width: 100% !important;\\n  min-height: calc(100vh - 220px) !important;\\n  overflow-x: hidden !important;\\n  position: relative !important;\\n  z-index: 1 !important; \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n@media (min-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 16px 60px 16px !important; \\n\\n    max-width: 768px !important;\\n    margin: 0 auto !important; \\n\\n    gap: 16px !important;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 300px !important; \\n\\n    padding: 160px 24px 60px 24px !important;\\n    max-width: 1200px !important;\\n    gap: 32px !important;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px !important; \\n\\n    padding: 160px 32px 60px 32px !important;\\n    max-width: 1400px !important;\\n    gap: 40px !important;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 48px 60px 48px !important;\\n    max-width: 1600px !important;\\n    gap: 48px !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-top: 1px solid #dbdbdb !important;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n    padding: 0 !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0;\\n    margin: 0;\\n    background: #fafafa;\\n  }\\n  .content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    background: white;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin: 0;\\n    padding: 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 60px !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    min-height: 100vh;\\n    padding: 0 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 160px 0 60px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    color: #262626 !important;\\n    gap: 0;\\n    padding: 0;\\n    width: 100% !important;\\n    max-width: 100% !important;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 90%;\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .sidebar-overlay[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.8);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 95%;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    overflow-x: hidden !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 360px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    padding: 20px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n    padding: 0;\\n    margin: 0 auto;\\n    \\n\\n    grid-template-columns: 1fr 300px;\\n    gap: 32px;\\n    max-width: 1000px;\\n    \\n\\n    \\n\\n    \\n\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 350px;\\n    gap: 36px;\\n    max-width: 1200px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1200px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px;\\n    gap: 40px;\\n    max-width: 1400px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1440px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 450px;\\n    gap: 48px;\\n    max-width: 1600px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "ViewAddStoriesComponent", "FeedComponent", "SidebarComponent", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_div_1_Template_div_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleTabMenu", "ɵɵtemplate", "HomeComponent_div_1_div_9_Template", "HomeComponent_div_1_Template_div_click_10_listener", "toggleSidebar", "ɵɵadvance", "ɵɵproperty", "hasNotifications", "HomeComponent_div_2_div_9_Template_div_click_0_listener", "story_r5", "_r4", "$implicit", "viewStory", "HomeComponent_div_2_div_9_Template_div_touchstart_0_listener", "$event", "onStoryTouchStart", "HomeComponent_div_2_div_9_Template_div_touchend_0_listener", "onStoryTouchEnd", "HomeComponent_div_2_div_9_div_3_Template", "HomeComponent_div_2_div_9_div_4_Template", "ɵɵclassProp", "hasStory", "viewed", "touching", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "HomeComponent_div_2_Template_div_click_2_listener", "_r3", "createStory", "HomeComponent_div_2_div_9_Template", "instagramStories", "trackByStoryId", "HomeComponent_div_8_Template_div_click_0_listener", "_r6", "closeTabMenu", "isTabMenuOpen", "HomeComponent_div_9_Template_div_click_0_listener", "_r7", "closeSidebar", "isSidebarOpen", "HomeComponent_div_10_Template_ion_icon_click_4_listener", "_r8", "HomeComponent_div_10_Template_div_click_6_listener", "openSidebarTab", "HomeComponent_div_10_Template_div_click_13_listener", "HomeComponent_div_10_Template_div_click_20_listener", "HomeComponent_div_10_Template_div_click_27_listener", "HomeComponent_div_10_Template_div_click_34_listener", "HomeComponent_div_10_Template_div_click_41_listener", "HomeComponent_div_11_Template_ion_icon_click_10_listener", "_r9", "category_r11", "icon", "name", "HomeComponent_div_12_div_11_div_2_Template", "categories", "HomeComponent_div_12_Template_ion_icon_click_4_listener", "_r10", "closeSidebarContent", "HomeComponent_div_12_div_6_Template", "HomeComponent_div_12_div_7_Template", "HomeComponent_div_12_div_8_Template", "HomeComponent_div_12_div_9_Template", "HomeComponent_div_12_div_10_Template", "HomeComponent_div_12_div_11_Template", "isSidebarContentOpen", "currentSidebarTitle", "currentSidebarTab", "HomeComponent", "constructor", "isMobile", "window", "isLiked", "id", "preventScroll", "e", "preventDefault", "ngOnInit", "checkScreenSize", "console", "log", "length", "document", "addEventListener", "passive", "ngOnDestroy", "removeEventListener", "onResize", "event", "width", "innerWidth", "userAgent", "navigator", "isMobileUserAgent", "test", "height", "innerHeight", "toggleBodyScroll", "body", "style", "overflow", "tabType", "titles", "toggleLike", "openComments", "shareContent", "share", "title", "text", "url", "location", "href", "openMusic", "story", "index", "vibrate", "onLikeClick", "onCommentClick", "onShareClick", "onBookmarkClick", "navigateToTrending", "navigateToNewArrivals", "navigateToOffers", "navigateToCategories", "navigateToWishlist", "navigateToCart", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_app_view_add_stories_5_Template", "HomeComponent_div_8_Template", "HomeComponent_div_9_Template", "HomeComponent_div_10_Template", "HomeComponent_div_11_Template", "HomeComponent_div_12_Template", "HomeComponent_div_13_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IonicModule,\n    ViewAddStoriesComponent,\n    FeedComponent,\n    SidebarComponent,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent\n  ],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  isMobile = false;\n  isSidebarOpen = false;\n  isTabMenuOpen = false;\n  isSidebarContentOpen = false;\n  currentSidebarTab = '';\n  currentSidebarTitle = '';\n  hasNotifications = true; // Example notification state\n  window = window; // For template access\n\n  // TikTok-style interaction states\n  isLiked = false;\n\n  // Instagram Stories Data - Enhanced for responsive design\n  instagramStories = [\n    {\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    },\n    {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    },\n    {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true\n    },\n    {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    },\n    {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    },\n    {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true\n    },\n    {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    },\n    {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false\n    }\n  ];\n\n  // Categories Data\n  categories = [\n    { name: 'Women', icon: 'woman' },\n    { name: 'Men', icon: 'man' },\n    { name: 'Kids', icon: 'happy' },\n    { name: 'Shoes', icon: 'footsteps' },\n    { name: 'Bags', icon: 'bag' },\n    { name: 'Accessories', icon: 'watch' },\n    { name: 'Beauty', icon: 'flower' },\n    { name: 'Sports', icon: 'fitness' }\n  ];\n\n  constructor() {}\n\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, { passive: false });\n  }\n\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n\n  private checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  private toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n\n  private preventScroll = (e: TouchEvent) => {\n    if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n      e.preventDefault();\n    }\n  }\n\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  openSidebarTab(tabType: string) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n\n    // Set title based on tab type\n    const titles: { [key: string]: string } = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n\n  viewStory(story: any) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n\n  trackByStoryId(index: number, story: any): any {\n    return story.id || index;\n  }\n\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event: TouchEvent, story: any) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n\n  onStoryTouchEnd(event: TouchEvent, story: any) {\n    story.touching = false;\n  }\n\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n}\n", "<div class=\"home-container\" [class.mobile-instagram]=\"isMobile\" [class.sidebar-open]=\"isSidebarOpen\" [class.mobile]=\"isMobile\">\n  <!-- Mobile Header with Instagram-like styling -->\n  <div class=\"mobile-header instagram-style\" *ngIf=\"isMobile\">\n    <div class=\"header-left\">\n      <h1 class=\"app-logo\">DFashion</h1>\n      <ion-icon name=\"chevron-down\" class=\"logo-dropdown\"></ion-icon>\n    </div>\n    <div class=\"header-right\">\n      <ion-icon name=\"heart-outline\" class=\"header-icon\"></ion-icon>\n      <div class=\"menu-icon-container\" (click)=\"toggleTabMenu()\">\n        <ion-icon name=\"grid-outline\" class=\"header-icon menu-icon\"></ion-icon>\n        <div class=\"notification-dot\" *ngIf=\"hasNotifications\">3</div>\n      </div>\n      <div class=\"menu-icon-container\" (click)=\"toggleSidebar()\">\n        <ion-icon name=\"menu-outline\" class=\"header-icon menu-icon\"></ion-icon>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Stories Section (Mobile Only) - Enhanced for 768px to 320px -->\n  <div class=\"instagram-stories-section\" *ngIf=\"isMobile\">\n    <div class=\"stories-container\">\n      <div class=\"story-item your-story\" (click)=\"createStory()\">\n        <div class=\"story-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Your story\">\n          <div class=\"add-story-btn\">\n            <ion-icon name=\"add\"></ion-icon>\n          </div>\n        </div>\n        <span class=\"story-username\">Your story</span>\n      </div>\n      <div class=\"story-item\" *ngFor=\"let story of instagramStories; trackBy: trackByStoryId\"\n           (click)=\"viewStory(story)\"\n           (touchstart)=\"onStoryTouchStart($event, story)\"\n           (touchend)=\"onStoryTouchEnd($event, story)\">\n        <div class=\"story-avatar\"\n             [class.has-story]=\"story.hasStory\"\n             [class.viewed]=\"story.viewed\"\n             [class.touching]=\"story.touching\">\n          <img [src]=\"story.avatar\" [alt]=\"story.username\" loading=\"lazy\">\n          <div class=\"story-ring\" *ngIf=\"story.hasStory && !story.viewed\"></div>\n          <div class=\"story-gradient-ring\" *ngIf=\"story.hasStory && !story.viewed\"></div>\n        </div>\n        <span class=\"story-username\">{{story.username}}</span>\n      </div>\n    </div>\n  </div>\n\n\n  <div class=\"content-grid\">\n    <!-- Main Feed -->\n    <div class=\"main-content\">\n      <!-- Desktop Stories Component (Hidden on Mobile) -->\n      <app-view-add-stories *ngIf=\"!isMobile\"></app-view-add-stories>\n      <!-- Instagram-style Feed with Posts and Reels -->\n      <app-feed></app-feed>\n    </div>\n\n    <!-- Desktop Sidebar -->\n    <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n  </div>\n\n  <!-- Mobile Tab Menu Overlay -->\n  <div class=\"tab-menu-overlay\"\n       [class.active]=\"isTabMenuOpen\"\n       (click)=\"closeTabMenu()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Mobile Sidebar Overlay -->\n  <div class=\"sidebar-overlay\"\n       [class.active]=\"isSidebarOpen\"\n       (click)=\"closeSidebar()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Instagram-Style Tab Menu -->\n  <div class=\"instagram-tab-menu\"\n       [class.active]=\"isTabMenuOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"tab-menu-header\">\n      <h3>Discover</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeTabMenu()\"></ion-icon>\n    </div>\n\n    <div class=\"tab-menu-grid\">\n      <!-- Trending Products Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('trending')\">\n        <div class=\"tab-icon trending\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Trending</span>\n        <div class=\"tab-tooltip\">Hot products right now</div>\n      </div>\n\n      <!-- Featured Brands Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('brands')\">\n        <div class=\"tab-icon brands\">\n          <ion-icon name=\"diamond\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Brands</span>\n        <div class=\"tab-tooltip\">Top fashion brands</div>\n      </div>\n\n      <!-- New Arrivals Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('arrivals')\">\n        <div class=\"tab-icon arrivals\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">New</span>\n        <div class=\"tab-tooltip\">Latest arrivals</div>\n      </div>\n\n      <!-- Suggested for You Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('suggested')\">\n        <div class=\"tab-icon suggested\">\n          <ion-icon name=\"heart\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">For You</span>\n        <div class=\"tab-tooltip\">Personalized picks</div>\n      </div>\n\n      <!-- Fashion Influencers Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('influencers')\">\n        <div class=\"tab-icon influencers\">\n          <ion-icon name=\"people\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Influencers</span>\n        <div class=\"tab-tooltip\">Top fashion creators</div>\n      </div>\n\n      <!-- Categories Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('categories')\">\n        <div class=\"tab-icon categories\">\n          <ion-icon name=\"grid\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Categories</span>\n        <div class=\"tab-tooltip\">Browse by category</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <div class=\"mobile-sidebar\"\n       [class.active]=\"isSidebarOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"sidebar-header\">\n      <div class=\"user-profile\">\n        <div class=\"profile-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>Your Profile</h3>\n          <p>&#64;username</p>\n        </div>\n      </div>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebar()\"></ion-icon>\n    </div>\n\n    <div class=\"sidebar-content\">\n      <app-sidebar></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Sidebar Content Modal -->\n  <div class=\"sidebar-content-modal\"\n       [class.active]=\"isSidebarContentOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"modal-header\">\n      <h3>{{currentSidebarTitle}}</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebarContent()\"></ion-icon>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Trending Products Section -->\n      <div *ngIf=\"currentSidebarTab === 'trending'\" class=\"sidebar-section\">\n        <app-trending-products></app-trending-products>\n      </div>\n\n      <!-- Featured Brands Section -->\n      <div *ngIf=\"currentSidebarTab === 'brands'\" class=\"sidebar-section\">\n        <app-featured-brands></app-featured-brands>\n      </div>\n\n      <!-- New Arrivals Section -->\n      <div *ngIf=\"currentSidebarTab === 'arrivals'\" class=\"sidebar-section\">\n        <app-new-arrivals></app-new-arrivals>\n      </div>\n\n      <!-- Suggested for you -->\n      <div *ngIf=\"currentSidebarTab === 'suggested'\" class=\"sidebar-section\">\n        <app-suggested-for-you></app-suggested-for-you>\n      </div>\n\n      <!-- Top Fashion Influencers -->\n      <div *ngIf=\"currentSidebarTab === 'influencers'\" class=\"sidebar-section\">\n        <app-top-fashion-influencers></app-top-fashion-influencers>\n      </div>\n\n      <!-- Categories (placeholder) -->\n      <div *ngIf=\"currentSidebarTab === 'categories'\" class=\"sidebar-section\">\n        <div class=\"categories-grid\">\n          <div class=\"category-item\" *ngFor=\"let category of categories\">\n            <div class=\"category-icon\">\n              <ion-icon [name]=\"category.icon\"></ion-icon>\n            </div>\n            <span>{{category.name}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Bottom Navigation (Mobile Only) -->\n  <div class=\"instagram-bottom-nav\" *ngIf=\"isMobile\">\n    <div class=\"nav-item active\">\n      <ion-icon name=\"home\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"search\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"add-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"play-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <div class=\"profile-avatar-nav\">\n        <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,uBAAuB,QAAQ,4DAA4D;AACpG,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,8BAA8B,QAAQ,4EAA4E;;;;;;ICAnHC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPhEH,EAFJ,CAAAC,cAAA,cAA4D,cACjC,aACF;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAI,SAAA,mBAA+D;IACjEJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,mBAA8D;IAC9DJ,EAAA,CAAAC,cAAA,cAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAC,kDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACxDZ,EAAA,CAAAI,SAAA,mBAAuE;IACvEJ,EAAA,CAAAa,UAAA,IAAAC,kCAAA,kBAAuD;IACzDd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAU,mDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,aAAA,EAAe;IAAA,EAAC;IACxDhB,EAAA,CAAAI,SAAA,oBAAuE;IAG7EJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAN+BH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAU,gBAAA,CAAsB;;;;;IA6BnDnB,EAAA,CAAAI,SAAA,cAAsE;;;;;IACtEJ,EAAA,CAAAI,SAAA,cAA+E;;;;;;IAVnFJ,EAAA,CAAAC,cAAA,cAGiD;IAA5CD,EAFA,CAAAK,UAAA,mBAAAe,wDAAA;MAAA,MAAAC,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,SAAA,CAAAH,QAAA,CAAgB;IAAA,EAAC,wBAAAI,6DAAAC,MAAA;MAAA,MAAAL,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACZF,MAAA,CAAAkB,iBAAA,CAAAD,MAAA,EAAAL,QAAA,CAAgC;IAAA,EAAC,sBAAAO,2DAAAF,MAAA;MAAA,MAAAL,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACnCF,MAAA,CAAAoB,eAAA,CAAAH,MAAA,EAAAL,QAAA,CAA8B;IAAA,EAAC;IAC9CrB,EAAA,CAAAC,cAAA,cAGuC;IACrCD,EAAA,CAAAI,SAAA,cAAgE;IAEhEJ,EADA,CAAAa,UAAA,IAAAiB,wCAAA,kBAAgE,IAAAC,wCAAA,kBACS;IAC3E/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EAClD;;;;IARCH,EAAA,CAAAiB,SAAA,EAAkC;IAElCjB,EAFA,CAAAgC,WAAA,cAAAX,QAAA,CAAAY,QAAA,CAAkC,WAAAZ,QAAA,CAAAa,MAAA,CACL,aAAAb,QAAA,CAAAc,QAAA,CACI;IAC/BnC,EAAA,CAAAiB,SAAA,EAAoB;IAACjB,EAArB,CAAAkB,UAAA,QAAAG,QAAA,CAAAe,MAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAoB,QAAAhB,QAAA,CAAAiB,QAAA,CAAuB;IACvBtC,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAkB,UAAA,SAAAG,QAAA,CAAAY,QAAA,KAAAZ,QAAA,CAAAa,MAAA,CAAqC;IAC5BlC,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAkB,UAAA,SAAAG,QAAA,CAAAY,QAAA,KAAAZ,QAAA,CAAAa,MAAA,CAAqC;IAE5ClC,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAuC,iBAAA,CAAAlB,QAAA,CAAAiB,QAAA,CAAkB;;;;;;IArBjDtC,EAFJ,CAAAC,cAAA,cAAwD,cACvB,cAC8B;IAAxBD,EAAA,CAAAK,UAAA,mBAAAmC,kDAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiC,WAAA,EAAa;IAAA,EAAC;IACxD1C,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,cAA6D;IAC7DJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAI,SAAA,mBAAgC;IAEpCJ,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAa,UAAA,IAAA8B,kCAAA,mBAGiD;IAYrD3C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfwCH,EAAA,CAAAiB,SAAA,GAAqB;IAAAjB,EAArB,CAAAkB,UAAA,YAAAT,MAAA,CAAAmC,gBAAA,CAAqB,iBAAAnC,MAAA,CAAAoC,cAAA,CAAuB;;;;;IAsBtF7C,EAAA,CAAAI,SAAA,2BAA+D;;;;;;IAUnEJ,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAyC,kDAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,YAAA,EAAc;IAAA,EAAC;IAE7BhD,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAwC,aAAA,CAA8B;;;;;;IAMnCjD,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAA6C,kDAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAE7BpD,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAA4C,aAAA,CAA8B;;;;;;IAU/BrD,EAJJ,CAAAC,cAAA,cAEsB,cACS,SACvB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,mBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAiD,wDAAA;MAAAtD,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,YAAA,EAAc;IAAA,EAAC;IAC5EhD,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAIJH,EAFF,CAAAC,cAAA,cAA2B,cAEkC;IAArCD,EAAA,CAAAK,UAAA,mBAAAmD,mDAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDzD,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,mBAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EACjD;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAAnCD,EAAA,CAAAK,UAAA,mBAAAqD,oDAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACtDzD,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,oBAAoC;IACtCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IAArCD,EAAA,CAAAK,UAAA,mBAAAsD,oDAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDzD,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,oBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IAGNH,EAAA,CAAAC,cAAA,eAA4D;IAAtCD,EAAA,CAAAK,UAAA,mBAAAuD,oDAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,WAAW,CAAC;IAAA,EAAC;IACzDzD,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAI,SAAA,oBAAkC;IACpCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA8D;IAAxCD,EAAA,CAAAK,UAAA,mBAAAwD,oDAAA;MAAA7D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,aAAa,CAAC;IAAA,EAAC;IAC3DzD,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAI,SAAA,oBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;IAGNH,EAAA,CAAAC,cAAA,eAA6D;IAAvCD,EAAA,CAAAK,UAAA,mBAAAyD,oDAAA;MAAA9D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,YAAY,CAAC;IAAA,EAAC;IAC1DzD,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAI,SAAA,oBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAGjDF,EAHiD,CAAAG,YAAA,EAAM,EAC7C,EACF,EACF;;;;IA9DDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAwC,aAAA,CAA8B;;;;;;IAsE7BjD,EALN,CAAAC,cAAA,cAEsB,cACQ,cACA,cACI;IAC1BD,EAAA,CAAAI,SAAA,cAA0D;IAC5DJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gBAAa;IAEpBF,EAFoB,CAAAG,YAAA,EAAI,EAChB,EACF;IACNH,EAAA,CAAAC,cAAA,oBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAA0D,yDAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAC5EpD,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAENH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,mBAA2B;IAE/BJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlBDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAA4C,aAAA,CAA8B;;;;;IA+B/BrD,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,SAAA,0BAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,kCAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAMAH,EADF,CAAAC,cAAA,cAA+D,cAClC;IACzBD,EAAA,CAAAI,SAAA,mBAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;;;;IAHQH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAA+C,YAAA,CAAAC,IAAA,CAAsB;IAE5BlE,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAuC,iBAAA,CAAA0B,YAAA,CAAAE,IAAA,CAAiB;;;;;IAL3BnE,EADF,CAAAC,cAAA,cAAwE,cACzC;IAC3BD,EAAA,CAAAa,UAAA,IAAAuD,0CAAA,kBAA+D;IAOnEpE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAP8CH,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,YAAAT,MAAA,CAAA4D,UAAA,CAAa;;;;;;IAjCjErE,EAJJ,CAAAC,cAAA,cAEsB,cACM,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,mBAAkF;IAAhCD,EAAA,CAAAK,UAAA,mBAAAiE,wDAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+D,mBAAA,EAAqB;IAAA,EAAC;IACnFxE,EADoF,CAAAG,YAAA,EAAW,EACzF;IAENH,EAAA,CAAAC,cAAA,cAA2B;IA2BzBD,EAzBA,CAAAa,UAAA,IAAA4D,mCAAA,kBAAsE,IAAAC,mCAAA,kBAKF,IAAAC,mCAAA,kBAKE,IAAAC,mCAAA,kBAKC,KAAAC,oCAAA,kBAKE,KAAAC,oCAAA,kBAKD;IAW5E9E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7CDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAsE,oBAAA,CAAqC;IAGlC/E,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAuE,mBAAA,CAAuB;IAMrBhF,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,gBAAsC;IAKtCjF,EAAA,CAAAiB,SAAA,EAAoC;IAApCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,cAAoC;IAKpCjF,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,gBAAsC;IAKtCjF,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,iBAAuC;IAKvCjF,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,mBAAyC;IAKzCjF,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,kBAAwC;;;;;IAehDjF,EADF,CAAAC,cAAA,cAAmD,cACpB;IAC3BD,EAAA,CAAAI,SAAA,mBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAgD;IAClDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,eACY;IAC9BD,EAAA,CAAAI,SAAA,eAA0D;IAGhEJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;ADzMR,OAAM,MAAO+E,aAAa;EAqFxBC,YAAA;IApFA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAA/B,aAAa,GAAG,KAAK;IACrB,KAAAJ,aAAa,GAAG,KAAK;IACrB,KAAA8B,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,iBAAiB,GAAG,EAAE;IACtB,KAAAD,mBAAmB,GAAG,EAAE;IACxB,KAAA7D,gBAAgB,GAAG,IAAI,CAAC,CAAC;IACzB,KAAAkE,MAAM,GAAGA,MAAM,CAAC,CAAC;IAEjB;IACA,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAA1C,gBAAgB,GAAG,CACjB;MACE2C,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,MAAM;MAChBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,MAAM;MAChBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,QAAQ;MAClBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,KAAK;MACfF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,QAAQ;MAClBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,OAAO;MACjBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,OAAO;MACjBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,EACD;MACEqD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,SAAS;MACnBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT,CACF;IAED;IACA,KAAAmC,UAAU,GAAG,CACX;MAAEF,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAO,CAAE,EAChC;MAAEC,IAAI,EAAE,KAAK;MAAED,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAO,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAW,CAAE,EACpC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAO,CAAE,EACtC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAQ,CAAE,EAClC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAS,CAAE,CACpC;IA8DO,KAAAsB,aAAa,GAAIC,CAAa,IAAI;MACxC,IAAI,IAAI,CAACpC,aAAa,IAAI,IAAI,CAACJ,aAAa,IAAI,IAAI,CAAC8B,oBAAoB,EAAE;QACzEU,CAAC,CAACC,cAAc,EAAE;;IAEtB,CAAC;EAhEc;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;MACzCV,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBxC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACmD;KACzC,CAAC;IACF;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACT,aAAa,EAAE;MAAEU,OAAO,EAAE;IAAK,CAAE,CAAC;EAChF;EAEAC,WAAWA,CAAA;IACTH,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACZ,aAAa,CAAC;EAC/D;EAGAa,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACV,eAAe,EAAE;IACtB,IAAI,CAAC,IAAI,CAACR,QAAQ,IAAI,IAAI,CAAC/B,aAAa,EAAE;MACxC,IAAI,CAACD,YAAY,EAAE;;EAEvB;EAEQwC,eAAeA,CAAA;IACrB;IACA,MAAMW,KAAK,GAAGlB,MAAM,CAACmB,UAAU;IAC/B,MAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAACrB,QAAQ,GAAGmB,KAAK,IAAI,GAAG,IAAII,iBAAiB;IAEjDd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChCS,KAAK,EAAEA,KAAK;MACZM,MAAM,EAAExB,MAAM,CAACyB,WAAW;MAC1B1B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBuB,iBAAiB,EAAEA,iBAAiB;MACpCF,SAAS,EAAEA;KACZ,CAAC;EACJ;EAEAzF,aAAaA,CAAA;IACX,IAAI,CAACqC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC0D,gBAAgB,EAAE;EACzB;EAEA3D,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC0D,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtB2C,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;KACxC,MAAM;MACLlB,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;;EAErC;EAQA;EACAtG,aAAaA,CAAA;IACX,IAAI,CAACqC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC8D,gBAAgB,EAAE;EACzB;EAEA/D,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC8D,gBAAgB,EAAE;EACzB;EAEAtD,cAAcA,CAAC0D,OAAe;IAC5B,IAAI,CAAClC,iBAAiB,GAAGkC,OAAO;IAChC,IAAI,CAACpC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC9B,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMmE,MAAM,GAA8B;MACxC,UAAU,EAAE,mBAAmB;MAC/B,QAAQ,EAAE,iBAAiB;MAC3B,UAAU,EAAE,cAAc;MAC1B,WAAW,EAAE,mBAAmB;MAChC,aAAa,EAAE,qBAAqB;MACpC,YAAY,EAAE;KACf;IAED,IAAI,CAACpC,mBAAmB,GAAGoC,MAAM,CAACD,OAAO,CAAC,IAAI,UAAU;IACxD,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEAvC,mBAAmBA,CAAA;IACjB,IAAI,CAACO,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC8B,gBAAgB,EAAE;EACzB;EAEA;EACAM,UAAUA,CAAA;IACR,IAAI,CAAC/B,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;IACAO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,OAAO,CAAC;EAC5C;EAEAgC,YAAYA,CAAA;IACV;IACAzB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEAyB,YAAYA,CAAA;IACV;IACA1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAIY,SAAS,CAACc,KAAK,EAAE;MACnBd,SAAS,CAACc,KAAK,CAAC;QACdC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAEtC,MAAM,CAACuC,QAAQ,CAACC;OACtB,CAAC;;EAEN;EAEAC,SAASA,CAAA;IACP;IACAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA;EACApD,WAAWA,CAAA;IACTmD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAtE,SAASA,CAACuG,KAAU;IAClBlC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiC,KAAK,CAAC;IACjC;EACF;EAEAlF,cAAcA,CAACmF,KAAa,EAAED,KAAU;IACtC,OAAOA,KAAK,CAACxC,EAAE,IAAIyC,KAAK;EAC1B;EAEA;EACArG,iBAAiBA,CAAC2E,KAAiB,EAAEyB,KAAU;IAC7CA,KAAK,CAAC5F,QAAQ,GAAG,IAAI;IACrB;IACA,IAAI,SAAS,IAAIuE,SAAS,EAAE;MAC1BA,SAAS,CAACuB,OAAO,CAAC,EAAE,CAAC;;EAEzB;EAEApG,eAAeA,CAACyE,KAAiB,EAAEyB,KAAU;IAC3CA,KAAK,CAAC5F,QAAQ,GAAG,KAAK;EACxB;EAEA;EACA+F,WAAWA,CAAA;IACT,IAAI,CAAC5C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5BO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,OAAO,CAAC;IAC1C;EACF;EAEA6C,cAAcA,CAAA;IACZtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;EACF;EAEAsC,YAAYA,CAAA;IACVvC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B;EACF;EAEAuC,eAAeA,CAAA;IACbxC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACAwC,kBAAkBA,CAAA;IAChBzC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAyC,qBAAqBA,CAAA;IACnB1C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEA0C,gBAAgBA,CAAA;IACd3C,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;EACF;EAEA2C,oBAAoBA,CAAA;IAClB5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;EAEA4C,kBAAkBA,CAAA;IAChB7C,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEA6C,cAAcA,CAAA;IACZ9C,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;;;uBAvSWZ,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAA0D,SAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAb/I,EAAA,CAAAK,UAAA,oBAAA4I,wCAAAvH,MAAA;YAAA,OAAAsH,GAAA,CAAA3C,QAAA,CAAA3E,MAAA,CAAgB;UAAA,UAAA1B,EAAA,CAAAkJ,eAAA,CAAH;;;;;;;;;;UC/B1BlJ,EAAA,CAAAC,cAAA,aAA+H;UAoB7HD,EAlBA,CAAAa,UAAA,IAAAsI,4BAAA,kBAA4D,IAAAC,4BAAA,kBAkBJ;UA+BtDpJ,EAFF,CAAAC,cAAA,aAA0B,aAEE;UAExBD,EAAA,CAAAa,UAAA,IAAAwI,6CAAA,kCAAwC;UAExCrJ,EAAA,CAAAI,SAAA,eAAqB;UACvBJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAI,SAAA,qBAAmD;UACrDJ,EAAA,CAAAG,YAAA,EAAM;UA0JNH,EAvJA,CAAAa,UAAA,IAAAyI,4BAAA,iBAGsB,IAAAC,4BAAA,iBAOA,KAAAC,6BAAA,kBAMA,KAAAC,6BAAA,mBAkEA,KAAAC,6BAAA,mBAsBA,KAAAC,6BAAA,mBA+C6B;UAmBrD3J,EAAA,CAAAG,YAAA,EAAM;;;UAzO+FH,EAAzE,CAAAgC,WAAA,qBAAAgH,GAAA,CAAA5D,QAAA,CAAmC,iBAAA4D,GAAA,CAAA3F,aAAA,CAAqC,WAAA2F,GAAA,CAAA5D,QAAA,CAA0B;UAEhFpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAkBlBpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAiC3BpF,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAkB,UAAA,UAAA8H,GAAA,CAAA5D,QAAA,CAAe;UAapCpF,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAOdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAMdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAkEdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAsBdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UA+CepF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;;;qBDrM/C9F,YAAY,EAAAsK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZvK,WAAW,EAAAwK,EAAA,CAAAC,OAAA,EACXxK,uBAAuB,EACvBC,aAAa,EACbC,gBAAgB,EAChBC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B;MAAAkK,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}