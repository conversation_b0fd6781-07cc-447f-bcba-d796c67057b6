{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../shop-by-category/shop-by-category.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction SidebarComponent_app_trending_products_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-trending-products\");\n  }\n}\nfunction SidebarComponent_app_featured_brands_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-featured-brands\");\n  }\n}\nfunction SidebarComponent_app_new_arrivals_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-new-arrivals\");\n  }\n}\nfunction SidebarComponent_app_suggested_for_you_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-suggested-for-you\");\n  }\n}\nfunction SidebarComponent_app_top_fashion_influencers_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-top-fashion-influencers\");\n  }\n}\nfunction SidebarComponent_app_shop_by_category_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-shop-by-category\");\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    // Component availability flags\n    this.isTrendingProductsAvailable = true;\n    this.isFeaturedBrandsAvailable = true;\n    this.isNewArrivalsAvailable = true;\n    this.isSuggestedForYouAvailable = true;\n    this.isTopFashionInfluencersAvailable = true;\n    this.isShopByCategoryAvailable = true;\n  }\n  ngOnInit() {\n    this.checkComponentAvailability();\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  checkComponentAvailability() {\n    // Check if components are properly imported and available\n    try {\n      this.isTrendingProductsAvailable = !!TrendingProductsComponent;\n    } catch (error) {\n      console.warn('TrendingProductsComponent not available:', error);\n      this.isTrendingProductsAvailable = false;\n    }\n    try {\n      this.isFeaturedBrandsAvailable = !!FeaturedBrandsComponent;\n    } catch (error) {\n      console.warn('FeaturedBrandsComponent not available:', error);\n      this.isFeaturedBrandsAvailable = false;\n    }\n    try {\n      this.isNewArrivalsAvailable = !!NewArrivalsComponent;\n    } catch (error) {\n      console.warn('NewArrivalsComponent not available:', error);\n      this.isNewArrivalsAvailable = false;\n    }\n    try {\n      this.isSuggestedForYouAvailable = !!SuggestedForYouComponent;\n    } catch (error) {\n      console.warn('SuggestedForYouComponent not available:', error);\n      this.isSuggestedForYouAvailable = false;\n    }\n    try {\n      this.isTopFashionInfluencersAvailable = !!TopFashionInfluencersComponent;\n    } catch (error) {\n      console.warn('TopFashionInfluencersComponent not available:', error);\n      this.isTopFashionInfluencersAvailable = false;\n    }\n    try {\n      this.isShopByCategoryAvailable = !!ShopByCategoryComponent;\n    } catch (error) {\n      console.warn('ShopByCategoryComponent not available:', error);\n      this.isShopByCategoryAvailable = false;\n    }\n    console.log('Sidebar component availability:', {\n      trending: this.isTrendingProductsAvailable,\n      featuredBrands: this.isFeaturedBrandsAvailable,\n      newArrivals: this.isNewArrivalsAvailable,\n      suggestedForYou: this.isSuggestedForYouAvailable,\n      topInfluencers: this.isTopFashionInfluencersAvailable,\n      shopByCategory: this.isShopByCategoryAvailable\n    });\n  }\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [{\n      id: '1',\n      username: 'fashionista_maya',\n      fullName: 'Maya Chen',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      followedBy: 'Followed by 12 others',\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_guru_alex',\n      fullName: 'Alex Rodriguez',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      followedBy: 'Followed by 8 others',\n      isFollowing: false\n    }];\n  }\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [{\n      id: '1',\n      username: 'fashion_queen',\n      fullName: 'Priya Sharma',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n      followersCount: 25000,\n      postsCount: 156,\n      engagement: 8.5,\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_maven',\n      fullName: 'Kavya Reddy',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n      followersCount: 18000,\n      postsCount: 89,\n      engagement: 12.3,\n      isFollowing: true\n    }];\n  }\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [{\n      id: '1',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n    }, {\n      id: '2',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n    }, {\n      id: '3',\n      name: 'Accessories',\n      slug: 'accessories',\n      image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n    }, {\n      id: '4',\n      name: 'Footwear',\n      slug: 'footwear',\n      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 6,\n      consts: [[1, \"sidebar\"], [4, \"ngIf\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵtemplate(1, SidebarComponent_app_trending_products_1_Template, 1, 0, \"app-trending-products\", 1)(2, SidebarComponent_app_featured_brands_2_Template, 1, 0, \"app-featured-brands\", 1)(3, SidebarComponent_app_new_arrivals_3_Template, 1, 0, \"app-new-arrivals\", 1)(4, SidebarComponent_app_suggested_for_you_4_Template, 1, 0, \"app-suggested-for-you\", 1)(5, SidebarComponent_app_top_fashion_influencers_5_Template, 1, 0, \"app-top-fashion-influencers\", 1)(6, SidebarComponent_app_shop_by_category_6_Template, 1, 0, \"app-shop-by-category\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isTrendingProductsAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeaturedBrandsAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isNewArrivalsAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSuggestedForYouAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isTopFashionInfluencersAvailable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isShopByCategoryAvailable);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, RouterModule, IonicModule, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent, ShopByCategoryComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\napp-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%], app-suggested-for-you[_ngcontent-%COMP%], app-top-fashion-influencers[_ngcontent-%COMP%], app-shop-by-category[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 16px;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container, app-featured-brands[_ngcontent-%COMP%]     .component-container, app-new-arrivals[_ngcontent-%COMP%]     .component-container, app-suggested-for-you[_ngcontent-%COMP%]     .component-container, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container, app-shop-by-category[_ngcontent-%COMP%]     .component-container {\\n  background: #ffffff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header {\\n  padding: 16px;\\n  border-bottom: 1px solid #efefef;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header h3, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header h3, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header h3, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header h3, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header h3, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header h3 {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all {\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  text-decoration: none;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover {\\n  text-decoration: underline;\\n}\\napp-trending-products[_ngcontent-%COMP%]     .component-container .component-content, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-content, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-content, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-content, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-content, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-content {\\n  padding: 16px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  background: #f8f9fa;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(232, 67, 147, 0.3);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  text-decoration: none;\\n  color: inherit;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 3px solid #f8f9fa;\\n  transition: transform 0.3s ease;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  text-align: center;\\n}\\n\\n@media (max-width: 768px) {\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n    font-size: 24px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .category-list[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin-bottom: 0;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL3NpZGViYXIvc2lkZWJhci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBaEI7RUFDRSxnQkFBQTtFQUNBLFNBQUE7RUFDQSx3QkFBQTtFQUFBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQUVGO0FBQ0U7RUFDRSxVQUFBO0FBQ0o7QUFFRTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFBSjtBQUdFO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQURKO0FBSUU7RUFDRSxtQkFBQTtBQUZKOztBQU9BOzs7Ozs7RUFNRSxXQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBSkY7QUFRSTs7Ozs7O0VBQ0UsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFETjtBQUdNOzs7Ozs7RUFDRSxhQUFBO0VBQ0EsZ0NBQUE7QUFJUjtBQUZROzs7Ozs7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQVNWO0FBTlE7Ozs7OztFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtBQWFWO0FBWFU7Ozs7OztFQUNFLDBCQUFBO0FBa0JaO0FBYk07Ozs7OztFQUNFLGFBQUE7QUFvQlI7O0FBYkE7RUFDRSxhQUFBO0VBQ0EsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBZ0JGO0FBYkU7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBZUo7QUFiSTtFQUNFLE9BQUE7QUFlTjtBQVpJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQWNOO0FBWk07RUFDRSxlQUFBO0VBQ0EsY0FBQTtBQWNSO0FBVkk7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7QUFZTjtBQVRJO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EsOENBQUE7QUFXTjtBQVRNO0VBQ0UsMkJBQUE7RUFDQSw4Q0FBQTtBQVdSO0FBUk07RUFDRSxlQUFBO0FBVVI7QUFMRTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFPSjtBQUxJO0VBQ0UsYUFBQTtFQUNBLGVBQUE7QUFPTjtBQUhFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQUtKO0FBSEk7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBS047QUFITTtFQUNFLDJCQUFBO0VBQ0EsMENBQUE7QUFLUjtBQUZNO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7QUFJUjtBQURNO0VBQ0UsT0FBQTtBQUdSO0FBRFE7RUFDRSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFHVjtBQUFRO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0FBRVY7QUFFTTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBQVI7QUFFUTtFQUNFLDJCQUFBO0VBQ0EsOENBQUE7QUFBVjs7QUFRQTtFQUNFLGFBQUE7RUFDQSw2REFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFMRjtBQU9FO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQUxKO0FBT0k7RUFDRSxhQUFBO0VBQ0EsZUFBQTtBQUxOO0FBU0U7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBUEo7QUFTSTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7QUFQTjtBQVNNO0VBQ0UsMkJBQUE7RUFDQSwwQ0FBQTtBQVBSO0FBVU07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtBQVJSO0FBV007RUFDRSxPQUFBO0FBVFI7QUFXUTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQVRWO0FBWVE7RUFDRSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFWVjtBQWFRO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtFQUNBLG1CQUFBO0FBWFY7QUFhVTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFBQSxrQkFBQTtFQUNBLGdCQUFBO0FBWFo7QUFlUTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBYlY7QUFlVTtFQUNFLDJCQUFBO0VBQ0EsOENBQUE7QUFiWjs7QUFzQkE7RUFDRSxhQUFBO0VBQ0EsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBbkJGO0FBcUJFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQW5CSjtBQXFCSTtFQUNFLGNBQUE7RUFDQSxlQUFBO0FBbkJOO0FBdUJFO0VBQ0UsYUFBQTtFQUNBLHFDQUFBO0VBQ0EsU0FBQTtBQXJCSjtBQXVCSTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBckJOO0FBdUJNO0VBQ0UsMkJBQUE7RUFDQSwwQ0FBQTtBQXJCUjtBQXdCTTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSwrQkFBQTtBQXRCUjtBQXlCTTtFQUNFLHFCQUFBO0FBdkJSO0FBMEJNO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBeEJSOztBQStCQTtFQUNFOzs7SUFHRSxhQUFBO0VBNUJGO0VBK0JBOzs7SUFHRSxlQUFBO0VBN0JGO0VBK0JFOzs7SUFDRSxlQUFBO0VBM0JKO0VBK0JBOztJQUVFLGFBQUE7SUFDQSxTQUFBO0VBN0JGO0VBK0JFOztJQUNFLFdBQUE7SUFDQSxZQUFBO0VBNUJKO0VBZ0NBOztJQUVFLGVBQUE7RUE5QkY7RUFpQ0E7SUFDRSwwQkFBQTtJQUNBLFNBQUE7RUEvQkY7RUFpQ0U7SUFDRSxtQkFBQTtJQUNBLGdCQUFBO0lBQ0EsYUFBQTtJQUNBLFNBQUE7RUEvQko7RUFpQ0k7SUFDRSxXQUFBO0lBQ0EsWUFBQTtJQUNBLGdCQUFBO0VBL0JOO0VBa0NJO0lBQ0UsZUFBQTtFQWhDTjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLnNpZGViYXIge1xuICBwb3NpdGlvbjogc3RpY2t5O1xuICB0b3A6IDgwcHg7XG4gIGhlaWdodDogZml0LWNvbnRlbnQ7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMjBweDtcbiAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDEwMHB4KTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgcGFkZGluZy1yaWdodDogOHB4O1xuXG4gIC8vIEN1c3RvbSBzY3JvbGxiYXIgZm9yIHdlYmtpdCBicm93c2Vyc1xuICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgd2lkdGg6IDZweDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICBiYWNrZ3JvdW5kOiAjZjFmMWYxO1xuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxO1xuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kOiAjYThhOGE4O1xuICB9XG59XG5cbi8vIFN0eWxpbmcgZm9yIHRoZSBuZXcgY29tcG9uZW50cyBpbiBzaWRlYmFyXG5hcHAtdHJlbmRpbmctcHJvZHVjdHMsXG5hcHAtZmVhdHVyZWQtYnJhbmRzLFxuYXBwLW5ldy1hcnJpdmFscyxcbmFwcC1zdWdnZXN0ZWQtZm9yLXlvdSxcbmFwcC10b3AtZmFzaGlvbi1pbmZsdWVuY2VycyxcbmFwcC1zaG9wLWJ5LWNhdGVnb3J5IHtcbiAgd2lkdGg6IDEwMCU7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuXG4gIC8vIEluc3RhZ3JhbS1saWtlIGNhcmQgc3R5bGluZ1xuICA6Om5nLWRlZXAge1xuICAgIC5jb21wb25lbnQtY29udGFpbmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGJkYmRiO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAgICAgLmNvbXBvbmVudC1oZWFkZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VmZWZlZjtcblxuICAgICAgICBoMyB7XG4gICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgICB9XG5cbiAgICAgICAgLnNlZS1hbGwge1xuICAgICAgICAgIGNvbG9yOiAjMDA5NWY2O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5jb21wb25lbnQtY29udGVudCB7XG4gICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIFN1Z2dlc3Rpb25zIHNlY3Rpb24gd2l0aCBUcmVuZGluZyBOb3cgc3R5bGluZ1xuLnN1Z2dlc3Rpb25zIHtcbiAgcGFkZGluZzogMjBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcblxuICAvLyBIZWFkZXIgU2VjdGlvblxuICAuc2VjdGlvbi1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcblxuICAgIC5oZWFkZXItY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgIH1cblxuICAgIC5zZWN0aW9uLXRpdGxlIHtcbiAgICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICBjb2xvcjogIzFhMWExYTtcbiAgICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEycHg7XG5cbiAgICAgIC50aXRsZS1pY29uIHtcbiAgICAgICAgZm9udC1zaXplOiAyOHB4O1xuICAgICAgICBjb2xvcjogIzZjNWNlNztcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuc2VjdGlvbi1zdWJ0aXRsZSB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbjogMDtcbiAgICB9XG5cbiAgICAudmlldy1hbGwtYnRuIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2YzVjZTcgMCUsICNhMjliZmUgMTAwJSk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBwYWRkaW5nOiAxMnB4IDIwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiAyNXB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDEwOCwgOTIsIDIzMSwgMC4zKTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgICAgYm94LXNoYWRvdzogMCA2cHggMjBweCByZ2JhKDEwOCwgOTIsIDIzMSwgMC40KTtcbiAgICAgIH1cblxuICAgICAgaW9uLWljb24ge1xuICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgaDMge1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICBmb250LXdlaWdodDogNzAwO1xuICAgIGNvbG9yOiAjMWExYTFhO1xuICAgIG1hcmdpbjogMCAwIDI0cHggMDtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxMnB4O1xuXG4gICAgJjo6YmVmb3JlIHtcbiAgICAgIGNvbnRlbnQ6ICfDsMKfwpLCoSc7XG4gICAgICBmb250LXNpemU6IDI4cHg7XG4gICAgfVxuICB9XG5cbiAgLnN1Z2dlc3Rpb24tbGlzdCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMTZweDtcblxuICAgIC5zdWdnZXN0aW9uLWl0ZW0ge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDE2cHg7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjEyKTtcbiAgICAgIH1cblxuICAgICAgaW1nIHtcbiAgICAgICAgd2lkdGg6IDYwcHg7XG4gICAgICAgIGhlaWdodDogNjBweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICAgICAgYm9yZGVyOiAzcHggc29saWQgI2Y4ZjlmYTtcbiAgICAgIH1cblxuICAgICAgLnN1Z2dlc3Rpb24taW5mbyB7XG4gICAgICAgIGZsZXg6IDE7XG5cbiAgICAgICAgaDUge1xuICAgICAgICAgIG1hcmdpbjogMCAwIDRweCAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAjMWExYTFhO1xuICAgICAgICB9XG5cbiAgICAgICAgcCB7XG4gICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICBjb2xvcjogIzY2NjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuZm9sbG93LWJ0biB7XG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2YzVjZTcgMCUsICNhMjliZmUgMTAwJSk7XG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDEwOCwgOTIsIDIzMSwgMC4zKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBJbmZsdWVuY2VycyBzZWN0aW9uIHdpdGggVHJlbmRpbmcgTm93IHN0eWxpbmdcbi5pbmZsdWVuY2VycyB7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNlOWVjZWYgMTAwJSk7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG5cbiAgaDMge1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICBmb250LXdlaWdodDogNzAwO1xuICAgIGNvbG9yOiAjMWExYTFhO1xuICAgIG1hcmdpbjogMCAwIDI0cHggMDtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxMnB4O1xuXG4gICAgJjo6YmVmb3JlIHtcbiAgICAgIGNvbnRlbnQ6ICfDsMKfwpHCkSc7XG4gICAgICBmb250LXNpemU6IDI4cHg7XG4gICAgfVxuICB9XG5cbiAgLmluZmx1ZW5jZXItbGlzdCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMjBweDtcblxuICAgIC5pbmZsdWVuY2VyLWl0ZW0ge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgZ2FwOiAxNnB4O1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICAgICAgICBib3gtc2hhZG93OiAwIDhweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xMik7XG4gICAgICB9XG5cbiAgICAgIGltZyB7XG4gICAgICAgIHdpZHRoOiA3MHB4O1xuICAgICAgICBoZWlnaHQ6IDcwcHg7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgICAgIGJvcmRlcjogM3B4IHNvbGlkICNmOGY5ZmE7XG4gICAgICB9XG5cbiAgICAgIC5pbmZsdWVuY2VyLWluZm8ge1xuICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgIGg1IHtcbiAgICAgICAgICBtYXJnaW46IDAgMCA2cHggMDtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjb2xvcjogIzFhMWExYTtcbiAgICAgICAgfVxuXG4gICAgICAgIHAge1xuICAgICAgICAgIG1hcmdpbjogMCAwIDEycHggMDtcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5pbmZsdWVuY2VyLXN0YXRzIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgZ2FwOiA2cHg7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcblxuICAgICAgICAgIHNwYW4ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgICAgICAgcGFkZGluZzogNHB4IDhweDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICAgICAgICB3aWR0aDogZml0LWNvbnRlbnQ7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5mb2xsb3ctYnRuIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmQ3OWE4IDAlLCAjZTg0MzkzIDEwMCUpO1xuICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgcGFkZGluZzogMTBweCAyMHB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDIzMiwgNjcsIDE0NywgMC4zKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gQ2F0ZWdvcmllcyBzZWN0aW9uIHdpdGggVHJlbmRpbmcgTm93IHN0eWxpbmdcbi5jYXRlZ29yaWVzIHtcbiAgcGFkZGluZzogMjBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcblxuICBoMyB7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgY29sb3I6ICMxYTFhMWE7XG4gICAgbWFyZ2luOiAwIDAgMjRweCAwO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDEycHg7XG5cbiAgICAmOjpiZWZvcmUge1xuICAgICAgY29udGVudDogJ8Owwp/Cm8KNw6/CuMKPJztcbiAgICAgIGZvbnQtc2l6ZTogMjhweDtcbiAgICB9XG4gIH1cblxuICAuY2F0ZWdvcnktbGlzdCB7XG4gICAgZGlzcGxheTogZ3JpZDtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpO1xuICAgIGdhcDogMTZweDtcblxuICAgIC5jYXRlZ29yeS1pdGVtIHtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgcGFkZGluZzogMjBweCAxNnB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgY29sb3I6IGluaGVyaXQ7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjEyKTtcbiAgICAgIH1cblxuICAgICAgaW1nIHtcbiAgICAgICAgd2lkdGg6IDcwcHg7XG4gICAgICAgIGhlaWdodDogNzBweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDtcbiAgICAgICAgYm9yZGVyOiAzcHggc29saWQgI2Y4ZjlmYTtcbiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZTtcbiAgICAgIH1cblxuICAgICAgJjpob3ZlciBpbWcge1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgICB9XG5cbiAgICAgIHNwYW4ge1xuICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiAjMWExYTFhO1xuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIFJlc3BvbnNpdmUgRGVzaWduXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnN1Z2dlc3Rpb25zLFxuICAuaW5mbHVlbmNlcnMsXG4gIC5jYXRlZ29yaWVzIHtcbiAgICBwYWRkaW5nOiAxNnB4O1xuICB9XG5cbiAgLnN1Z2dlc3Rpb25zIGgzLFxuICAuaW5mbHVlbmNlcnMgaDMsXG4gIC5jYXRlZ29yaWVzIGgzIHtcbiAgICBmb250LXNpemU6IDIwcHg7XG5cbiAgICAmOjpiZWZvcmUge1xuICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgIH1cbiAgfVxuXG4gIC5zdWdnZXN0aW9uLWl0ZW0sXG4gIC5pbmZsdWVuY2VyLWl0ZW0ge1xuICAgIHBhZGRpbmc6IDEycHg7XG4gICAgZ2FwOiAxMnB4O1xuXG4gICAgaW1nIHtcbiAgICAgIHdpZHRoOiA1MHB4O1xuICAgICAgaGVpZ2h0OiA1MHB4O1xuICAgIH1cbiAgfVxuXG4gIC5zdWdnZXN0aW9uLWluZm8gaDUsXG4gIC5pbmZsdWVuY2VyLWluZm8gaDUge1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgfVxuXG4gIC5jYXRlZ29yeS1saXN0IHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICBnYXA6IDEycHg7XG5cbiAgICAuY2F0ZWdvcnktaXRlbSB7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgICAgdGV4dC1hbGlnbjogbGVmdDtcbiAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICBnYXA6IDEycHg7XG5cbiAgICAgIGltZyB7XG4gICAgICAgIHdpZHRoOiA1MHB4O1xuICAgICAgICBoZWlnaHQ6IDUwcHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgICB9XG5cbiAgICAgIHNwYW4ge1xuICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "ShopByCategoryComponent", "i0", "ɵɵelement", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "topInfluencers", "categories", "isTrendingProductsAvailable", "isFeaturedBrandsAvailable", "isNewArrivalsAvailable", "isSuggestedForYouAvailable", "isTopFashionInfluencersAvailable", "isShopByCategoryAvailable", "ngOnInit", "checkComponentAvailability", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "error", "console", "warn", "log", "trending", "featuredB<PERSON>s", "newArrivals", "suggested<PERSON>or<PERSON><PERSON>", "shopByCategory", "id", "username", "fullName", "avatar", "<PERSON><PERSON><PERSON>", "isFollowing", "followersCount", "postsCount", "engagement", "name", "slug", "image", "formatFollowerCount", "count", "toFixed", "toString", "followUser", "userId", "user", "find", "u", "followInfluencer", "influencerId", "influencer", "i", "quickBuy", "productId", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "SidebarComponent_app_trending_products_1_Template", "SidebarComponent_app_featured_brands_2_Template", "SidebarComponent_app_new_arrivals_3_Template", "SidebarComponent_app_suggested_for_you_4_Template", "SidebarComponent_app_top_fashion_influencers_5_Template", "SidebarComponent_app_shop_by_category_6_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i3", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../shop-by-category/shop-by-category.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent,\n    ShopByCategoryComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n\n  // Component availability flags\n  isTrendingProductsAvailable = true;\n  isFeaturedBrandsAvailable = true;\n  isNewArrivalsAvailable = true;\n  isSuggestedForYouAvailable = true;\n  isTopFashionInfluencersAvailable = true;\n  isShopByCategoryAvailable = true;\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.checkComponentAvailability();\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  private checkComponentAvailability() {\n    // Check if components are properly imported and available\n    try {\n      this.isTrendingProductsAvailable = !!TrendingProductsComponent;\n    } catch (error) {\n      console.warn('TrendingProductsComponent not available:', error);\n      this.isTrendingProductsAvailable = false;\n    }\n\n    try {\n      this.isFeaturedBrandsAvailable = !!FeaturedBrandsComponent;\n    } catch (error) {\n      console.warn('FeaturedBrandsComponent not available:', error);\n      this.isFeaturedBrandsAvailable = false;\n    }\n\n    try {\n      this.isNewArrivalsAvailable = !!NewArrivalsComponent;\n    } catch (error) {\n      console.warn('NewArrivalsComponent not available:', error);\n      this.isNewArrivalsAvailable = false;\n    }\n\n    try {\n      this.isSuggestedForYouAvailable = !!SuggestedForYouComponent;\n    } catch (error) {\n      console.warn('SuggestedForYouComponent not available:', error);\n      this.isSuggestedForYouAvailable = false;\n    }\n\n    try {\n      this.isTopFashionInfluencersAvailable = !!TopFashionInfluencersComponent;\n    } catch (error) {\n      console.warn('TopFashionInfluencersComponent not available:', error);\n      this.isTopFashionInfluencersAvailable = false;\n    }\n\n    try {\n      this.isShopByCategoryAvailable = !!ShopByCategoryComponent;\n    } catch (error) {\n      console.warn('ShopByCategoryComponent not available:', error);\n      this.isShopByCategoryAvailable = false;\n    }\n\n    console.log('Sidebar component availability:', {\n      trending: this.isTrendingProductsAvailable,\n      featuredBrands: this.isFeaturedBrandsAvailable,\n      newArrivals: this.isNewArrivalsAvailable,\n      suggestedForYou: this.isSuggestedForYouAvailable,\n      topInfluencers: this.isTopFashionInfluencersAvailable,\n      shopByCategory: this.isShopByCategoryAvailable\n    });\n  }\n\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [\n      {\n        id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followedBy: 'Followed by 12 others',\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followedBy: 'Followed by 8 others',\n        isFollowing: false\n      }\n    ];\n  }\n\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [\n      {\n        id: '1',\n        username: 'fashion_queen',\n        fullName: 'Priya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        followersCount: 25000,\n        postsCount: 156,\n        engagement: 8.5,\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_maven',\n        fullName: 'Kavya Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 18000,\n        postsCount: 89,\n        engagement: 12.3,\n        isFollowing: true\n      }\n    ];\n  }\n\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [\n      {\n        id: '1',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n      },\n      {\n        id: '2',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n      },\n      {\n        id: '3',\n        name: 'Accessories',\n        slug: 'accessories',\n        image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n      },\n      {\n        id: '4',\n        name: 'Footwear',\n        slug: 'footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Trending Products Section -->\n  <app-trending-products *ngIf=\"isTrendingProductsAvailable\"></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands *ngIf=\"isFeaturedBrandsAvailable\"></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals *ngIf=\"isNewArrivalsAvailable\"></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <app-suggested-for-you *ngIf=\"isSuggestedForYouAvailable\"></app-suggested-for-you>\n\n  <!-- Top Fashion Influencers -->\n  <app-top-fashion-influencers *ngIf=\"isTopFashionInfluencersAvailable\"></app-top-fashion-influencers>\n\n  <!-- Shop by Category -->\n  <app-shop-by-category *ngIf=\"isShopByCategoryAvailable\"></app-shop-by-category>\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,8BAA8B,QAAQ,8DAA8D;AAC7G,SAASC,uBAAuB,QAAQ,gDAAgD;;;;;;;ICVtFC,EAAA,CAAAC,SAAA,4BAAmF;;;;;IAGnFD,EAAA,CAAAC,SAAA,0BAA6E;;;;;IAG7ED,EAAA,CAAAC,SAAA,uBAAoE;;;;;IAGpED,EAAA,CAAAC,SAAA,4BAAkF;;;;;IAGlFD,EAAA,CAAAC,SAAA,kCAAoG;;;;;IAGpGD,EAAA,CAAAC,SAAA,2BAA+E;;;ADcjF,OAAM,MAAOC,gBAAgB;EAc3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAfhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,UAAU,GAAU,EAAE;IAEtB;IACA,KAAAC,2BAA2B,GAAG,IAAI;IAClC,KAAAC,yBAAyB,GAAG,IAAI;IAChC,KAAAC,sBAAsB,GAAG,IAAI;IAC7B,KAAAC,0BAA0B,GAAG,IAAI;IACjC,KAAAC,gCAAgC,GAAG,IAAI;IACvC,KAAAC,yBAAyB,GAAG,IAAI;EAK7B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQJ,0BAA0BA,CAAA;IAChC;IACA,IAAI;MACF,IAAI,CAACP,2BAA2B,GAAG,CAAC,CAAChB,yBAAyB;KAC/D,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;MAC/D,IAAI,CAACZ,2BAA2B,GAAG,KAAK;;IAG1C,IAAI;MACF,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAChB,uBAAuB;KAC3D,CAAC,OAAO2B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAEF,KAAK,CAAC;MAC7D,IAAI,CAACX,yBAAyB,GAAG,KAAK;;IAGxC,IAAI;MACF,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAChB,oBAAoB;KACrD,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,KAAK,CAAC;MAC1D,IAAI,CAACV,sBAAsB,GAAG,KAAK;;IAGrC,IAAI;MACF,IAAI,CAACC,0BAA0B,GAAG,CAAC,CAAChB,wBAAwB;KAC7D,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,KAAK,CAAC;MAC9D,IAAI,CAACT,0BAA0B,GAAG,KAAK;;IAGzC,IAAI;MACF,IAAI,CAACC,gCAAgC,GAAG,CAAC,CAAChB,8BAA8B;KACzE,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,+CAA+C,EAAEF,KAAK,CAAC;MACpE,IAAI,CAACR,gCAAgC,GAAG,KAAK;;IAG/C,IAAI;MACF,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAChB,uBAAuB;KAC3D,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAEF,KAAK,CAAC;MAC7D,IAAI,CAACP,yBAAyB,GAAG,KAAK;;IAGxCQ,OAAO,CAACE,GAAG,CAAC,iCAAiC,EAAE;MAC7CC,QAAQ,EAAE,IAAI,CAAChB,2BAA2B;MAC1CiB,cAAc,EAAE,IAAI,CAAChB,yBAAyB;MAC9CiB,WAAW,EAAE,IAAI,CAAChB,sBAAsB;MACxCiB,eAAe,EAAE,IAAI,CAAChB,0BAA0B;MAChDL,cAAc,EAAE,IAAI,CAACM,gCAAgC;MACrDgB,cAAc,EAAE,IAAI,CAACf;KACtB,CAAC;EACJ;EAEAG,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACZ,cAAc,GAAG,CACpB;MACEyB,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,kBAAkB;MAC5BC,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,oEAAoE;MAC5EC,UAAU,EAAE,uBAAuB;MACnCC,WAAW,EAAE;KACd,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,iBAAiB;MAC3BC,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE,oEAAoE;MAC5EC,UAAU,EAAE,sBAAsB;MAClCC,WAAW,EAAE;KACd,CACF;EACH;EAEAjB,oBAAoBA,CAAA;IAClB,IAAI,CAACZ,gBAAgB,GAAG,EAAE;EAC5B;EAEAa,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACZ,cAAc,GAAG,CACpB;MACEuB,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE,cAAc;MACxBC,MAAM,EAAE,oEAAoE;MAC5EG,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfH,WAAW,EAAE;KACd,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE,aAAa;MACvBC,MAAM,EAAE,iEAAiE;MACzEG,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,IAAI;MAChBH,WAAW,EAAE;KACd,CACF;EACH;EAEAf,cAAcA,CAAA;IACZ;IACA,IAAI,CAACZ,UAAU,GAAG,CAChB;MACEsB,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;EACH;EAEAC,mBAAmBA,CAACC,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAC,UAAUA,CAACC,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAAC3C,cAAc,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKiB,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACb,WAAW,GAAG,CAACa,IAAI,CAACb,WAAW;;EAExC;EAEAgB,gBAAgBA,CAACC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC9C,cAAc,CAAC0C,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACxB,EAAE,KAAKsB,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAClB,WAAW,GAAG,CAACkB,UAAU,CAAClB,WAAW;;EAEpD;EAEAoB,QAAQA,CAACC,SAAiB;IACxBlC,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEgC,SAAS,CAAC;IAC5C;EACF;EAEAC,cAAcA,CAACC,YAAoB;IACjCpC,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEkC,YAAY,CAAC;IAC7C,IAAI,CAACtD,MAAM,CAACuD,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBAhMWzD,gBAAgB,EAAAF,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhB/D,gBAAgB;MAAAgE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApE,EAAA,CAAAqE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/B7B3E,EAAA,CAAA6E,cAAA,eAAuB;UAiBrB7E,EAfA,CAAA8E,UAAA,IAAAC,iDAAA,mCAA2D,IAAAC,+CAAA,iCAGJ,IAAAC,4CAAA,8BAGN,IAAAC,iDAAA,mCAGS,IAAAC,uDAAA,yCAGY,IAAAC,gDAAA,kCAGd;UAC1DpF,EAAA,CAAAqF,YAAA,EAAQ;;;UAhBkBrF,EAAA,CAAAsF,SAAA,EAAiC;UAAjCtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAAlE,2BAAA,CAAiC;UAGnCV,EAAA,CAAAsF,SAAA,EAA+B;UAA/BtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAAjE,yBAAA,CAA+B;UAGlCX,EAAA,CAAAsF,SAAA,EAA4B;UAA5BtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAAhE,sBAAA,CAA4B;UAGvBZ,EAAA,CAAAsF,SAAA,EAAgC;UAAhCtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAA/D,0BAAA,CAAgC;UAG1Bb,EAAA,CAAAsF,SAAA,EAAsC;UAAtCtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAA9D,gCAAA,CAAsC;UAG7Cd,EAAA,CAAAsF,SAAA,EAA+B;UAA/BtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAA7D,yBAAA,CAA+B;;;qBDCpDxB,YAAY,EAAAiG,EAAA,CAAAC,IAAA,EACZjG,YAAY,EACZC,WAAW,EACXC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B,EAC9BC,uBAAuB;MAAA2F,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}