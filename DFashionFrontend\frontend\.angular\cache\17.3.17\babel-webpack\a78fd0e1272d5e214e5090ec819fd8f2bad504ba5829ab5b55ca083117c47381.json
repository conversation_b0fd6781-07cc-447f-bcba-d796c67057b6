{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/cart.service\";\nimport * as i3 from \"../../../../core/services/wishlist.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3];\nfunction FeedComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11);\n    i0.ɵɵelement(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 14)(7, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, FeedComponent_div_1_div_1_Template, 8, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeedComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"p\");\n    i0.ɵɵtext(2, \"No posts available. Loading posts...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_2_article_2_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r2.location);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"img\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.content);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Reel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_2_article_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"video\", 56);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_13_Template_video_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleVideoPlay($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 57)(3, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_13_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleVideoPlay($event));\n    });\n    i0.ɵɵelement(4, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, FeedComponent_div_2_article_2_div_13_div_5_Template, 4, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"muted\", true)(\"loop\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", post_r2.isReel);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_14_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_14_button_1_Template_button_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.showProductDetails(product_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedComponent_div_2_article_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_article_2_div_14_button_1_Template, 2, 0, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatLikesCount(post_r2.likes));\n  }\n}\nfunction FeedComponent_div_2_article_2_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"#\", hashtag_r7, \"\");\n  }\n}\nfunction FeedComponent_div_2_article_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_article_2_div_31_span_1_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.hashtags);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleComments(post_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r2.comments, \" comments \");\n  }\n}\nfunction FeedComponent_div_2_article_2_div_40_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"img\", 79);\n    i0.ɵɵelementStart(3, \"div\", 80)(4, \"span\", 81);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 82);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 83)(9, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_9_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.viewProduct(product_r10));\n    });\n    i0.ɵɵtext(10, \"Shop\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_11_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.addToCart(product_r10));\n    });\n    i0.ɵɵelement(12, \"i\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_13_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.addToWishlist(product_r10));\n    });\n    i0.ɵɵelement(14, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_15_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.buyNow(product_r10));\n    });\n    i0.ɵɵelement(16, \"i\", 90);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r10.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatPrice(product_r10.price));\n  }\n}\nfunction FeedComponent_div_2_article_2_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵtemplate(2, FeedComponent_div_2_article_2_div_40_div_2_Template, 17, 4, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products.slice(0, 3));\n  }\n}\nfunction FeedComponent_div_2_article_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 20)(1, \"header\", 21)(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵelement(4, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 25)(6, \"h3\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FeedComponent_div_2_article_2_span_8_Template, 2, 1, \"span\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 30);\n    i0.ɵɵtemplate(12, FeedComponent_div_2_article_2_div_12_Template, 2, 2, \"div\", 31)(13, FeedComponent_div_2_article_2_div_13_Template, 6, 4, \"div\", 32)(14, FeedComponent_div_2_article_2_div_14_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 34)(16, \"div\", 35)(17, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_17_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleLike(post_r2));\n    });\n    i0.ɵɵelement(18, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_19_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.focusCommentInput(post_r2));\n    });\n    i0.ɵɵelement(20, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_21_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.sharePost(post_r2));\n    });\n    i0.ɵɵelement(22, \"i\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_23_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSave(post_r2));\n    });\n    i0.ɵɵelement(24, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, FeedComponent_div_2_article_2_div_25_Template, 3, 1, \"div\", 42);\n    i0.ɵɵelementStart(26, \"div\", 43)(27, \"span\", 26);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 44);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, FeedComponent_div_2_article_2_div_31_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, FeedComponent_div_2_article_2_div_32_Template, 3, 1, \"div\", 46);\n    i0.ɵɵelementStart(33, \"div\", 47);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 48)(36, \"input\", 49, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FeedComponent_div_2_article_2_Template_input_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.newComment, $event) || (ctx_r3.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function FeedComponent_div_2_article_2_Template_input_keyup_enter_36_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_38_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵtext(39, \" Post \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, FeedComponent_div_2_article_2_div_40_Template, 3, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (post_r2.user == null ? null : post_r2.user.avatar) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (post_r2.user == null ? null : post_r2.user.fullName) || \"User\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((post_r2.user == null ? null : post_r2.user.username) || \"Unknown User\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", post_r2.mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r2.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r2.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((post_r2.user == null ? null : post_r2.user.username) || \"Unknown User\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r2.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.hashtags && post_r2.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.comments > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getTimeAgo(post_r2.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.newComment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newComment || !ctx_r3.newComment.trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n  }\n}\nfunction FeedComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_div_1_Template, 3, 0, \"div\", 17)(2, FeedComponent_div_2_article_2_Template, 41, 24, \"article\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.posts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.posts)(\"ngForTrackBy\", ctx_r3.trackByPostId);\n  }\n}\nfunction FeedComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadMorePosts());\n    });\n    i0.ɵɵtext(2, \" Load More Posts \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94);\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Welcome to DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Follow fashion influencers to see their latest posts and discover trending styles!\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class FeedComponent {\n  constructor(router, cartService, wishlistService) {\n    this.router = router;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.posts = [];\n    this.loading = true;\n    this.hasMore = true;\n    this.currentPage = 1;\n    this.newComment = '';\n  }\n  ngOnInit() {\n    this.loadPosts();\n  }\n  loadPosts() {\n    this.loading = true;\n    // Simulate loading with realistic Instagram-style posts\n    setTimeout(() => {\n      this.posts = this.getFallbackPosts();\n      this.loading = false;\n    }, 1000);\n  }\n  getFallbackPosts() {\n    return [{\n      _id: 'post-1',\n      user: {\n        _id: 'user-1',\n        username: 'ai_fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'assets/images/default-avatar.svg'\n      },\n      content: 'Sustainable fashion is the future! 🌱✨ This eco-friendly dress is made from recycled materials and looks absolutely stunning. #SustainableFashion #EcoFriendly #OOTD',\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      location: 'Mumbai, India',\n      likes: 1247,\n      comments: 89,\n      shares: 34,\n      isLiked: false,\n      isSaved: false,\n      isReel: false,\n      hashtags: ['SustainableFashion', 'EcoFriendly', 'OOTD'],\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      products: [{\n        _id: 'prod-1',\n        name: 'Eco-Friendly Summer Dress',\n        price: 2499,\n        image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop'\n      }]\n    }, {\n      _id: 'post-3',\n      user: {\n        _id: 'user-3',\n        username: 'ai_trendsetter_zara',\n        fullName: 'Zara Patel',\n        avatar: 'assets/images/default-avatar.svg'\n      },\n      content: 'Ethnic fusion at its finest! Traditional meets modern ✨ This kurti is perfect for any occasion.',\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      location: 'Bangalore, India',\n      likes: 2156,\n      comments: 134,\n      shares: 67,\n      isLiked: false,\n      isSaved: true,\n      isReel: false,\n      hashtags: ['EthnicWear', 'Fusion', 'Traditional'],\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      products: [{\n        _id: 'prod-3',\n        name: 'Designer Ethnic Kurti',\n        price: 1899,\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop'\n      }]\n    }];\n  }\n  loadMorePosts() {\n    this.currentPage++;\n    this.loadPosts();\n  }\n  trackByPostId(index, post) {\n    return post._id;\n  }\n  // Instagram-style Actions\n  toggleLike(post) {\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n  }\n  toggleSave(post) {\n    post.isSaved = !post.isSaved;\n  }\n  toggleComments(post) {\n    // Navigate to post detail or show comments modal\n    console.log('Toggle comments for post:', post._id);\n  }\n  sharePost(post) {\n    // Implement share functionality\n    console.log('Share post:', post._id);\n  }\n  addComment(post) {\n    if (this.newComment.trim()) {\n      post.comments += 1;\n      console.log('Add comment:', this.newComment, 'to post:', post._id);\n      this.newComment = '';\n    }\n  }\n  focusCommentInput(post) {\n    // Focus on comment input\n    console.log('Focus comment input for post:', post._id);\n  }\n  toggleVideoPlay(event) {\n    const video = event.target;\n    if (video.paused) {\n      video.play();\n    } else {\n      video.pause();\n    }\n  }\n  showProductDetails(product) {\n    console.log('Show product details:', product);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  formatLikesCount(likes) {\n    if (likes === 1) return '1 like';\n    if (likes < 1000) return `${likes} likes`;\n    if (likes < 1000000) return `${(likes / 1000).toFixed(1)}K likes`;\n    return `${(likes / 1000000).toFixed(1)}M likes`;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks}w`;\n  }\n  // E-commerce Actions\n  addToCart(product) {\n    console.log('Adding to cart:', product);\n    this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n      next: response => {\n        if (response.success) {\n          alert('Product added to cart!');\n        } else {\n          alert('Failed to add product to cart');\n        }\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n        alert('Error adding product to cart');\n      }\n    });\n  }\n  addToWishlist(product) {\n    console.log('Adding to wishlist:', product);\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: response => {\n        if (response.success) {\n          alert('Product added to wishlist!');\n        } else {\n          alert('Failed to add product to wishlist');\n        }\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n        alert('Error adding product to wishlist');\n      }\n    });\n  }\n  buyNow(product) {\n    console.log('Buying product:', product);\n    this.router.navigate(['/checkout'], {\n      queryParams: {\n        productId: product._id,\n        source: 'feed'\n      }\n    });\n  }\n  static {\n    this.ɵfac = function FeedComponent_Factory(t) {\n      return new (t || FeedComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeedComponent,\n      selectors: [[\"app-feed\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"commentInput\", \"\"], [1, \"instagram-feed\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"feed-posts\", 4, \"ngIf\"], [\"class\", \"load-more\", 4, \"ngIf\"], [\"class\", \"empty-feed\", 4, \"ngIf\"], [1, \"loading-container\"], [\"class\", \"post-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"post-skeleton\"], [1, \"skeleton-header\"], [1, \"skeleton-avatar\"], [1, \"skeleton-user-info\"], [1, \"skeleton-username\"], [1, \"skeleton-time\"], [1, \"skeleton-image\"], [1, \"skeleton-actions\"], [1, \"feed-posts\"], [\"class\", \"no-posts-message\", 4, \"ngIf\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"no-posts-message\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar-container\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"post-location\", 4, \"ngIf\"], [1, \"post-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media-container\"], [\"class\", \"post-image-container\", 4, \"ngIf\"], [\"class\", \"post-video-container\", 4, \"ngIf\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [\"class\", \"ecommerce-section\", 4, \"ngIf\"], [1, \"post-location\"], [1, \"post-image-container\"], [1, \"post-image\", 3, \"src\", \"alt\"], [1, \"post-video-container\"], [1, \"post-video\", 3, \"click\", \"src\", \"muted\", \"loop\"], [1, \"video-overlay\"], [1, \"play-pause-btn\", 3, \"click\"], [1, \"fas\", \"fa-play\"], [\"class\", \"reel-indicator\", 4, \"ngIf\"], [1, \"reel-indicator\"], [1, \"fas\", \"fa-video\"], [1, \"product-tags-overlay\"], [\"class\", \"product-tag-btn\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"ecommerce-section\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-product\"], [1, \"product-header\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"product-actions\"], [1, \"shop-btn\", 3, \"click\"], [1, \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"load-more\"], [1, \"load-more-btn\", 3, \"click\"], [1, \"empty-feed\"], [1, \"empty-content\"], [1, \"fas\", \"fa-camera\"]],\n      template: function FeedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, FeedComponent_div_1_Template, 2, 2, \"div\", 2)(2, FeedComponent_div_2_Template, 3, 3, \"div\", 3)(3, FeedComponent_div_3_Template, 3, 0, \"div\", 4)(4, FeedComponent_div_4_Template, 7, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasMore && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.posts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".instagram-feed[_ngcontent-%COMP%] {\\n  max-width: 470px;\\n  margin: 0 auto;\\n  background: #fafafa;\\n  min-height: 100vh;\\n}\\n@media (max-width: 768px) {\\n  .instagram-feed[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    background: #ffffff;\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.no-posts-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #666;\\n  background: white;\\n  border-radius: 8px;\\n  margin: 20px 0;\\n}\\n.no-posts-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n}\\n\\n.post-skeleton[_ngcontent-%COMP%] {\\n  background: white;\\n  margin-bottom: 12px;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.skeleton-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  gap: 12px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.skeleton-username[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n  border-radius: 4px;\\n  margin-bottom: 6px;\\n}\\n\\n.skeleton-time[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 10px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n  border-radius: 4px;\\n}\\n\\n.skeleton-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 400px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-actions[_ngcontent-%COMP%] {\\n  height: 60px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.feed-posts[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.instagram-post[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: visible;\\n}\\n@media (max-width: 768px) {\\n  .instagram-post[_ngcontent-%COMP%] {\\n    overflow: visible;\\n    min-height: auto;\\n  }\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0;\\n  line-height: 1.2;\\n}\\n\\n.post-location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  line-height: 1.2;\\n}\\n\\n.post-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n.post-options[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.post-media-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-height: 600px;\\n  overflow: hidden;\\n}\\n\\n.post-image-container[_ngcontent-%COMP%], .post-video-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n}\\n\\n.post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  display: block;\\n}\\n\\n.post-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  display: block;\\n  max-height: 600px;\\n  object-fit: cover;\\n}\\n\\n.video-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.post-video-container[_ngcontent-%COMP%]:hover   .video-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.play-pause-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  color: white;\\n  font-size: 20px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.reel-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  left: 16px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.product-tags-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.7);\\n  border: 2px solid white;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 16px;\\n  transition: all 0.3s ease;\\n}\\n.product-tag-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(0, 0, 0, 0.9);\\n}\\n\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 16px;\\n}\\n\\n.primary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 24px;\\n  color: #262626;\\n  transition: all 0.2s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n  animation: _ngcontent-%COMP%_likeAnimation 0.3s ease;\\n}\\n.action-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n@keyframes _ngcontent-%COMP%_likeAnimation {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.likes-section[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n\\n.likes-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.hashtags[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n\\n.hashtag[_ngcontent-%COMP%] {\\n  color: #00376b;\\n  margin-right: 8px;\\n  cursor: pointer;\\n}\\n.hashtag[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.comments-preview[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n\\n.view-comments-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n  cursor: pointer;\\n  padding: 0;\\n}\\n.view-comments-btn[_ngcontent-%COMP%]:hover {\\n  color: #262626;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  text-transform: uppercase;\\n  letter-spacing: 0.2px;\\n}\\n\\n.add-comment-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  border-top: 1px solid #efefef;\\n  gap: 12px;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.comment-input[_ngcontent-%COMP%]::placeholder {\\n  color: #8e8e8e;\\n}\\n\\n.post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.post-comment-btn[_ngcontent-%COMP%]:disabled {\\n  color: #b2dffc;\\n  cursor: not-allowed;\\n}\\n.post-comment-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  color: #00376b;\\n}\\n\\n.ecommerce-section[_ngcontent-%COMP%] {\\n  border-top: 1px solid #efefef;\\n  padding: 16px;\\n  background: #fafafa;\\n  margin-top: 8px;\\n  position: relative;\\n  z-index: 1;\\n  overflow: visible;\\n}\\n@media (max-width: 768px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px 16px 16px;\\n    background: #ffffff;\\n    border-top: 1px solid #efefef;\\n    margin-top: 0;\\n  }\\n}\\n\\n.product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n@media (max-width: 768px) {\\n  .product-showcase[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .product-showcase[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-showcase[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n}\\n\\n.featured-product[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border: 1px solid #efefef;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  background: white;\\n  overflow: visible;\\n  position: relative;\\n}\\n.featured-product[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .featured-product[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    gap: 10px;\\n    border-radius: 6px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .featured-product[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 8px;\\n  }\\n}\\n\\n.product-header[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 425px) {\\n  .product-header[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    width: 100%;\\n    padding-bottom: 5px;\\n    border-bottom: 1px solid #f0f0f0;\\n    margin-bottom: 0;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .product-header[_ngcontent-%COMP%] {\\n    gap: 6px;\\n    padding-bottom: 4px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-header[_ngcontent-%COMP%] {\\n    gap: 5px;\\n    padding-bottom: 3px;\\n  }\\n}\\n\\n.product-thumbnail[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n  flex-shrink: 0;\\n}\\n@media (max-width: 425px) {\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    border-radius: 6px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n  min-width: 0;\\n}\\n@media (max-width: 425px) {\\n  .product-details[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  line-height: 1.3;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n@media (max-width: 425px) {\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #8e8e8e;\\n  font-weight: 500;\\n}\\n@media (max-width: 425px) {\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  justify-content: flex-start;\\n  width: 100%;\\n  padding: 8px 0 0 0;\\n  margin-top: 8px;\\n  overflow: visible;\\n  position: relative;\\n  z-index: 2;\\n}\\n@media (max-width: 768px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-top: 6px;\\n    padding: 6px 0 0 0;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 6px;\\n    margin-top: 5px;\\n    padding: 5px 0 0 0;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 5px;\\n    margin-top: 4px;\\n    padding: 4px 0 0 0;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 4px;\\n    margin-top: 3px;\\n    padding: 3px 0 0 0;\\n  }\\n}\\n\\n.shop-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n  min-width: 80px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.shop-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.shop-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\\n}\\n.shop-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.shop-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n@media (max-width: 425px) {\\n  .shop-btn[_ngcontent-%COMP%] {\\n    min-width: 70px;\\n    height: 32px;\\n    font-size: 11px;\\n    padding: 6px 12px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .shop-btn[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n    height: 30px;\\n    font-size: 10px;\\n    padding: 5px 10px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .shop-btn[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n    height: 28px;\\n    font-size: 9px;\\n    padding: 4px 8px;\\n  }\\n}\\n\\n.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  color: white;\\n  border: none;\\n  padding: 0;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);\\n  display: flex !important;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  flex-shrink: 0;\\n}\\n.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  transition: transform 0.3s ease;\\n}\\n.cart-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);\\n}\\n.cart-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.cart-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.cart-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) scale(1);\\n}\\n@media (max-width: 425px) {\\n  .cart-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .cart-btn[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .cart-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\\n  color: #e91e63;\\n  border: none;\\n  padding: 0;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);\\n  display: flex !important;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  flex-shrink: 0;\\n}\\n.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  transition: transform 0.3s ease;\\n}\\n.wishlist-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #fecfef 0%, #ff9a9e 100%);\\n  color: #c2185b;\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);\\n}\\n.wishlist-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.wishlist-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.wishlist-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) scale(1);\\n}\\n.wishlist-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);\\n  color: white;\\n}\\n.wishlist-btn.active[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #c2185b 0%, #e91e63 100%);\\n  color: white;\\n}\\n@media (max-width: 425px) {\\n  .wishlist-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .wishlist-btn[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .wishlist-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.buy-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n  color: white;\\n  border: none;\\n  padding: 0;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);\\n  display: flex !important;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  flex-shrink: 0;\\n}\\n.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  transition: transform 0.3s ease;\\n}\\n.buy-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.buy-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);\\n}\\n.buy-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.buy-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.buy-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) scale(1);\\n}\\n@media (max-width: 425px) {\\n  .buy-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .buy-btn[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .buy-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px 20px 16px;\\n    background: #ffffff;\\n    border-top: 1px solid #efefef;\\n    margin-bottom: 8px;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 10px 0 15px 0;\\n    overflow: visible;\\n    position: relative;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 14px;\\n    font-size: 12px;\\n    border-radius: 18px;\\n    min-width: 60px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    padding: 8px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 12px 8px 25px 8px;\\n    margin-bottom: 15px;\\n    background: #f8f9fa;\\n    border-top: 2px solid #dee2e6;\\n  }\\n  .featured-product[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    padding: 12px 8px;\\n    gap: 8px;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 8px;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    gap: 8px;\\n    flex-wrap: wrap;\\n    padding: 10px 0 15px 0;\\n    justify-content: space-between;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    border-radius: 16px;\\n    min-width: 70px;\\n    max-width: 120px;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    padding: 8px;\\n    flex-shrink: 0;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 425px) {\\n  .instagram-post[_ngcontent-%COMP%] {\\n    margin-bottom: 12px !important;\\n    border-radius: 0;\\n    overflow: visible !important;\\n    position: relative;\\n  }\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 16px 10px 35px 10px !important;\\n    margin-bottom: 20px !important;\\n    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\\n    border: 2px solid #dee2e6 !important;\\n    border-radius: 12px !important;\\n    position: relative !important;\\n    z-index: 100 !important;\\n    min-height: 140px !important;\\n    overflow: visible !important;\\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1) !important;\\n  }\\n  .product-showcase[_ngcontent-%COMP%] {\\n    gap: 12px !important;\\n    overflow: visible !important;\\n  }\\n  .featured-product[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    flex-direction: row !important;\\n    align-items: center !important;\\n    padding: 12px !important;\\n    background: white !important;\\n    border: 1px solid #e9ecef !important;\\n    border-radius: 10px !important;\\n    margin-bottom: 10px !important;\\n    overflow: visible !important;\\n    position: relative !important;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;\\n    transition: all 0.3s ease !important;\\n  }\\n  .featured-product[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px) !important;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12) !important;\\n  }\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 55px !important;\\n    height: 55px !important;\\n    margin-right: 12px !important;\\n    margin-bottom: 0 !important;\\n    border-radius: 8px !important;\\n    border: 1px solid #e9ecef !important;\\n    object-fit: cover !important;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    flex: 1 !important;\\n    text-align: left !important;\\n    margin-bottom: 0 !important;\\n    margin-right: 8px !important;\\n  }\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 13px !important;\\n    font-weight: 600 !important;\\n    margin-bottom: 4px !important;\\n    color: #212529 !important;\\n    display: block !important;\\n    line-height: 1.3 !important;\\n  }\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n    color: #6c757d !important;\\n    font-weight: 500 !important;\\n    display: block !important;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    flex-direction: column !important;\\n    gap: 6px !important;\\n    padding: 0 !important;\\n    align-items: center !important;\\n    overflow: visible !important;\\n    position: relative !important;\\n    z-index: 10 !important;\\n    min-width: 80px !important;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    width: 75px !important;\\n    padding: 8px 12px !important;\\n    font-size: 11px !important;\\n    font-weight: 600 !important;\\n    border-radius: 16px !important;\\n    min-height: 32px !important;\\n    display: flex !important;\\n    align-items: center !important;\\n    justify-content: center !important;\\n    margin-bottom: 4px !important;\\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n    color: white !important;\\n    border: none !important;\\n    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\\n    transition: all 0.3s ease !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 32px !important;\\n    height: 32px !important;\\n    padding: 6px !important;\\n    border-radius: 50% !important;\\n    display: flex !important;\\n    align-items: center !important;\\n    justify-content: center !important;\\n    border: none !important;\\n    cursor: pointer !important;\\n    transition: all 0.3s ease !important;\\n    margin: 1px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\\n    color: white !important;\\n    box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3) !important;\\n  }\\n  .wishlist-btn[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%) !important;\\n    color: white !important;\\n    box-shadow: 0 2px 6px rgba(255, 159, 243, 0.3) !important;\\n  }\\n  .buy-btn[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;\\n    color: white !important;\\n    box-shadow: 0 2px 6px rgba(79, 172, 254, 0.3) !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px !important;\\n    font-weight: bold !important;\\n  }\\n  .shop-btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-1px) !important;\\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5) !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]:hover, .wishlist-btn[_ngcontent-%COMP%]:hover, .buy-btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-1px) scale(1.05) !important;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 14px 8px 30px 8px !important;\\n    margin-bottom: 18px !important;\\n    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%) !important;\\n    border: 1px solid #dadce0 !important;\\n    border-radius: 10px !important;\\n    min-height: 130px !important;\\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;\\n  }\\n  .featured-product[_ngcontent-%COMP%] {\\n    padding: 10px !important;\\n    margin-bottom: 8px !important;\\n    border-radius: 8px !important;\\n  }\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 50px !important;\\n    height: 50px !important;\\n    margin-right: 10px !important;\\n    border-radius: 6px !important;\\n  }\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n    font-weight: 600 !important;\\n    line-height: 1.2 !important;\\n  }\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 11px !important;\\n    font-weight: 500 !important;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    min-width: 70px !important;\\n    gap: 4px !important;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    width: 65px !important;\\n    padding: 6px 8px !important;\\n    font-size: 10px !important;\\n    min-height: 28px !important;\\n    border-radius: 14px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 28px !important;\\n    height: 28px !important;\\n    padding: 4px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n  }\\n}\\n@media (max-width: 350px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 12px 6px 25px 6px !important;\\n    margin-bottom: 15px !important;\\n    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\\n    border: 1px solid #ced4da !important;\\n    border-radius: 8px !important;\\n    min-height: 120px !important;\\n    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06) !important;\\n  }\\n  .featured-product[_ngcontent-%COMP%] {\\n    padding: 8px !important;\\n    margin-bottom: 6px !important;\\n    border-radius: 6px !important;\\n    gap: 8px !important;\\n  }\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 45px !important;\\n    height: 45px !important;\\n    margin-right: 8px !important;\\n    border-radius: 5px !important;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    margin-right: 6px !important;\\n  }\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 11px !important;\\n    font-weight: 600 !important;\\n    line-height: 1.2 !important;\\n    margin-bottom: 2px !important;\\n  }\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 10px !important;\\n    font-weight: 500 !important;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    min-width: 60px !important;\\n    gap: 3px !important;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    width: 55px !important;\\n    padding: 5px 6px !important;\\n    font-size: 9px !important;\\n    min-height: 24px !important;\\n    border-radius: 12px !important;\\n    margin-bottom: 2px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 24px !important;\\n    height: 24px !important;\\n    padding: 3px !important;\\n    margin: 0.5px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px !important;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 10px 4px 20px 4px !important;\\n    margin-bottom: 12px !important;\\n    background: #f8f9fa !important;\\n    border: 1px solid #dee2e6 !important;\\n    border-radius: 6px !important;\\n    min-height: 110px !important;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\\n  }\\n  .featured-product[_ngcontent-%COMP%] {\\n    padding: 6px !important;\\n    margin-bottom: 4px !important;\\n    border-radius: 4px !important;\\n    gap: 6px !important;\\n  }\\n  .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 40px !important;\\n    height: 40px !important;\\n    margin-right: 6px !important;\\n    border-radius: 4px !important;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    margin-right: 4px !important;\\n  }\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 10px !important;\\n    font-weight: 600 !important;\\n    line-height: 1.1 !important;\\n    margin-bottom: 1px !important;\\n  }\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 9px !important;\\n    font-weight: 500 !important;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    min-width: 50px !important;\\n    gap: 2px !important;\\n  }\\n  .shop-btn[_ngcontent-%COMP%] {\\n    width: 45px !important;\\n    padding: 4px 5px !important;\\n    font-size: 8px !important;\\n    min-height: 20px !important;\\n    border-radius: 10px !important;\\n    margin-bottom: 1px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%] {\\n    width: 20px !important;\\n    height: 20px !important;\\n    padding: 2px !important;\\n    margin: 0.5px !important;\\n  }\\n  .cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 8px !important;\\n  }\\n}\\n.load-more[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n}\\n\\n.load-more-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n}\\n.load-more-btn[_ngcontent-%COMP%]:hover {\\n  background: #00376b;\\n}\\n\\n.empty-feed[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  margin: 20px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #dbdbdb;\\n  margin-bottom: 16px;\\n}\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 8px 0;\\n}\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  margin: 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .instagram-feed[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .instagram-post[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n  }\\n  .feed-posts[_ngcontent-%COMP%] {\\n    gap: 0;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n  }\\n  .likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%] {\\n    padding-left: 12px;\\n    padding-right: 12px;\\n  }\\n  .add-comment-section[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .ecommerce-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px 20px 16px;\\n    background: #f8f9fa;\\n    border-top: 2px solid #e9ecef;\\n    margin-bottom: 8px;\\n    position: relative;\\n    z-index: 1;\\n    overflow: visible;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "FeedComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵtextInterpolate", "post_r2", "location", "mediaUrl", "ɵɵsanitizeUrl", "content", "ɵɵlistener", "FeedComponent_div_2_article_2_div_13_Template_video_click_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "toggleVideoPlay", "FeedComponent_div_2_article_2_div_13_Template_button_click_3_listener", "FeedComponent_div_2_article_2_div_13_div_5_Template", "isReel", "FeedComponent_div_2_article_2_div_14_button_1_Template_button_click_0_listener", "product_r6", "_r5", "$implicit", "showProductDetails", "FeedComponent_div_2_article_2_div_14_button_1_Template", "products", "formatLikesCount", "likes", "ɵɵtextInterpolate1", "hashtag_r7", "FeedComponent_div_2_article_2_div_31_span_1_Template", "hashtags", "FeedComponent_div_2_article_2_div_32_Template_button_click_1_listener", "_r8", "toggleComments", "comments", "FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_9_listener", "product_r10", "_r9", "viewProduct", "FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_11_listener", "addToCart", "FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_13_listener", "addToWishlist", "FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_15_listener", "buyNow", "image", "name", "formatPrice", "price", "FeedComponent_div_2_article_2_div_40_div_2_Template", "slice", "FeedComponent_div_2_article_2_span_8_Template", "FeedComponent_div_2_article_2_div_12_Template", "FeedComponent_div_2_article_2_div_13_Template", "FeedComponent_div_2_article_2_div_14_Template", "FeedComponent_div_2_article_2_Template_button_click_17_listener", "_r1", "toggleLike", "FeedComponent_div_2_article_2_Template_button_click_19_listener", "focusCommentInput", "FeedComponent_div_2_article_2_Template_button_click_21_listener", "sharePost", "FeedComponent_div_2_article_2_Template_button_click_23_listener", "toggleSave", "FeedComponent_div_2_article_2_div_25_Template", "FeedComponent_div_2_article_2_div_31_Template", "FeedComponent_div_2_article_2_div_32_Template", "ɵɵtwoWayListener", "FeedComponent_div_2_article_2_Template_input_ngModelChange_36_listener", "ɵɵtwoWayBindingSet", "newComment", "FeedComponent_div_2_article_2_Template_input_keyup_enter_36_listener", "addComment", "FeedComponent_div_2_article_2_Template_button_click_38_listener", "FeedComponent_div_2_article_2_div_40_Template", "user", "avatar", "fullName", "username", "mediaType", "length", "ɵɵclassProp", "isLiked", "ɵɵclassMap", "isSaved", "getTimeAgo", "createdAt", "ɵɵtwoWayProperty", "trim", "FeedComponent_div_2_div_1_Template", "FeedComponent_div_2_article_2_Template", "posts", "trackByPostId", "FeedComponent_div_3_Template_button_click_1_listener", "_r11", "loadMorePosts", "FeedComponent", "constructor", "router", "cartService", "wishlistService", "loading", "hasMore", "currentPage", "ngOnInit", "loadPosts", "setTimeout", "getFallbackPosts", "_id", "shares", "Date", "now", "toISOString", "index", "post", "console", "log", "event", "video", "target", "paused", "play", "pause", "product", "navigate", "toFixed", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "diffInWeeks", "undefined", "subscribe", "next", "response", "success", "alert", "error", "queryParams", "productId", "source", "ɵɵdirectiveInject", "i1", "Router", "i2", "CartService", "i3", "WishlistService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeedComponent_Template", "rf", "ctx", "FeedComponent_div_1_Template", "FeedComponent_div_2_Template", "FeedComponent_div_3_Template", "FeedComponent_div_4_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\feed\\feed.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\feed\\feed.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-feed',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './feed.component.html',\n  styleUrls: ['./feed.component.scss']\n})\nexport class FeedComponent implements OnInit {\n  posts: any[] = [];\n  loading = true;\n  hasMore = true;\n  currentPage = 1;\n  newComment = '';\n\n  constructor(\n    private router: Router,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    this.loadPosts();\n  }\n\n  loadPosts() {\n    this.loading = true;\n\n    // Simulate loading with realistic Instagram-style posts\n    setTimeout(() => {\n      this.posts = this.getFallbackPosts();\n      this.loading = false;\n    }, 1000);\n  }\n\n  getFallbackPosts() {\n    return [\n      {\n        _id: 'post-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'assets/images/default-avatar.svg'\n        },\n        content: 'Sustainable fashion is the future! 🌱✨ This eco-friendly dress is made from recycled materials and looks absolutely stunning. #SustainableFashion #EcoFriendly #OOTD',\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        location: 'Mumbai, India',\n        likes: 1247,\n        comments: 89,\n        shares: 34,\n        isLiked: false,\n        isSaved: false,\n        isReel: false,\n        hashtags: ['SustainableFashion', 'EcoFriendly', 'OOTD'],\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        products: [\n          {\n            _id: 'prod-1',\n            name: 'Eco-Friendly Summer Dress',\n            price: 2499,\n            image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop'\n          }\n        ]\n      },\n\n      {\n        _id: 'post-3',\n        user: {\n          _id: 'user-3',\n          username: 'ai_trendsetter_zara',\n          fullName: 'Zara Patel',\n          avatar: 'assets/images/default-avatar.svg'\n        },\n        content: 'Ethnic fusion at its finest! Traditional meets modern ✨ This kurti is perfect for any occasion.',\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        location: 'Bangalore, India',\n        likes: 2156,\n        comments: 134,\n        shares: 67,\n        isLiked: false,\n        isSaved: true,\n        isReel: false,\n        hashtags: ['EthnicWear', 'Fusion', 'Traditional'],\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        products: [\n          {\n            _id: 'prod-3',\n            name: 'Designer Ethnic Kurti',\n            price: 1899,\n            image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop'\n          }\n        ]\n      }\n    ];\n  }\n\n\n\n  loadMorePosts() {\n    this.currentPage++;\n    this.loadPosts();\n  }\n\n  trackByPostId(index: number, post: any): string {\n    return post._id;\n  }\n\n  // Instagram-style Actions\n  toggleLike(post: any) {\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n  }\n\n  toggleSave(post: any) {\n    post.isSaved = !post.isSaved;\n  }\n\n  toggleComments(post: any) {\n    // Navigate to post detail or show comments modal\n    console.log('Toggle comments for post:', post._id);\n  }\n\n  sharePost(post: any) {\n    // Implement share functionality\n    console.log('Share post:', post._id);\n  }\n\n  addComment(post: any) {\n    if (this.newComment.trim()) {\n      post.comments += 1;\n      console.log('Add comment:', this.newComment, 'to post:', post._id);\n      this.newComment = '';\n    }\n  }\n\n  focusCommentInput(post: any) {\n    // Focus on comment input\n    console.log('Focus comment input for post:', post._id);\n  }\n\n  toggleVideoPlay(event: Event) {\n    const video = event.target as HTMLVideoElement;\n    if (video.paused) {\n      video.play();\n    } else {\n      video.pause();\n    }\n  }\n\n  showProductDetails(product: any) {\n    console.log('Show product details:', product);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  formatLikesCount(likes: number): string {\n    if (likes === 1) return '1 like';\n    if (likes < 1000) return `${likes} likes`;\n    if (likes < 1000000) return `${(likes / 1000).toFixed(1)}K likes`;\n    return `${(likes / 1000000).toFixed(1)}M likes`;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d`;\n\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks}w`;\n  }\n\n  // E-commerce Actions\n  addToCart(product: any) {\n    console.log('Adding to cart:', product);\n    this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n      next: (response) => {\n        if (response.success) {\n          alert('Product added to cart!');\n        } else {\n          alert('Failed to add product to cart');\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n        alert('Error adding product to cart');\n      }\n    });\n  }\n\n  addToWishlist(product: any) {\n    console.log('Adding to wishlist:', product);\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: (response) => {\n        if (response.success) {\n          alert('Product added to wishlist!');\n        } else {\n          alert('Failed to add product to wishlist');\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n        alert('Error adding product to wishlist');\n      }\n    });\n  }\n\n  buyNow(product: any) {\n    console.log('Buying product:', product);\n    this.router.navigate(['/checkout'], {\n      queryParams: {\n        productId: product._id,\n        source: 'feed'\n      }\n    });\n  }\n}\n", "<!-- Instagram-style Feed -->\n<div class=\"instagram-feed\">\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <div *ngFor=\"let item of [1,2,3]\" class=\"post-skeleton\">\n      <div class=\"skeleton-header\">\n        <div class=\"skeleton-avatar\"></div>\n        <div class=\"skeleton-user-info\">\n          <div class=\"skeleton-username\"></div>\n          <div class=\"skeleton-time\"></div>\n        </div>\n      </div>\n      <div class=\"skeleton-image\"></div>\n      <div class=\"skeleton-actions\"></div>\n    </div>\n  </div>\n\n  <!-- Posts and Reels -->\n  <div *ngIf=\"!loading\" class=\"feed-posts\">\n    <!-- Debug info -->\n    <div *ngIf=\"posts.length === 0\" class=\"no-posts-message\">\n      <p>No posts available. Loading posts...</p>\n    </div>\n\n    <article *ngFor=\"let post of posts; trackBy: trackByPostId\" class=\"instagram-post\">\n\n      <!-- Post Header -->\n      <header class=\"post-header\">\n        <div class=\"user-info\">\n          <div class=\"user-avatar-container\">\n            <img [src]=\"post.user?.avatar || 'assets/images/default-avatar.png'\"\n                 [alt]=\"post.user?.fullName || 'User'\"\n                 class=\"user-avatar\">\n          </div>\n          <div class=\"user-details\">\n            <h3 class=\"username\">{{ post.user?.username || 'Unknown User' }}</h3>\n            <span class=\"post-location\" *ngIf=\"post.location\">{{ post.location }}</span>\n          </div>\n        </div>\n        <button class=\"post-options\">\n          <i class=\"fas fa-ellipsis-h\"></i>\n        </button>\n      </header>\n\n      <!-- Post Media -->\n      <div class=\"post-media-container\">\n        <!-- Image Post -->\n        <div *ngIf=\"post.mediaType === 'image'\" class=\"post-image-container\">\n          <img [src]=\"post.mediaUrl\" [alt]=\"post.content\" class=\"post-image\">\n        </div>\n\n        <!-- Video/Reel Post -->\n        <div *ngIf=\"post.mediaType === 'video'\" class=\"post-video-container\">\n          <video [src]=\"post.mediaUrl\" class=\"post-video\"\n                 [muted]=\"true\"\n                 [loop]=\"true\"\n                 (click)=\"toggleVideoPlay($event)\">\n          </video>\n          <div class=\"video-overlay\">\n            <button class=\"play-pause-btn\" (click)=\"toggleVideoPlay($event)\">\n              <i class=\"fas fa-play\"></i>\n            </button>\n          </div>\n          <!-- Reel indicator -->\n          <div class=\"reel-indicator\" *ngIf=\"post.isReel\">\n            <i class=\"fas fa-video\"></i>\n            <span>Reel</span>\n          </div>\n        </div>\n\n        <!-- Product Tags Overlay -->\n        <div class=\"product-tags-overlay\" *ngIf=\"post.products && post.products.length > 0\">\n          <button *ngFor=\"let product of post.products\"\n                  class=\"product-tag-btn\"\n                  (click)=\"showProductDetails(product)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Post Actions -->\n      <div class=\"post-actions\">\n        <div class=\"primary-actions\">\n          <button class=\"action-btn like-btn\"\n                  [class.liked]=\"post.isLiked\"\n                  (click)=\"toggleLike(post)\">\n            <i [class]=\"post.isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n          </button>\n\n          <button class=\"action-btn comment-btn\" (click)=\"focusCommentInput(post)\">\n            <i class=\"far fa-comment\"></i>\n          </button>\n\n          <button class=\"action-btn share-btn\" (click)=\"sharePost(post)\">\n            <i class=\"far fa-paper-plane\"></i>\n          </button>\n        </div>\n\n        <button class=\"action-btn save-btn\"\n                [class.saved]=\"post.isSaved\"\n                (click)=\"toggleSave(post)\">\n          <i [class]=\"post.isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n        </button>\n      </div>\n\n      <!-- Likes Count -->\n      <div class=\"likes-section\" *ngIf=\"post.likes > 0\">\n        <span class=\"likes-count\">{{ formatLikesCount(post.likes) }}</span>\n      </div>\n\n      <!-- Post Caption -->\n      <div class=\"post-caption\">\n        <span class=\"username\">{{ post.user?.username || 'Unknown User' }}</span>\n        <span class=\"caption-text\">{{ post.content }}</span>\n        <div class=\"hashtags\" *ngIf=\"post.hashtags && post.hashtags.length > 0\">\n          <span *ngFor=\"let hashtag of post.hashtags\" class=\"hashtag\">#{{ hashtag }}</span>\n        </div>\n      </div>\n\n      <!-- View Comments -->\n      <div class=\"comments-preview\" *ngIf=\"post.comments > 0\">\n        <button class=\"view-comments-btn\" (click)=\"toggleComments(post)\">\n          View all {{ post.comments }} comments\n        </button>\n      </div>\n\n      <!-- Post Time -->\n      <div class=\"post-time\">\n        {{ getTimeAgo(post.createdAt) }}\n      </div>\n\n      <!-- Add Comment -->\n      <div class=\"add-comment-section\">\n        <input type=\"text\"\n               placeholder=\"Add a comment...\"\n               class=\"comment-input\"\n               [(ngModel)]=\"newComment\"\n               (keyup.enter)=\"addComment(post)\"\n               #commentInput>\n        <button class=\"post-comment-btn\"\n                (click)=\"addComment(post)\"\n                [disabled]=\"!newComment || !newComment.trim()\">\n          Post\n        </button>\n      </div>\n\n      <!-- E-commerce Actions for Fashion Posts -->\n      <div class=\"ecommerce-section\" *ngIf=\"post.products && post.products.length > 0\">\n        <div class=\"product-showcase\">\n          <div *ngFor=\"let product of post.products.slice(0, 3)\" class=\"featured-product\">\n            <!-- Product Header: Image + Title inline -->\n            <div class=\"product-header\">\n              <img [src]=\"product.image\" [alt]=\"product.name\" class=\"product-thumbnail\">\n              <div class=\"product-details\">\n                <span class=\"product-name\">{{ product.name }}</span>\n                <span class=\"product-price\">{{ formatPrice(product.price) }}</span>\n              </div>\n            </div>\n\n            <!-- Product Actions: Buttons inline below -->\n            <div class=\"product-actions\">\n              <button class=\"shop-btn\" (click)=\"viewProduct(product)\">Shop</button>\n              <button class=\"cart-btn\" (click)=\"addToCart(product)\">\n                <i class=\"fas fa-shopping-cart\"></i>\n              </button>\n              <button class=\"wishlist-btn\" (click)=\"addToWishlist(product)\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n              <button class=\"buy-btn\" (click)=\"buyNow(product)\">\n                <i class=\"fas fa-bolt\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </article>\n  </div>\n\n  <!-- Load More -->\n  <div *ngIf=\"hasMore && !loading\" class=\"load-more\">\n    <button (click)=\"loadMorePosts()\" class=\"load-more-btn\">\n      Load More Posts\n    </button>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!loading && posts.length === 0\" class=\"empty-feed\">\n    <div class=\"empty-content\">\n      <i class=\"fas fa-camera\"></i>\n      <h3>Welcome to DFashion</h3>\n      <p>Follow fashion influencers to see their latest posts and discover trending styles!</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;ICGtCC,EADF,CAAAC,cAAA,aAAwD,aACzB;IAC3BD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,cAAgC;IAE9BD,EADA,CAAAE,SAAA,cAAqC,cACJ;IAErCF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EADA,CAAAE,SAAA,cAAkC,cACE;IACtCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAXRH,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAI,UAAA,IAAAC,kCAAA,iBAAwD;IAW1DL,EAAA,CAAAG,YAAA,EAAM;;;IAXkBH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;IAiB9BT,EADF,CAAAC,cAAA,cAAyD,QACpD;IAAAD,EAAA,CAAAU,MAAA,2CAAoC;IACzCV,EADyC,CAAAG,YAAA,EAAI,EACvC;;;;;IAcEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAmB;;;;;IAWzEb,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAE,SAAA,cAAmE;IACrEF,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAM,SAAA,EAAqB;IAACN,EAAtB,CAAAO,UAAA,QAAAK,OAAA,CAAAE,QAAA,EAAAd,EAAA,CAAAe,aAAA,CAAqB,QAAAH,OAAA,CAAAI,OAAA,CAAqB;;;;;IAgB/ChB,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,WAAI;IACZV,EADY,CAAAG,YAAA,EAAO,EACb;;;;;;IAdNH,EADF,CAAAC,cAAA,cAAqE,gBAI1B;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAC,qEAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IACxCnB,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,cAA2B,iBACwC;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAS,sEAAAP,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAC9DnB,EAAA,CAAAE,SAAA,YAA2B;IAE/BF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAI,UAAA,IAAAuB,mDAAA,kBAAgD;IAIlD3B,EAAA,CAAAG,YAAA,EAAM;;;;IAfGH,EAAA,CAAAM,SAAA,EAAqB;IAErBN,EAFA,CAAAO,UAAA,QAAAK,OAAA,CAAAE,QAAA,EAAAd,EAAA,CAAAe,aAAA,CAAqB,eACP,cACD;IASSf,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAgB,MAAA,CAAiB;;;;;;IAQ9C5B,EAAA,CAAAC,cAAA,iBAE8C;IAAtCD,EAAA,CAAAiB,UAAA,mBAAAY,+EAAA;MAAA,MAAAC,UAAA,GAAA9B,EAAA,CAAAoB,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAW,kBAAA,CAAAH,UAAA,CAA2B;IAAA,EAAC;IAC3C9B,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;IALXH,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAI,UAAA,IAAA8B,sDAAA,qBAE8C;IAGhDlC,EAAA,CAAAG,YAAA,EAAM;;;;IALwBH,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAK,OAAA,CAAAuB,QAAA,CAAgB;;;;;IAmC9CnC,EADF,CAAAC,cAAA,cAAkD,eACtB;IAAAD,EAAA,CAAAU,MAAA,GAAkC;IAC9DV,EAD8D,CAAAG,YAAA,EAAO,EAC/D;;;;;IADsBH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAW,iBAAA,CAAAW,MAAA,CAAAc,gBAAA,CAAAxB,OAAA,CAAAyB,KAAA,EAAkC;;;;;IAQ1DrC,EAAA,CAAAC,cAAA,eAA4D;IAAAD,EAAA,CAAAU,MAAA,GAAc;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IAArBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAsC,kBAAA,MAAAC,UAAA,KAAc;;;;;IAD5EvC,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAI,UAAA,IAAAoC,oDAAA,mBAA4D;IAC9DxC,EAAA,CAAAG,YAAA,EAAM;;;;IADsBH,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAK,OAAA,CAAA6B,QAAA,CAAgB;;;;;;IAM5CzC,EADF,CAAAC,cAAA,cAAwD,iBACW;IAA/BD,EAAA,CAAAiB,UAAA,mBAAAyB,sEAAA;MAAA1C,EAAA,CAAAoB,aAAA,CAAAuB,GAAA;MAAA,MAAA/B,OAAA,GAAAZ,EAAA,CAAAuB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAsB,cAAA,CAAAhC,OAAA,CAAoB;IAAA,EAAC;IAC9DZ,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAFFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAsC,kBAAA,eAAA1B,OAAA,CAAAiC,QAAA,eACF;;;;;;IA4BI7C,EAFF,CAAAC,cAAA,cAAgF,cAElD;IAC1BD,EAAA,CAAAE,SAAA,cAA0E;IAExEF,EADF,CAAAC,cAAA,cAA6B,eACA;IAAAD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IAEhEV,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;IAIJH,EADF,CAAAC,cAAA,cAA6B,iBAC6B;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA6B,4EAAA;MAAA,MAAAC,WAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAAhB,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAA2B,WAAA,CAAAF,WAAA,CAAoB;IAAA,EAAC;IAAC/C,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACrEH,EAAA,CAAAC,cAAA,kBAAsD;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAiC,6EAAA;MAAA,MAAAH,WAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAAhB,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAA6B,SAAA,CAAAJ,WAAA,CAAkB;IAAA,EAAC;IACnD/C,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAmC,6EAAA;MAAA,MAAAL,WAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAAhB,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAA+B,aAAA,CAAAN,WAAA,CAAsB;IAAA,EAAC;IAC3D/C,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkD;IAA1BD,EAAA,CAAAiB,UAAA,mBAAAqC,6EAAA;MAAA,MAAAP,WAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAAhB,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAiC,MAAA,CAAAR,WAAA,CAAe;IAAA,EAAC;IAC/C/C,EAAA,CAAAE,SAAA,aAA2B;IAGjCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IApBGH,EAAA,CAAAM,SAAA,GAAqB;IAACN,EAAtB,CAAAO,UAAA,QAAAwC,WAAA,CAAAS,KAAA,EAAAxD,EAAA,CAAAe,aAAA,CAAqB,QAAAgC,WAAA,CAAAU,IAAA,CAAqB;IAElBzD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAW,iBAAA,CAAAoC,WAAA,CAAAU,IAAA,CAAkB;IACjBzD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAW,iBAAA,CAAAW,MAAA,CAAAoC,WAAA,CAAAX,WAAA,CAAAY,KAAA,EAAgC;;;;;IAPpE3D,EADF,CAAAC,cAAA,cAAiF,cACjD;IAC5BD,EAAA,CAAAI,UAAA,IAAAwD,mDAAA,mBAAgF;IAyBpF5D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzBuBH,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAO,UAAA,YAAAK,OAAA,CAAAuB,QAAA,CAAA0B,KAAA,OAA4B;;;;;;IAxHrD7D,EALN,CAAAC,cAAA,kBAAmF,iBAGrD,cACH,cACc;IACjCD,EAAA,CAAAE,SAAA,cAEyB;IAC3BF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,aACH;IAAAD,EAAA,CAAAU,MAAA,GAA2C;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAI,UAAA,IAAA0D,6CAAA,mBAAkD;IAEtD9D,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,aAAiC;IAErCF,EADE,CAAAG,YAAA,EAAS,EACF;IAGTH,EAAA,CAAAC,cAAA,eAAkC;IA0BhCD,EAxBA,CAAAI,UAAA,KAAA2D,6CAAA,kBAAqE,KAAAC,6CAAA,kBAKA,KAAAC,6CAAA,kBAmBe;IAOtFjE,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA0B,eACK,kBAGQ;IAA3BD,EAAA,CAAAiB,UAAA,mBAAAiD,gEAAA;MAAA,MAAAtD,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAA8C,UAAA,CAAAxD,OAAA,CAAgB;IAAA,EAAC;IAChCZ,EAAA,CAAAE,SAAA,SAAgE;IAClEF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAAyE;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAoD,gEAAA;MAAA,MAAAzD,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAgD,iBAAA,CAAA1D,OAAA,CAAuB;IAAA,EAAC;IACtEZ,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAA+D;IAA1BD,EAAA,CAAAiB,UAAA,mBAAAsD,gEAAA;MAAA,MAAA3D,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAkD,SAAA,CAAA5D,OAAA,CAAe;IAAA,EAAC;IAC5DZ,EAAA,CAAAE,SAAA,aAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,kBAEmC;IAA3BD,EAAA,CAAAiB,UAAA,mBAAAwD,gEAAA;MAAA,MAAA7D,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAoD,UAAA,CAAA9D,OAAA,CAAgB;IAAA,EAAC;IAChCZ,EAAA,CAAAE,SAAA,SAAsE;IAE1EF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAuE,6CAAA,kBAAkD;IAMhD3E,EADF,CAAAC,cAAA,eAA0B,gBACD;IAAAD,EAAA,CAAAU,MAAA,IAA2C;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAI,UAAA,KAAAwE,6CAAA,kBAAwE;IAG1E5E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,KAAAyE,6CAAA,kBAAwD;IAOxD7E,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAiC,oBAMV;IAFdD,EAAA,CAAA8E,gBAAA,2BAAAC,uEAAA5D,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAAvB,EAAA,CAAAgF,kBAAA,CAAA1D,MAAA,CAAA2D,UAAA,EAAA9D,MAAA,MAAAG,MAAA,CAAA2D,UAAA,GAAA9D,MAAA;MAAA,OAAAnB,EAAA,CAAAwB,WAAA,CAAAL,MAAA;IAAA,EAAwB;IACxBnB,EAAA,CAAAiB,UAAA,yBAAAiE,qEAAA;MAAA,MAAAtE,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAeF,MAAA,CAAA6D,UAAA,CAAAvE,OAAA,CAAgB;IAAA,EAAC;IAJvCZ,EAAA,CAAAG,YAAA,EAKqB;IACrBH,EAAA,CAAAC,cAAA,kBAEuD;IAD/CD,EAAA,CAAAiB,UAAA,mBAAAmE,gEAAA;MAAA,MAAAxE,OAAA,GAAAZ,EAAA,CAAAoB,aAAA,CAAA+C,GAAA,EAAAnC,SAAA;MAAA,MAAAV,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAA6D,UAAA,CAAAvE,OAAA,CAAgB;IAAA,EAAC;IAEhCZ,EAAA,CAAAU,MAAA,cACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAiF,6CAAA,kBAAiF;IA4BnFrF,EAAA,CAAAG,YAAA,EAAU;;;;;IAjJGH,EAAA,CAAAM,SAAA,GAA+D;IAC/DN,EADA,CAAAO,UAAA,SAAAK,OAAA,CAAA0E,IAAA,kBAAA1E,OAAA,CAAA0E,IAAA,CAAAC,MAAA,yCAAAvF,EAAA,CAAAe,aAAA,CAA+D,SAAAH,OAAA,CAAA0E,IAAA,kBAAA1E,OAAA,CAAA0E,IAAA,CAAAE,QAAA,YAC1B;IAIrBxF,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAW,iBAAA,EAAAC,OAAA,CAAA0E,IAAA,kBAAA1E,OAAA,CAAA0E,IAAA,CAAAG,QAAA,oBAA2C;IACnCzF,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAC,QAAA,CAAmB;IAW9Cb,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAA8E,SAAA,aAAgC;IAKhC1F,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAA8E,SAAA,aAAgC;IAmBH1F,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAuB,QAAA,IAAAvB,OAAA,CAAAuB,QAAA,CAAAwD,MAAA,KAA+C;IAaxE3F,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAA4F,WAAA,UAAAhF,OAAA,CAAAiF,OAAA,CAA4B;IAE/B7F,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAA8F,UAAA,CAAAlF,OAAA,CAAAiF,OAAA,mCAAwD;IAavD7F,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAA4F,WAAA,UAAAhF,OAAA,CAAAmF,OAAA,CAA4B;IAE/B/F,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAA8F,UAAA,CAAAlF,OAAA,CAAAmF,OAAA,yCAA8D;IAKzC/F,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAyB,KAAA,KAAoB;IAMvBrC,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAW,iBAAA,EAAAC,OAAA,CAAA0E,IAAA,kBAAA1E,OAAA,CAAA0E,IAAA,CAAAG,QAAA,oBAA2C;IACvCzF,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAI,OAAA,CAAkB;IACtBhB,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAA6B,QAAA,IAAA7B,OAAA,CAAA6B,QAAA,CAAAkD,MAAA,KAA+C;IAMzC3F,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAiC,QAAA,KAAuB;IAQpD7C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAsC,kBAAA,MAAAhB,MAAA,CAAA0E,UAAA,CAAApF,OAAA,CAAAqF,SAAA,OACF;IAOSjG,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAkG,gBAAA,YAAA5E,MAAA,CAAA2D,UAAA,CAAwB;IAKvBjF,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAO,UAAA,cAAAe,MAAA,CAAA2D,UAAA,KAAA3D,MAAA,CAAA2D,UAAA,CAAAkB,IAAA,GAA8C;IAMxBnG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAK,OAAA,CAAAuB,QAAA,IAAAvB,OAAA,CAAAuB,QAAA,CAAAwD,MAAA,KAA+C;;;;;IAjInF3F,EAAA,CAAAC,cAAA,cAAyC;IAMvCD,EAJA,CAAAI,UAAA,IAAAgG,kCAAA,kBAAyD,IAAAC,sCAAA,wBAI0B;IAwJrFrG,EAAA,CAAAG,YAAA,EAAM;;;;IA5JEH,EAAA,CAAAM,SAAA,EAAwB;IAAxBN,EAAA,CAAAO,UAAA,SAAAe,MAAA,CAAAgF,KAAA,CAAAX,MAAA,OAAwB;IAIJ3F,EAAA,CAAAM,SAAA,EAAU;IAAAN,EAAV,CAAAO,UAAA,YAAAe,MAAA,CAAAgF,KAAA,CAAU,iBAAAhF,MAAA,CAAAiF,aAAA,CAAsB;;;;;;IA4J1DvG,EADF,CAAAC,cAAA,cAAmD,iBACO;IAAhDD,EAAA,CAAAiB,UAAA,mBAAAuF,qDAAA;MAAAxG,EAAA,CAAAoB,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASF,MAAA,CAAAoF,aAAA,EAAe;IAAA,EAAC;IAC/B1G,EAAA,CAAAU,MAAA,wBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAIJH,EADF,CAAAC,cAAA,cAA+D,cAClC;IACzBD,EAAA,CAAAE,SAAA,YAA6B;IAC7BF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,yFAAkF;IAEzFV,EAFyF,CAAAG,YAAA,EAAI,EACrF,EACF;;;ADlLR,OAAM,MAAOwG,aAAa;EAOxBC,YACUC,MAAc,EACdC,WAAwB,EACxBC,eAAgC;IAFhC,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IATzB,KAAAT,KAAK,GAAU,EAAE;IACjB,KAAAU,OAAO,GAAG,IAAI;IACd,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAjC,UAAU,GAAG,EAAE;EAMZ;EAEHkC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACJ,OAAO,GAAG,IAAI;IAEnB;IACAK,UAAU,CAAC,MAAK;MACd,IAAI,CAACf,KAAK,GAAG,IAAI,CAACgB,gBAAgB,EAAE;MACpC,IAAI,CAACN,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAM,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEC,GAAG,EAAE,QAAQ;MACbjC,IAAI,EAAE;QACJiC,GAAG,EAAE,QAAQ;QACb9B,QAAQ,EAAE,qBAAqB;QAC/BD,QAAQ,EAAE,WAAW;QACrBD,MAAM,EAAE;OACT;MACDvE,OAAO,EAAE,sKAAsK;MAC/KF,QAAQ,EAAE,mFAAmF;MAC7F4E,SAAS,EAAE,OAAO;MAClB7E,QAAQ,EAAE,eAAe;MACzBwB,KAAK,EAAE,IAAI;MACXQ,QAAQ,EAAE,EAAE;MACZ2E,MAAM,EAAE,EAAE;MACV3B,OAAO,EAAE,KAAK;MACdE,OAAO,EAAE,KAAK;MACdnE,MAAM,EAAE,KAAK;MACba,QAAQ,EAAE,CAAC,oBAAoB,EAAE,aAAa,EAAE,MAAM,CAAC;MACvDwD,SAAS,EAAE,IAAIwB,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClExF,QAAQ,EAAE,CACR;QACEoF,GAAG,EAAE,QAAQ;QACb9D,IAAI,EAAE,2BAA2B;QACjCE,KAAK,EAAE,IAAI;QACXH,KAAK,EAAE;OACR;KAEJ,EAED;MACE+D,GAAG,EAAE,QAAQ;MACbjC,IAAI,EAAE;QACJiC,GAAG,EAAE,QAAQ;QACb9B,QAAQ,EAAE,qBAAqB;QAC/BD,QAAQ,EAAE,YAAY;QACtBD,MAAM,EAAE;OACT;MACDvE,OAAO,EAAE,iGAAiG;MAC1GF,QAAQ,EAAE,gFAAgF;MAC1F4E,SAAS,EAAE,OAAO;MAClB7E,QAAQ,EAAE,kBAAkB;MAC5BwB,KAAK,EAAE,IAAI;MACXQ,QAAQ,EAAE,GAAG;MACb2E,MAAM,EAAE,EAAE;MACV3B,OAAO,EAAE,KAAK;MACdE,OAAO,EAAE,IAAI;MACbnE,MAAM,EAAE,KAAK;MACba,QAAQ,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,CAAC;MACjDwD,SAAS,EAAE,IAAIwB,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClExF,QAAQ,EAAE,CACR;QACEoF,GAAG,EAAE,QAAQ;QACb9D,IAAI,EAAE,uBAAuB;QAC7BE,KAAK,EAAE,IAAI;QACXH,KAAK,EAAE;OACR;KAEJ,CACF;EACH;EAIAkD,aAAaA,CAAA;IACX,IAAI,CAACQ,WAAW,EAAE;IAClB,IAAI,CAACE,SAAS,EAAE;EAClB;EAEAb,aAAaA,CAACqB,KAAa,EAAEC,IAAS;IACpC,OAAOA,IAAI,CAACN,GAAG;EACjB;EAEA;EACAnD,UAAUA,CAACyD,IAAS;IAClBA,IAAI,CAAChC,OAAO,GAAG,CAACgC,IAAI,CAAChC,OAAO;IAC5BgC,IAAI,CAACxF,KAAK,IAAIwF,IAAI,CAAChC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EACrC;EAEAnB,UAAUA,CAACmD,IAAS;IAClBA,IAAI,CAAC9B,OAAO,GAAG,CAAC8B,IAAI,CAAC9B,OAAO;EAC9B;EAEAnD,cAAcA,CAACiF,IAAS;IACtB;IACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,IAAI,CAACN,GAAG,CAAC;EACpD;EAEA/C,SAASA,CAACqD,IAAS;IACjB;IACAC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,IAAI,CAACN,GAAG,CAAC;EACtC;EAEApC,UAAUA,CAAC0C,IAAS;IAClB,IAAI,IAAI,CAAC5C,UAAU,CAACkB,IAAI,EAAE,EAAE;MAC1B0B,IAAI,CAAChF,QAAQ,IAAI,CAAC;MAClBiF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC9C,UAAU,EAAE,UAAU,EAAE4C,IAAI,CAACN,GAAG,CAAC;MAClE,IAAI,CAACtC,UAAU,GAAG,EAAE;;EAExB;EAEAX,iBAAiBA,CAACuD,IAAS;IACzB;IACAC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,IAAI,CAACN,GAAG,CAAC;EACxD;EAEA9F,eAAeA,CAACuG,KAAY;IAC1B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,MAAM,EAAE;MAChBF,KAAK,CAACG,IAAI,EAAE;KACb,MAAM;MACLH,KAAK,CAACI,KAAK,EAAE;;EAEjB;EAEApG,kBAAkBA,CAACqG,OAAY;IAC7BR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEO,OAAO,CAAC;EAC/C;EAEArF,WAAWA,CAACqF,OAAY;IACtB,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACf,GAAG,CAAC,CAAC;EACjD;EAEAnF,gBAAgBA,CAACC,KAAa;IAC5B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ;IAChC,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAO,GAAGA,KAAK,QAAQ;IACzC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAO,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEmG,OAAO,CAAC,CAAC,CAAC,SAAS;IACjE,OAAO,GAAG,CAACnG,KAAK,GAAG,OAAO,EAAEmG,OAAO,CAAC,CAAC,CAAC,SAAS;EACjD;EAEA9E,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI8E,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC;EAClB;EAEAqC,UAAUA,CAAC+C,UAAkB;IAC3B,MAAMrB,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMuB,IAAI,GAAG,IAAIvB,IAAI,CAACsB,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACzB,GAAG,CAAC0B,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,GAAG;IAE3C,MAAMC,WAAW,GAAGL,IAAI,CAACC,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,GAAG;EAC1B;EAEA;EACApG,SAASA,CAACmF,OAAY;IACpBR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,OAAO,CAAC;IACvC,IAAI,CAACxB,WAAW,CAAC3D,SAAS,CAACmF,OAAO,CAACf,GAAG,EAAE,CAAC,EAAEiC,SAAS,EAAEA,SAAS,CAAC,CAACC,SAAS,CAAC;MACzEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBC,KAAK,CAAC,wBAAwB,CAAC;SAChC,MAAM;UACLA,KAAK,CAAC,+BAA+B,CAAC;;MAE1C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CD,KAAK,CAAC,8BAA8B,CAAC;MACvC;KACD,CAAC;EACJ;EAEAxG,aAAaA,CAACiF,OAAY;IACxBR,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,OAAO,CAAC;IAC3C,IAAI,CAACvB,eAAe,CAAC1D,aAAa,CAACiF,OAAO,CAACf,GAAG,CAAC,CAACkC,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBC,KAAK,CAAC,4BAA4B,CAAC;SACpC,MAAM;UACLA,KAAK,CAAC,mCAAmC,CAAC;;MAE9C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDD,KAAK,CAAC,kCAAkC,CAAC;MAC3C;KACD,CAAC;EACJ;EAEAtG,MAAMA,CAAC+E,OAAY;IACjBR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,OAAO,CAAC;IACvC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCwB,WAAW,EAAE;QACXC,SAAS,EAAE1B,OAAO,CAACf,GAAG;QACtB0C,MAAM,EAAE;;KAEX,CAAC;EACJ;;;uBApOWtD,aAAa,EAAA3G,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtK,EAAA,CAAAkK,iBAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAb7D,aAAa;MAAA8D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3K,EAAA,CAAA4K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb1BlL,EAAA,CAAAC,cAAA,aAA4B;UAyL1BD,EAvLA,CAAAI,UAAA,IAAAgL,4BAAA,iBAA+C,IAAAC,4BAAA,iBAeN,IAAAC,4BAAA,iBAiKU,IAAAC,4BAAA,iBAOY;UAOjEvL,EAAA,CAAAG,YAAA,EAAM;;;UA9LEH,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAO,UAAA,SAAA4K,GAAA,CAAAnE,OAAA,CAAa;UAebhH,EAAA,CAAAM,SAAA,EAAc;UAAdN,EAAA,CAAAO,UAAA,UAAA4K,GAAA,CAAAnE,OAAA,CAAc;UAiKdhH,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAA4K,GAAA,CAAAlE,OAAA,KAAAkE,GAAA,CAAAnE,OAAA,CAAyB;UAOzBhH,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,UAAA4K,GAAA,CAAAnE,OAAA,IAAAmE,GAAA,CAAA7E,KAAA,CAAAX,MAAA,OAAoC;;;qBDhLhC7F,YAAY,EAAA0L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3L,WAAW,EAAA4L,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}