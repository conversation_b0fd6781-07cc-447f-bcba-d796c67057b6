{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 19);\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 23);\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 33);\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵelement(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_9_div_2_div_5_Template, 1, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_9_div_2_Template, 6, 2, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"ion-icon\", 40);\n    i0.ɵɵelementStart(2, \"h3\", 41);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 42);\n    i0.ɵɵtext(5, \"Featured brands will appear here when available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeaturedBrandsComponent_div_12_div_7_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_12_div_7_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 14);\n  }\n  if (rf & 2) {\n    const star_r9 = ctx.$implicit;\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r9 <= product_r8.rating.average);\n    i0.ɵɵproperty(\"name\", star_r9 <= product_r8.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_12_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_div_7_div_25_Template_div_click_0_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r8, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 69);\n    i0.ɵɵelement(2, \"img\", 70);\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_div_7_div_25_Template_button_click_4_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_div_7_div_25_Template_button_click_6_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 72);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 73)(9, \"h5\", 74);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 75)(12, \"span\", 76);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_12_div_7_div_25_span_14_Template, 2, 1, \"span\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 78)(16, \"div\", 79);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_12_div_7_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r8.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.images[0].alt || product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r8._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r8.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r8._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r8.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_div_7_Template_div_click_0_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 52)(2, \"div\", 53)(3, \"h3\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 55)(6, \"div\", 56);\n    i0.ɵɵelement(7, \"ion-icon\", 57);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 56);\n    i0.ɵɵelement(11, \"ion-icon\", 58);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 56);\n    i0.ɵɵelement(15, \"ion-icon\", 59);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 60);\n    i0.ɵɵelement(19, \"ion-icon\", 61);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 62)(22, \"h4\", 63);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 64);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_12_div_7_div_25_Template, 20, 13, \"div\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 66)(27, \"button\", 67)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r6.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r6.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r6.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r6.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵlistener(\"mouseenter\", function FeaturedBrandsComponent_div_12_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function FeaturedBrandsComponent_div_12_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 49);\n    i0.ɵɵtemplate(7, FeaturedBrandsComponent_div_12_div_7_Template, 31, 7, \"div\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵelement(1, \"ion-icon\", 84);\n    i0.ɵɵelementStart(2, \"h3\", 41);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 42);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 320; // Width of each brand card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4000; // 4 seconds for brands\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 287;\n    this.sectionComments = 89;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      this.featuredBrands = brands;\n      this.isLoading = false;\n      this.updateSliderOnBrandsLoad();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 768) {\n      this.cardWidth = 280;\n      this.visibleCards = 1;\n    } else if (width <= 1200) {\n      this.cardWidth = 320;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 340;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when brands load\n  updateSliderOnBrandsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for featured brands section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Featured Brands',\n        text: 'Check out these amazing featured fashion brands!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for featured brands');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 6,\n      consts: [[1, \"featured-brands-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"brands-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"empty-state\"], [\"name\", \"storefront-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"brands-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"brands-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, FeaturedBrandsComponent_div_9_Template, 3, 2, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 7, 1, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 6, 0, \"div\", 9)(12, FeaturedBrandsComponent_div_12_Template, 8, 6, \"div\", 10)(13, FeaturedBrandsComponent_div_13_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon, CarouselModule],\n      styles: [\".featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.brands-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.brands-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n  flex: 0 0 300px;\\n  width: 300px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    flex: 0 0 280px;\\n    width: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .brands-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .brands-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    flex: 0 0 260px;\\n    width: 260px;\\n  }\\n}\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 20px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 12px 0;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ffd700;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 16px 0;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 2px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 140px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100px;\\n  object-fit: cover;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 12px !important;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "FeaturedBrandsComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "FeaturedBrandsComponent_div_1_Template_button_click_5_listener", "openComments", "FeaturedBrandsComponent_div_1_Template_button_click_9_listener", "shareSection", "FeaturedBrandsComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "FeaturedBrandsComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "FeaturedBrandsComponent_div_9_div_2_div_5_Template", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_9_div_2_Template", "_c0", "FeaturedBrandsComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "formatPrice", "product_r8", "originalPrice", "star_r9", "rating", "average", "FeaturedBrandsComponent_div_12_div_7_div_25_Template_div_click_0_listener", "$event", "_r7", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_12_div_7_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_12_div_7_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_12_div_7_div_25_span_14_Template", "FeaturedBrandsComponent_div_12_div_7_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_12_div_7_Template_div_click_0_listener", "brand_r6", "_r5", "onBrandClick", "FeaturedBrandsComponent_div_12_div_7_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_12_Template_button_click_1_listener", "_r4", "slidePrev", "FeaturedBrandsComponent_div_12_Template_button_click_3_listener", "slideNext", "FeaturedBrandsComponent_div_12_Template_div_mouseenter_5_listener", "pauseAutoSlide", "FeaturedBrandsComponent_div_12_Template_div_mouseleave_5_listener", "resumeAutoSlide", "FeaturedBrandsComponent_div_12_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "featuredBrands$", "subscribe", "brands", "updateSliderOnBrandsLoad", "likedProducts$", "_this", "_asyncToGenerator", "console", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "share", "title", "text", "href", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_1_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_11_Template", "FeaturedBrandsComponent_div_12_Template", "FeaturedBrandsComponent_div_13_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './featured-brands.component.html',\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  featuredBrands: FeaturedBrand[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 320; // Width of each brand card including margin\n  visibleCards = 3; // Number of cards visible at once\n  maxSlide = 0;\n\n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 4000; // 4 seconds for brands\n  isAutoSliding = true;\n  isPaused = false;\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 287;\n  sectionComments = 89;\n  isMobile = false;\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private subscribeFeaturedBrands() {\n    this.subscription.add(\n      this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n        this.updateSliderOnBrandsLoad();\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadFeaturedBrands() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadFeaturedBrands();\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      this.error = 'Failed to load featured brands';\n      this.isLoading = false;\n    }\n  }\n\n  onBrandClick(brand: FeaturedBrand) {\n    this.router.navigate(['/products'], { \n      queryParams: { brand: brand.brand } \n    });\n  }\n\n  onProductClick(product: Product, event: Event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\n    return brand.brand;\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 768) {\n      this.cardWidth = 280;\n      this.visibleCards = 1;\n    } else if (width <= 1200) {\n      this.cardWidth = 320;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 340;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when brands load\n  private updateSliderOnBrandsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for featured brands section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Featured Brands',\n        text: 'Check out these amazing featured fashion brands!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for featured brands');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n}\n", "<div class=\"featured-brands-container\">\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\n        Featured Brands\n      </h2>\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\n        <div class=\"loading-header\">\n          <div class=\"loading-brand-name\"></div>\n          <div class=\"loading-stats\"></div>\n        </div>\n        <div class=\"loading-products\">\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- No Data State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-state\">\n    <ion-icon name=\"storefront-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Featured brands will appear here when available</p>\n  </div>\n\n  <!-- Brands Slider -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <!-- Slider Wrapper -->\n    <div class=\"brands-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"brands-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n    <div \n      *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\" \n      class=\"brand-card\"\n      (click)=\"onBrandClick(brand)\"\n    >\n      <!-- Brand Header -->\n      <div class=\"brand-header\">\n        <div class=\"brand-info\">\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\n          <div class=\"brand-stats\">\n            <div class=\"stat-item\">\n              <ion-icon name=\"bag-outline\"></ion-icon>\n              <span>{{ brand.productCount }} Products</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"star\"></ion-icon>\n              <span>{{ brand.avgRating }}/5</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"brand-badge\">\n          <ion-icon name=\"diamond\"></ion-icon>\n          Featured\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products\">\n        <h4 class=\"products-title\">Top Products</h4>\n        <div class=\"products-list\">\n          <div \n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \n            class=\"product-item\"\n            (click)=\"onProductClick(product, $event)\"\n          >\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Action Buttons -->\n              <div class=\"product-actions\">\n                <button\n                  class=\"action-btn like-btn\"\n                  [class.liked]=\"isProductLiked(product._id)\"\n                  (click)=\"onLikeProduct(product, $event)\"\n                  [attr.aria-label]=\"'Like ' + product.name\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn share-btn\" \n                  (click)=\"onShareProduct(product, $event)\"\n                  [attr.aria-label]=\"'Share ' + product.name\"\n                >\n                  <ion-icon name=\"share-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-details\">\n              <h5 class=\"product-name\">{{ product.name }}</h5>\n              <div class=\"product-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n              <div class=\"product-rating\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- View More Button -->\n      <div class=\"view-more-section\">\n        <button class=\"view-more-btn\">\n          <span>View All {{ brand.brand }} Products</span>\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n    </div>\n    </div> <!-- End brands-slider -->\n    </div> <!-- End brands-slider-wrapper -->\n  </div> <!-- End brands-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;ICL/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,+DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,+DAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,+DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,gEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,gEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA6BpE7B,EAAA,CAAAU,SAAA,cAAgE;;;;;IALlEV,EADF,CAAAC,cAAA,cAA+D,cACjC;IAE1BD,EADA,CAAAU,SAAA,cAAsC,cACL;IACnCV,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAA8B,UAAA,IAAAC,kDAAA,kBAA0D;IAE9D/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAFoBZ,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCjC,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAI,4CAAA,kBAA+D;IAUnElC,EADE,CAAAY,YAAA,EAAM,EACF;;;IAVoBZ,EAAA,CAAAqB,SAAA,GAAY;IAAZrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCnC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAkC,gEAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgC,OAAA,EAAS;IAAA,EAAC;IAC3CtC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAiC,KAAA,CAAW;;;;;IAQtCvC,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAAU,SAAA,mBAAkE;IAClEV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,yBAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,sDAA+C;IAC1EX,EAD0E,CAAAY,YAAA,EAAI,EACxE;;;;;IAsFQZ,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAkC,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAF,UAAA,CAAAG,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAF,UAAA,CAAAG,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7C,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,0EAAAC,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,cAAA,CAAAT,UAAA,EAAAM,MAAA,CAA+B;IAAA,EAAC;IAEzC/C,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAIAV,EADF,CAAAC,cAAA,cAA6B,iBAM1B;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,6EAAAJ,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,aAAA,CAAAX,UAAA,EAAAM,MAAA,CAA8B;IAAA,EAAC;IAGxC/C,EAAA,CAAAU,SAAA,mBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAmD,6EAAAN,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAAb,UAAA,EAAAM,MAAA,CAA+B;IAAA,EAAC;IAGzC/C,EAAA,CAAAU,SAAA,mBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAGJZ,EADF,CAAAC,cAAA,cAA6B,aACF;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAAyB,4DAAA,mBAC6B;IAC/BvD,EAAA,CAAAY,YAAA,EAAM;IAEJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA0B,gEAAA,uBAIC;IACHxD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IAG7DX,EAH6D,CAAAY,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAZ,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAiB,UAAA,CAAAgB,MAAA,IAAAC,GAAA,EAAA1D,EAAA,CAAA2D,aAAA,CAA6B,QAAAlB,UAAA,CAAAgB,MAAA,IAAAG,GAAA,IAAAnB,UAAA,CAAAoB,IAAA,CACgB;IAS3C7D,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAAwD,cAAA,CAAArB,UAAA,CAAAsB,GAAA,EAA2C;;IAIjC/D,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAwD,cAAA,CAAArB,UAAA,CAAAsB,GAAA,8BAAgE;IAK1E/D,EAAA,CAAAqB,SAAA,EAA2C;;IAQtBrB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAgB,UAAA,CAAAoB,IAAA,CAAkB;IAEb7D,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAkC,WAAA,CAAAC,UAAA,CAAAuB,KAAA,EAAgC;IACrDhE,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAuB,KAAA,CAAoE;IAMtDhE,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAAiC,GAAA,EAAc;IAKRjE,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAkE,kBAAA,MAAAzB,UAAA,CAAAG,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAlFnEnE,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkE,mEAAA;MAAA,MAAAC,QAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,GAAA,EAAArB,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,YAAA,CAAAF,QAAA,CAAmB;IAAA,EAAC;IAKzBrE,EAFJ,CAAAC,cAAA,cAA0B,cACA,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE3CZ,EADF,CAAAC,cAAA,cAAyB,cACA;IACrBD,EAAA,CAAAU,SAAA,mBAAwC;IACxCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,GAAiC;IACzCX,EADyC,CAAAY,YAAA,EAAO,EAC1C;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,SAAA,oBAAiC;IACjCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAAuB;IAC/BX,EAD+B,CAAAY,YAAA,EAAO,EAChC;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,SAAA,oBAAwC;IACxCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAA0C;IAGtDX,EAHsD,CAAAY,YAAA,EAAO,EACnD,EACF,EACF;IACNZ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAU,SAAA,oBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAM,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,cACG;IAAAD,EAAA,CAAAW,MAAA,oBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAA8B,UAAA,KAAA0C,oDAAA,oBAIC;IAiDLxE,EADE,CAAAY,YAAA,EAAM,EACF;IAKFZ,EAFJ,CAAAC,cAAA,eAA+B,kBACC,YACtB;IAAAD,EAAA,CAAAW,MAAA,IAAmC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChDZ,EAAA,CAAAU,SAAA,oBAA4C;IAGlDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;;;IAxFuBZ,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAyB,iBAAA,CAAA4C,QAAA,CAAAI,KAAA,CAAiB;IAI9BzE,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAkE,kBAAA,KAAAG,QAAA,CAAAK,YAAA,cAAiC;IAIjC1E,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAkE,kBAAA,KAAAG,QAAA,CAAAM,SAAA,OAAuB;IAIvB3E,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAkE,kBAAA,KAAA5D,MAAA,CAAAsE,YAAA,CAAAP,QAAA,CAAAQ,UAAA,YAA0C;IAe9B7E,EAAA,CAAAqB,SAAA,GAAsB;IAAArB,EAAtB,CAAAwB,UAAA,YAAA6C,QAAA,CAAAS,WAAA,CAAsB,iBAAAxE,MAAA,CAAAyE,gBAAA,CAAyB;IAyD/D/E,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAkE,kBAAA,cAAAG,QAAA,CAAAI,KAAA,cAAmC;;;;;;IAtG/CzE,EAFF,CAAAC,cAAA,cAA+F,iBAEH;IAAtDD,EAAA,CAAAE,UAAA,mBAAA8E,gEAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,SAAA,EAAW;IAAA,EAAC;IACvDlF,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAiF,gEAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8E,SAAA,EAAW;IAAA,EAAC;IACvDpF,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAoG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAAmF,kEAAA;MAAArF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAgF,cAAA,EAAgB;IAAA,EAAC,wBAAAC,kEAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAkF,eAAA,EAAiB;IAAA,EAAC;IACjGxF,EAAA,CAAAC,cAAA,cAAmF;IACrFD,EAAA,CAAA8B,UAAA,IAAA2D,6CAAA,mBAIC;IA+FHzF,EAFE,CAAAY,YAAA,EAAM,EACA,EACF;;;;IA7GsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoF,YAAA,OAA+B;IAG/B1F,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoF,YAAA,IAAApF,MAAA,CAAAqF,QAAA,CAAqC;IAMlE3F,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAA4F,WAAA,8BAAAtF,MAAA,CAAAuF,WAAA,SAAuD;IAEhE7F,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAwF,cAAA,CAAmB,iBAAAxF,MAAA,CAAAyF,gBAAA,CAAyB;;;;;IAqGlE/F,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA+D;IAC/DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,yBAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,sDAA+C;IAC1EX,EAD0E,CAAAY,YAAA,EAAI,EACxE;;;ADjLR,OAAM,MAAOoF,uBAAuB;EA2BlCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IA7BhB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAA9D,KAAK,GAAkB,IAAI;IAC3B,KAAA+D,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3G,YAAY,EAAE;IAEvD;IACA,KAAA6F,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAY,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAf,QAAQ,GAAG,CAAC;IAIZ,KAAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAtF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAkF,QAAQ,GAAG,KAAK;EAMb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,YAAY,CAACe,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQP,uBAAuBA,CAAA;IAC7B,IAAI,CAACT,YAAY,CAACiB,GAAG,CACnB,IAAI,CAACvB,eAAe,CAACwB,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtD,IAAI,CAAC9B,cAAc,GAAG8B,MAAM;MAC5B,IAAI,CAACvB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACwB,wBAAwB,EAAE;IACjC,CAAC,CAAC,CACH;EACH;EAEQX,sBAAsBA,CAAA;IAC5B,IAAI,CAACV,YAAY,CAACiB,GAAG,CACnB,IAAI,CAACtB,aAAa,CAAC2B,cAAc,CAACH,SAAS,CAACrB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcU,kBAAkBA,CAAA;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAC1B,SAAS,GAAG,IAAI;QACrB0B,KAAI,CAACxF,KAAK,GAAG,IAAI;QACjB,MAAMwF,KAAI,CAAC7B,eAAe,CAACc,kBAAkB,EAAE;OAChD,CAAC,OAAOzE,KAAK,EAAE;QACd0F,OAAO,CAAC1F,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDwF,KAAI,CAACxF,KAAK,GAAG,gCAAgC;QAC7CwF,KAAI,CAAC1B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA9B,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAAC2B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAE1D,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAACkF,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAClC,MAAM,CAAC8B,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAACrE,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAACgF,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAP,iBAAA;MAChDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACpC,aAAa,CAACsC,WAAW,CAACL,OAAO,CAACrE,GAAG,CAAC;QAChE,IAAIyE,MAAM,CAACE,OAAO,EAAE;UAClBT,OAAO,CAACU,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLX,OAAO,CAAC1F,KAAK,CAAC,yBAAyB,EAAEiG,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOrG,KAAK,EAAE;QACd0F,OAAO,CAAC1F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMe,cAAcA,CAAC8E,OAAgB,EAAEC,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAb,iBAAA;MACjDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAACrE,GAAG,EAAE;QACrE,MAAMmF,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC1C,aAAa,CAACkD,YAAY,CAACjB,OAAO,CAACrE,GAAG,EAAE;UACjDuF,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BR,OAAO,CAACvE,IAAI,SAASuE,OAAO,CAAC3D,KAAK;SACtE,CAAC;QAEFwD,OAAO,CAACU,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOpG,KAAK,EAAE;QACd0F,OAAO,CAAC1F,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACwB,KAAa;IACvB,OAAO,IAAIuF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC5F,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAACiF,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAzH,OAAOA,CAAA;IACL,IAAI,CAAC0E,kBAAkB,EAAE;EAC3B;EAEAjB,gBAAgBA,CAACiE,KAAa,EAAEvF,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAACmG,SAAiB;IAC9B,OAAO,IAAI,CAAC3D,aAAa,CAAC4D,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAlF,gBAAgBA,CAACiF,KAAa,EAAE5B,OAAgB;IAC9C,OAAOA,OAAO,CAACrE,GAAG;EACpB;EAEA;EACQoG,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvD,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACW,aAAa,EAAE;IACpB,IAAI,CAAC4C,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACxD,QAAQ,IAAI,IAAI,CAACf,cAAc,CAACwE,MAAM,GAAG,IAAI,CAAC5D,YAAY,EAAE;QACpE,IAAI,CAAC6D,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC5D,cAAc,CAAC;EACzB;EAEQa,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC4C,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC7E,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC+E,iBAAiB,EAAE;EAC1B;EAEAnF,cAAcA,CAAA;IACZ,IAAI,CAACuB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACW,aAAa,EAAE;EACtB;EAEAhC,eAAeA,CAAA;IACb,IAAI,CAACqB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsD,cAAc,EAAE;EACvB;EAEA;EACQhD,wBAAwBA,CAAA;IAC9B,MAAMuD,KAAK,GAAG3B,MAAM,CAAC4B,UAAU;IAC/B,IAAID,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAACjE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIgE,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAACjE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACkE,kBAAkB,EAAE;IACzB,IAAI,CAACH,iBAAiB,EAAE;EAC1B;EAEQrD,mBAAmBA,CAAA;IACzB2B,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC1D,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACAyD,kBAAkBA,CAAA;IAChB,IAAI,CAACjF,QAAQ,GAAGmF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjF,cAAc,CAACwE,MAAM,GAAG,IAAI,CAAC5D,YAAY,CAAC;EAC7E;EAEAxB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC+E,iBAAiB,EAAE;MACxB,IAAI,CAACO,gCAAgC,EAAE;;EAE3C;EAEA5F,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC+E,iBAAiB,EAAE;MACxB,IAAI,CAACO,gCAAgC,EAAE;;EAE3C;EAEQP,iBAAiBA,CAAA;IACvB,IAAI,CAAC5E,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACe,SAAS;EACxD;EAEQuE,gCAAgCA,CAAA;IACtC,IAAI,CAACxD,aAAa,EAAE;IACpByD,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQtC,wBAAwBA,CAAA;IAC9BoD,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAClF,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACsE,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA1J,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACVmH,OAAO,CAACU,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEA3H,YAAYA,CAAA;IACV,IAAIkI,SAAS,CAACgC,KAAK,EAAE;MACnBhC,SAAS,CAACgC,KAAK,CAAC;QACdC,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,kDAAkD;QACxD1H,GAAG,EAAEqF,MAAM,CAACC,QAAQ,CAACqC;OACtB,CAAC;KACH,MAAM;MACLnC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACqC,IAAI,CAAC;MACnDpD,OAAO,CAACU,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAvH,eAAeA,CAAA;IACb6G,OAAO,CAACU,GAAG,CAAC,0CAA0C,CAAC;EACzD;EAEAjH,WAAWA,CAACyC,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAE2F,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAI3F,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAE2F,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAO3F,KAAK,CAAC4F,QAAQ,EAAE;EACzB;EAEQ1C,iBAAiBA,CAAA;IACvB,IAAI,CAACP,QAAQ,GAAGiC,MAAM,CAAC4B,UAAU,IAAI,GAAG;EAC1C;;;uBA7SW3E,uBAAuB,EAAAhG,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB5F,uBAAuB;MAAA6F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/L,EAAA,CAAAgM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBpCtM,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAA0K,sCAAA,kBAAoD;UAiChDxM,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAuD;UACvDV,EAAA,CAAAW,MAAA,wBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,0CAAmC;UAEnEX,EAFmE,CAAAY,YAAA,EAAI,EAC/D,EACF;UAqJNZ,EAlJA,CAAA8B,UAAA,IAAA2K,sCAAA,iBAAiD,KAAAC,uCAAA,iBAeQ,KAAAC,uCAAA,iBAU4B,KAAAC,uCAAA,kBAOU,KAAAC,uCAAA,kBAkHN;UAK3F7M,EAAA,CAAAY,YAAA,EAAM;;;UAjMgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAzF,QAAA,CAAc;UA0C5C9G,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAlG,SAAA,CAAe;UAefrG,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAhK,KAAA,KAAAgK,GAAA,CAAAlG,SAAA,CAAyB;UAUzBrG,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+K,GAAA,CAAAlG,SAAA,KAAAkG,GAAA,CAAAhK,KAAA,IAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,OAAyD;UAOzDtK,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAA+K,GAAA,CAAAlG,SAAA,KAAAkG,GAAA,CAAAhK,KAAA,IAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,KAAuD;UAkHvDtK,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+K,GAAA,CAAAlG,SAAA,KAAAkG,GAAA,CAAAhK,KAAA,IAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,OAAyD;;;qBDjLrD1K,YAAY,EAAAkN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElN,WAAW,EAAAmN,EAAA,CAAAC,OAAA,EAAEnN,cAAc;MAAAoN,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}