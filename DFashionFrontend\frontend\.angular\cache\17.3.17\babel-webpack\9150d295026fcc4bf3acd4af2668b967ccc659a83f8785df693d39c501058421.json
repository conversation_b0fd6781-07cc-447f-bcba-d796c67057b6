{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"h3\", 9);\n    i0.ɵɵtext(2, \"Stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 10);\n    i0.ɵɵtext(4, \"Watch stories from people you follow\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 14)(2, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_2_div_1_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_3_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 31)(3, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_3_ng_container_13_ng_template_1_Template, 6, 5, \"ng-template\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"div\", 20)(5, \"div\", 21);\n    i0.ɵɵelement(6, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 24);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 25)(11, \"div\", 26)(12, \"owl-carousel-o\", 27);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_3_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_3_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_3_ng_container_13_Template, 2, 0, \"ng-container\", 28);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 66);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 67);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 72);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 73)(4, \"div\", 74);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_4_div_21_div_1_Template, 8, 2, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 81);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 55);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 22);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34, 0)(3, \"div\", 35);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_4_div_4_Template, 2, 4, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_4_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_4_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_4_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 38)(7, \"div\", 39);\n    i0.ɵɵelement(8, \"div\", 40);\n    i0.ɵɵelementStart(9, \"div\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 42);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 43);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 46);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_4_video_18_Template, 1, 1, \"video\", 47)(19, ViewAddStoriesComponent_div_4_div_19_Template, 1, 2, \"div\", 48)(20, ViewAddStoriesComponent_div_4_div_20_Template, 2, 1, \"div\", 49)(21, ViewAddStoriesComponent_div_4_div_21_Template, 2, 1, \"div\", 50)(22, ViewAddStoriesComponent_div_4_div_22_Template, 5, 0, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 52)(24, \"div\", 53)(25, \"button\", 54);\n    i0.ɵɵelement(26, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 56);\n    i0.ɵɵelement(28, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 58);\n    i0.ɵɵelement(30, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_4_div_31_Template, 13, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 61)(33, \"div\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 63, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵelement(2, \"i\", 86);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 87)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 88);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ViewAddStoriesComponent = /*#__PURE__*/(() => {\n  class ViewAddStoriesComponent {\n    constructor(router, http, cartService, wishlistService) {\n      this.router = router;\n      this.http = http;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      // Mobile detection\n      this.isMobile = false;\n      this.stories = [];\n      this.showAddStory = true;\n      this.currentUser = null;\n      this.storyClick = new EventEmitter();\n      this.isLoadingStories = true;\n      this.currentIndex = 0;\n      this.isOpen = false;\n      this.isRotating = false;\n      this.isDragging = false;\n      this.rotateY = 0;\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n      // Touch/drag properties\n      this.dragStartX = 0;\n      this.dragCurrentX = 0;\n      this.minDragPercentToTransition = 0.5;\n      this.minVelocityToTransition = 0.65;\n      this.transitionSpeed = 6;\n      // Carousel state properties\n      this.isCarouselInitialized = false;\n      this.isAutoPlaying = true;\n      this.currentSlideIndex = 0;\n      // Owl Carousel Options\n      this.customOptions = {\n        loop: true,\n        mouseDrag: true,\n        touchDrag: true,\n        pullDrag: false,\n        dots: false,\n        navSpeed: 700,\n        navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n        responsive: {\n          0: {\n            items: 3,\n            nav: false\n          },\n          400: {\n            items: 4,\n            nav: false\n          },\n          740: {\n            items: 5,\n            nav: true\n          },\n          940: {\n            items: 6,\n            nav: true\n          }\n        },\n        nav: true,\n        margin: 2,\n        stagePadding: 0,\n        autoplay: true,\n        autoplayTimeout: 4000,\n        autoplayHoverPause: true,\n        autoplaySpeed: 1000 // Animation speed for auto sliding\n      };\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Check screen size for mobile detection\n      this.checkScreenSize();\n      // Only load stories if none are provided as input\n      if (!this.stories || this.stories.length === 0) {\n        this.loadStories();\n      } else {\n        this.isLoadingStories = false;\n      }\n      this.setupEventListeners();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.removeEventListeners();\n    }\n    loadStories() {\n      this.isLoadingStories = true;\n      // Use mock stories data for now since stories API is not implemented\n      this.stories = [{\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n        views: 1250,\n        isActive: true,\n        isViewed: false\n      }, {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n        views: 2340,\n        isActive: true,\n        isViewed: false\n      }, {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n        views: 1890,\n        isActive: true,\n        isViewed: false\n      }, {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }];\n      this.isLoadingStories = false;\n    }\n    // Removed fallback stories - only use database data\n    getCurrentStory() {\n      return this.stories[this.currentIndex] || this.stories[0];\n    }\n    getTimeAgo(dateString) {\n      const now = new Date();\n      const date = new Date(dateString);\n      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n      if (diffInMinutes < 1) return 'now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m`;\n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      return `${diffInDays}d`;\n    }\n    formatNumber(num) {\n      if (!num || num === undefined || num === null) {\n        return '0';\n      }\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    openStories(index = 0) {\n      this.currentIndex = index;\n      this.isOpen = true;\n      this.showStory(index);\n      document.body.style.overflow = 'hidden';\n      // Emit story click event\n      if (this.stories[index]) {\n        this.storyClick.emit({\n          story: this.stories[index],\n          index\n        });\n      }\n    }\n    closeStories() {\n      this.isOpen = false;\n      this.pauseAllVideos();\n      document.body.style.overflow = 'auto';\n      // Add closing animation\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.add('is-closed');\n      }\n      setTimeout(() => {\n        if (this.storiesContainer) {\n          this.storiesContainer.nativeElement.classList.remove('is-closed');\n        }\n      }, 300);\n    }\n    showStory(index) {\n      this.currentIndex = index;\n      this.rotateY = 0;\n      // Reset container transform\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n      }\n    }\n    nextStory() {\n      if (this.currentIndex < this.stories.length - 1) {\n        this.targetRotateY = -90;\n        this.targetDirection = 'forward';\n        this.isRotating = true;\n        this.update();\n      } else {\n        this.closeStories();\n      }\n    }\n    previousStory() {\n      if (this.currentIndex > 0) {\n        this.targetRotateY = 90;\n        this.targetDirection = 'back';\n        this.isRotating = true;\n        this.update();\n      } else {\n        this.closeStories();\n      }\n    }\n    handleKeydown(event) {\n      if (!this.isOpen) return;\n      switch (event.key) {\n        case 'ArrowLeft':\n          this.previousStory();\n          break;\n        case 'ArrowRight':\n          this.nextStory();\n          break;\n        case 'Escape':\n          this.closeStories();\n          break;\n      }\n    }\n    onStoryClick(event) {\n      if (this.isRotating) return;\n      const rect = event.target.getBoundingClientRect();\n      const clickX = event.clientX - rect.left;\n      const width = rect.width;\n      if (clickX < width / 2) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    }\n    onTouchStart(event) {\n      this.isDragging = true;\n      this.dragStartX = event.touches[0].clientX;\n      this.dragCurrentX = this.dragStartX;\n    }\n    onTouchMove(event) {\n      if (!this.isDragging) return;\n      this.dragCurrentX = event.touches[0].clientX;\n      const dragDistance = this.dragCurrentX - this.dragStartX;\n      const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n      if (dragPercent > this.minDragPercentToTransition) {\n        if (dragDistance > 0) {\n          this.previousStory();\n        } else {\n          this.nextStory();\n        }\n        this.isDragging = false;\n      }\n    }\n    onTouchEnd(_event) {\n      this.isDragging = false;\n    }\n    setupEventListeners() {\n      // Add any additional event listeners here\n    }\n    removeEventListeners() {\n      // Remove any additional event listeners here\n    }\n    pauseAllVideos() {\n      const videos = document.querySelectorAll('video');\n      videos.forEach(video => {\n        video.pause();\n      });\n    }\n    update() {\n      if (!this.isRotating) return;\n      const diff = this.targetRotateY - this.rotateY;\n      this.rotateY += diff * 0.1;\n      if (Math.abs(diff) < 0.1) {\n        this.rotateY = this.targetRotateY;\n        this.isRotating = false;\n        if (this.targetDirection === 'forward') {\n          this.currentIndex++;\n        } else if (this.targetDirection === 'back') {\n          this.currentIndex--;\n        }\n        this.targetRotateY = 0;\n        this.targetDirection = null;\n      }\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n      }\n      if (this.isRotating) {\n        requestAnimationFrame(() => this.update());\n      }\n    }\n    hasProducts() {\n      const story = this.getCurrentStory();\n      return !!(story?.products && story.products.length > 0);\n    }\n    getStoryProducts() {\n      return this.getCurrentStory().products || [];\n    }\n    formatPrice(price) {\n      return `₹${(price / 100).toLocaleString('en-IN')}`;\n    }\n    viewProductDetails(product) {\n      console.log('Viewing product:', product);\n      // Navigate to product page or show product modal\n      this.router.navigate(['/products', product._id]);\n    }\n    getCurrentUserAvatar() {\n      // Use currentUser input if available, otherwise return default avatar\n      return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n    }\n    openAddStoryModal() {\n      console.log('Opening add story modal');\n      // Navigate to add story page or open modal\n      this.router.navigate(['/add-story']);\n    }\n    buyNow() {\n      const products = this.getStoryProducts();\n      if (products.length > 0) {\n        const product = products[0]; // Get first product for now\n        console.log('Buying product:', product);\n        // Navigate to checkout with product\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: product._id,\n            source: 'story'\n          }\n        });\n      }\n    }\n    // Direct product navigation\n    viewProduct(productId) {\n      // Track product click analytics\n      this.trackProductClick(productId, 'view_product');\n      // Navigate to product detail page\n      this.router.navigate(['/shop/product', productId]);\n    }\n    viewCategory(categoryId) {\n      // Navigate to category page\n      this.router.navigate(['/shop/category', categoryId]);\n    }\n    trackProductClick(productId, action) {\n      // Track analytics for product clicks from stories\n      console.log(`Story product ${action} tracked:`, productId);\n      // TODO: Implement analytics tracking API call\n    }\n    addToWishlist() {\n      const products = this.getStoryProducts();\n      if (products.length > 0) {\n        const product = products[0];\n        console.log('Adding to wishlist:', product);\n        this.wishlistService.addToWishlist(product._id).subscribe({\n          next: response => {\n            if (response.success) {\n              alert('Product added to wishlist!');\n            } else {\n              alert('Failed to add product to wishlist');\n            }\n          },\n          error: error => {\n            console.error('Error adding to wishlist:', error);\n            alert('Error adding product to wishlist');\n          }\n        });\n      }\n    }\n    addToCart() {\n      const products = this.getStoryProducts();\n      if (products.length > 0) {\n        const product = products[0];\n        console.log('Adding to cart:', product);\n        this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n          next: response => {\n            if (response.success) {\n              alert('Product added to cart!');\n            } else {\n              alert('Failed to add product to cart');\n            }\n          },\n          error: error => {\n            console.error('Error adding to cart:', error);\n            alert('Error adding product to cart');\n          }\n        });\n      }\n    }\n    // Owl Carousel Event Handlers\n    onSlideChanged(event) {\n      // Handle slide change events\n      if (event && event.startPosition !== undefined) {\n        this.currentSlideIndex = event.startPosition;\n        // Log slide change for debugging\n        console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n        // Update any slide-specific logic here\n        this.updateSlideAnalytics();\n      }\n    }\n    onInitialized(_event) {\n      // Handle carousel initialization\n      this.isCarouselInitialized = true;\n      console.log('Stories carousel initialized successfully with auto-sliding enabled');\n    }\n    // Analytics for slide changes\n    updateSlideAnalytics() {\n      // Track slide interactions for analytics\n      if (this.stories && this.stories[this.currentSlideIndex]) {\n        const currentStory = this.stories[this.currentSlideIndex];\n        console.log(`Viewing story from: ${currentStory.user.username}`);\n      }\n    }\n    // Method to toggle auto-play (can be called from template if needed)\n    toggleAutoPlay() {\n      this.isAutoPlaying = !this.isAutoPlaying;\n      // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n      // This would require reinitializing the carousel with new options\n      console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n    }\n    // Mobile detection method\n    checkScreenSize() {\n      const width = window.innerWidth;\n      const userAgent = navigator.userAgent;\n      const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n      // Consider it mobile if width <= 768px OR if it's a mobile user agent\n      this.isMobile = width <= 768 || isMobileUserAgent;\n    }\n    onResize() {\n      this.checkScreenSize();\n    }\n    static {\n      this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n        return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ViewAddStoriesComponent,\n        selectors: [[\"app-view-add-stories\"]],\n        viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n          }\n        },\n        hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n              return ctx.handleKeydown($event);\n            }, false, i0.ɵɵresolveDocument)(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n              return ctx.onResize($event);\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        inputs: {\n          stories: \"stories\",\n          showAddStory: \"showAddStory\",\n          currentUser: \"currentUser\"\n        },\n        outputs: {\n          storyClick: \"storyClick\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 6,\n        vars: 5,\n        consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-header\", 4, \"ngIf\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"stories-subtitle\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n        template: function ViewAddStoriesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 2);\n            i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 5, 0, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 2, 2, \"div\", 4)(3, ViewAddStoriesComponent_div_3_Template, 14, 4, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_4_Template, 36, 17, \"div\", 6)(5, ViewAddStoriesComponent_div_5_Template, 9, 0, \"div\", 7);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n        styles: [\".stories-container[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;padding:20px;margin-bottom:24px;width:100%;box-shadow:0 2px 8px #0000001a}@media (min-width: 769px){.stories-container[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;padding:24px;margin-bottom:24px;box-shadow:0 2px 8px #0000001a;position:relative;z-index:10}}@media (max-width: 768px){.stories-container[_ngcontent-%COMP%]{padding:12px 0;border:none;border-radius:0;border-bottom:1px solid #efefef;background:#fafafa;box-shadow:none;margin-bottom:0}}.stories-header[_ngcontent-%COMP%]{margin-bottom:20px}@media (min-width: 769px){.stories-header[_ngcontent-%COMP%]{border-bottom:1px solid #efefef;padding-bottom:16px;margin-bottom:24px}}.stories-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#262626;margin:0 0 4px}@media (min-width: 769px){.stories-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700}}.stories-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e;margin:0}@media (min-width: 769px){.stories-subtitle[_ngcontent-%COMP%]{font-size:15px}}.stories-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;padding:0}@media (min-width: 769px){.stories-section[_ngcontent-%COMP%]{padding:0;gap:20px;align-items:center;min-height:120px}}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]{padding:0 16px;gap:16px}}.add-story-static[_ngcontent-%COMP%]{flex-shrink:0;width:82px}.stories-slider-wrapper[_ngcontent-%COMP%]{flex:1;overflow:hidden;max-width:calc(100% - 98px);position:relative}.stories-slider-wrapper[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;right:0;width:20px;height:100%;background:linear-gradient(to left,rgba(255,255,255,.8),transparent);pointer-events:none;z-index:5;opacity:0;transition:opacity .3s ease}.stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]:after{opacity:1}.stories-slider-container[_ngcontent-%COMP%]{position:relative;width:100%;overflow:visible}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage{display:flex;align-items:flex-start}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item{display:flex;justify-content:center;align-items:flex-start;min-height:120px}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{position:absolute;top:50%;transform:translateY(-50%);width:100%;pointer-events:none}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{position:absolute;top:50%;transform:translateY(-50%);width:40px;height:40px;border-radius:50%;background:#00000080;color:#fff;border:none;display:none;align-items:center;justify-content:center;cursor:pointer;z-index:10;transition:all .2s ease;box-shadow:0 2px 8px #0003;pointer-events:all}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover{background:#000000b3;box-shadow:0 4px 12px #0000004d;transform:translateY(-50%) scale(1.1)}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active{transform:translateY(-50%) scale(.95)}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas{font-size:14px;color:#fff}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-20px}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-20px}@media (max-width: 768px){.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{display:none}}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer{position:relative}.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;right:0;height:2px;background:linear-gradient(90deg,transparent 0%,#405de6 50%,transparent 100%);opacity:.3;animation:_ngcontent-%COMP%_autoSlideIndicator 4s infinite linear}@keyframes _ngcontent-%COMP%_autoSlideIndicator{0%{transform:translate(-100%)}to{transform:translate(100%)}}.story-slide[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:transform .2s ease;width:66px}.story-slide[_ngcontent-%COMP%]:hover{transform:scale(1.05);animation-play-state:paused}.slider-nav-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);width:32px;height:32px;border-radius:50%;background:#ffffffe6;border:1px solid rgba(0,0,0,.1);display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:10;transition:all .2s ease;box-shadow:0 2px 8px #0000001a}.slider-nav-btn[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translateY(-50%) scale(1.1)}.slider-nav-btn[_ngcontent-%COMP%]:active{transform:translateY(-50%) scale(.95)}.slider-nav-btn.hidden[_ngcontent-%COMP%]{opacity:0;pointer-events:none}.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#262626}.slider-nav-left[_ngcontent-%COMP%]{left:-16px}.slider-nav-right[_ngcontent-%COMP%]{right:-16px}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;flex-shrink:0;transition:all .3s ease;width:82px;min-width:82px;position:relative}@media (min-width: 769px){.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:90px;min-width:90px;padding:8px;border-radius:12px}.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover{background:#0000000d;transform:scale(1.08)}}@media (max-width: 768px){.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:76px;min-width:76px}}.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%]{animation-duration:1s}.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%]{color:#0095f6;font-weight:600}.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active{transform:scale(.95)}.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);animation:_ngcontent-%COMP%_pulse 2s infinite}.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{color:#405de6;font-weight:600}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}to{transform:scale(1);opacity:1}}.story-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:8px}@media (max-width: 768px){.story-avatar-container[_ngcontent-%COMP%]{margin-bottom:6px}}.story-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;background-size:cover;background-position:center;border:2px solid #fff;box-shadow:0 2px 8px #0000001a;position:relative;z-index:2;transition:all .3s ease}@media (min-width: 769px){.story-avatar[_ngcontent-%COMP%]{width:74px;height:74px;border:3px solid #fff;box-shadow:0 4px 12px #00000026}.story-avatar[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 6px 16px #0003}}@media (max-width: 768px){.story-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border:1.5px solid #fff;box-shadow:0 1px 4px #0000001a}}.story-ring[_ngcontent-%COMP%]{position:absolute;top:-2px;left:-2px;width:70px;height:70px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:1;animation:_ngcontent-%COMP%_pulse 2s infinite}@media (min-width: 769px){.story-ring[_ngcontent-%COMP%]{top:-3px;left:-3px;width:80px;height:80px;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);box-shadow:0 0 20px #f094334d}}@media (max-width: 768px){.story-ring[_ngcontent-%COMP%]{width:64px;height:64px;top:-2px;left:-2px}}.story-ring.viewed[_ngcontent-%COMP%]{background:#c7c7c7;animation:none}.story-ring.active[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);animation:_ngcontent-%COMP%_pulse 1.5s infinite;box-shadow:0 0 10px #f0943380}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}.story-username[_ngcontent-%COMP%]{font-size:12px;color:#262626;font-weight:400;max-width:74px;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:1.2;margin-top:8px}@media (min-width: 769px){.story-username[_ngcontent-%COMP%]{font-size:13px;font-weight:500;max-width:90px;margin-top:10px;color:#262626;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}}@media (max-width: 768px){.story-username[_ngcontent-%COMP%]{font-size:11px;max-width:70px;font-weight:500;margin-top:6px}}.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-weight:600;color:#262626}.add-story-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;position:relative;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);display:flex;align-items:center;justify-content:center;z-index:2}.add-story-icon[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:20px;height:20px;background:#0095f6;border-radius:50%;display:flex;align-items:center;justify-content:center;border:2px solid white;z-index:3}.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:10px;font-weight:700}.current-user-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background-size:cover;background-position:center;border:2px solid white}.stories-loading[_ngcontent-%COMP%]{display:flex;gap:16px;padding:0 16px}.story-skeleton[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px}.skeleton-avatar[_ngcontent-%COMP%]{width:66px;height:66px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-name[_ngcontent-%COMP%]{width:60px;height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.story-bar__user[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:transform .2s ease}.story-bar__user[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.story-bar__user.bounce[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_bounce .3s ease}.story-bar__user-avatar[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;background-size:cover;background-position:center;border:3px solid transparent;background-clip:padding-box;position:relative}.story-bar__user-avatar[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-3px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-1}.story-bar__user-name[_ngcontent-%COMP%]{margin-top:4px;font-size:12px;color:#262626;text-align:center;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.stories-wrapper[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;z-index:9999;perspective:400px;overflow:hidden;opacity:0;visibility:hidden;transition:opacity .3s ease,visibility .3s ease}.stories-wrapper.is-open[_ngcontent-%COMP%]{opacity:1;visibility:visible}.stories[_ngcontent-%COMP%]{width:100%;height:100%;transform-style:preserve-3d;transform:translateZ(-50vw);transition:transform .25s ease-out}.stories.is-closed[_ngcontent-%COMP%]{opacity:0;transform:scale(.1)}.story-progress[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;right:8px;display:flex;gap:2px;z-index:100}.story-progress__bar[_ngcontent-%COMP%]{flex:1;height:2px;background:#ffffff4d;border-radius:1px;overflow:hidden}.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%]{width:100%}.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progress 15s linear}.story-progress__fill[_ngcontent-%COMP%]{height:100%;background:#fff;width:0%;transition:width .1s ease}@keyframes _ngcontent-%COMP%_progress{0%{width:0%}to{width:100%}}.story[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%;overflow:hidden;display:flex;flex-direction:column;-webkit-user-select:none;user-select:none}.story__top[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;padding:48px 16px 16px;background:linear-gradient(180deg,rgba(0,0,0,.6) 0%,transparent 100%);z-index:10;display:flex;justify-content:space-between;align-items:center}.story__details[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.story__avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-size:cover;background-position:center;border:2px solid #fff}.story__user[_ngcontent-%COMP%]{color:#fff;font-weight:600;font-size:14px}.story__time[_ngcontent-%COMP%]{color:#ffffffb3;font-size:12px}.story__views[_ngcontent-%COMP%]{color:#fff9;font-size:11px;margin-left:8px}.story__close[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:18px;cursor:pointer;padding:8px;border-radius:50%;transition:background .2s ease}.story__close[_ngcontent-%COMP%]:hover{background:#ffffff1a}.story__content[_ngcontent-%COMP%]{flex:1;position:relative;display:flex;align-items:center;justify-content:center;background:#000}.story__video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.story__image[_ngcontent-%COMP%]{width:100%;height:100%;background-size:cover;background-position:center;background-repeat:no-repeat}.story__caption[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:16px;right:16px;background:#0009;color:#fff;padding:12px 16px;border-radius:20px;font-size:14px;line-height:1.4;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:5}.story__product-tags[_ngcontent-%COMP%]{position:absolute;top:50%;left:20px;transform:translateY(-50%);z-index:6}.product-tag[_ngcontent-%COMP%]{background:#fffffff2;border-radius:12px;padding:8px 12px;margin-bottom:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);display:flex;align-items:center;gap:8px;min-width:160px}.product-tag[_ngcontent-%COMP%]:hover{transform:scale(1.05);background:#fff;box-shadow:0 4px 15px #0003}.product-tag-icon[_ngcontent-%COMP%]{font-size:16px}.product-tag-info[_ngcontent-%COMP%]{flex:1}.product-tag-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#333;margin-bottom:2px;line-height:1.2}.product-tag-price[_ngcontent-%COMP%]{font-size:11px;color:#666;font-weight:500}.story__bottom[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;padding:16px;background:linear-gradient(0deg,rgba(0,0,0,.6) 0%,transparent 100%);z-index:10}.story__actions[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:12px}.story__action-btn[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:20px;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.story__action-btn[_ngcontent-%COMP%]:hover{background:#ffffff1a;transform:scale(1.1)}.story__ecommerce-actions[_ngcontent-%COMP%]{display:flex;gap:8px;justify-content:center}.ecommerce-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;padding:8px 12px;border:none;border-radius:20px;font-size:12px;font-weight:600;cursor:pointer;transition:all .2s ease}.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#ee5a24);color:#fff}.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #ff6b6b66}.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff9ff3,#f368e0);color:#fff}.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #ff9ff366}.ecommerce-btn.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#54a0ff,#2e86de);color:#fff}.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #54a0ff66}.story__nav-area[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;width:33%;z-index:5;cursor:pointer}.story__nav-area.story__nav-prev[_ngcontent-%COMP%]{left:0}.story__nav-area.story__nav-next[_ngcontent-%COMP%]{right:0;width:67%}.feed__cover[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#fff;z-index:-1}.feed__cover.is-hidden[_ngcontent-%COMP%]{opacity:0}.touch-indicators[_ngcontent-%COMP%]{position:fixed;top:50%;left:0;right:0;transform:translateY(-50%);z-index:101;pointer-events:none;display:none}@media (max-width: 768px){.touch-indicators[_ngcontent-%COMP%]{display:block}}.touch-indicator[_ngcontent-%COMP%]{position:absolute;display:flex;align-items:center;gap:8px;color:#ffffffb3;font-size:12px;animation:_ngcontent-%COMP%_fadeInOut 3s infinite}.touch-indicator.left[_ngcontent-%COMP%]{left:16px}.touch-indicator.right[_ngcontent-%COMP%]{right:16px}@keyframes _ngcontent-%COMP%_fadeInOut{0%,to{opacity:0}50%{opacity:1}}@keyframes _ngcontent-%COMP%_bounce{0%,to{transform:scale(1)}50%{transform:scale(.8)}}@media (max-width: 1024px){.story-bar[_ngcontent-%COMP%]{padding:12px 16px;gap:10px;overflow-x:auto;scroll-behavior:smooth;-webkit-overflow-scrolling:touch}.stories-wrapper[_ngcontent-%COMP%]{touch-action:pan-y}.story[_ngcontent-%COMP%]{touch-action:manipulation}.stories-section[_ngcontent-%COMP%]{gap:12px;padding:0 12px}.add-story-static[_ngcontent-%COMP%]{width:70px}.stories-slider-wrapper[_ngcontent-%COMP%]{max-width:calc(100% - 82px)}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:70px;min-width:70px}.stories-list[_ngcontent-%COMP%]{gap:12px}.slider-nav-btn[_ngcontent-%COMP%]{display:none}}@media (max-width: 768px){.story-bar[_ngcontent-%COMP%]{padding:8px 12px;gap:8px;scrollbar-width:none;-ms-overflow-style:none}.story-bar[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.stories-section[_ngcontent-%COMP%]{gap:10px;padding:0 8px}.add-story-static[_ngcontent-%COMP%]{width:60px}.stories-slider-wrapper[_ngcontent-%COMP%]{max-width:calc(100% - 70px)}.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]{width:60px;min-width:60px}.stories-list[_ngcontent-%COMP%]{gap:10px}.slider-nav-btn[_ngcontent-%COMP%]{display:none}.story-avatar[_ngcontent-%COMP%]{width:56px;height:56px}.story-ring[_ngcontent-%COMP%]{width:60px;height:60px;top:-2px;left:-2px}.add-story-avatar[_ngcontent-%COMP%]{width:56px;height:56px}.current-user-avatar[_ngcontent-%COMP%]{width:50px;height:50px}.story-username[_ngcontent-%COMP%]{font-size:11px;max-width:60px}.story-bar__user-avatar[_ngcontent-%COMP%]{width:48px;height:48px}.story-bar__user-avatar[_ngcontent-%COMP%]:before{inset:-2px}.story-bar__user-name[_ngcontent-%COMP%]{font-size:11px;max-width:56px}.story__top[_ngcontent-%COMP%]{padding:40px 12px 12px}.story__bottom[_ngcontent-%COMP%]{padding:12px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:row;flex-wrap:wrap;gap:6px;justify-content:space-between}.ecommerce-btn[_ngcontent-%COMP%]{padding:8px 12px;font-size:11px;flex:1;min-width:80px}.ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.story__actions[_ngcontent-%COMP%]{gap:12px;margin-bottom:8px}.story__action-btn[_ngcontent-%COMP%]{font-size:18px;padding:6px}}@media (max-width: 480px){.story-bar[_ngcontent-%COMP%]{padding:6px 8px;gap:6px}.story-bar__user-avatar[_ngcontent-%COMP%]{width:40px;height:40px}.story-bar__user-avatar[_ngcontent-%COMP%]:before{inset:-2px}.story-bar__user-name[_ngcontent-%COMP%]{font-size:10px;max-width:48px}.story__top[_ngcontent-%COMP%]{padding:32px 8px 8px}.story__bottom[_ngcontent-%COMP%]{padding:8px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:column;gap:4px}.ecommerce-btn[_ngcontent-%COMP%]{padding:6px 8px;font-size:10px}.ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.story__actions[_ngcontent-%COMP%]{gap:8px;margin-bottom:6px}.story__action-btn[_ngcontent-%COMP%]{font-size:16px;padding:4px}.story__user[_ngcontent-%COMP%]{font-size:12px}.story__time[_ngcontent-%COMP%]{font-size:10px}.story__avatar[_ngcontent-%COMP%]{width:28px;height:28px}}@media (hover: none) and (pointer: coarse){.story-bar__user[_ngcontent-%COMP%]:active, .ecommerce-btn[_ngcontent-%COMP%]:active{transform:scale(.95);transition:transform .1s ease}.story__action-btn[_ngcontent-%COMP%]:active, .story__close[_ngcontent-%COMP%]:active{transform:scale(.9);transition:transform .1s ease}}@media (max-width: 896px) and (orientation: landscape){.story__top[_ngcontent-%COMP%]{padding:24px 12px 8px}.story__bottom[_ngcontent-%COMP%]{padding:8px 12px}.story__ecommerce-actions[_ngcontent-%COMP%]{flex-direction:row;gap:8px}.ecommerce-btn[_ngcontent-%COMP%]{padding:6px 10px;font-size:10px}}.middle-navigation[_ngcontent-%COMP%]{position:absolute;bottom:100px;left:50%;transform:translate(-50%);z-index:4}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#ee5a24);border:none;border-radius:25px;padding:12px 24px;color:#fff;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #ff6b6b4d}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #ff6b6b66}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px}@media (min-resolution: 192dpi){.story-bar__user-avatar[_ngcontent-%COMP%], .story__avatar[_ngcontent-%COMP%]{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}}@media (max-width: 768px){.middle-navigation[_ngcontent-%COMP%]{bottom:80px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]{padding:10px 20px;font-size:12px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px}}\"]\n      });\n    }\n  }\n  return ViewAddStoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}