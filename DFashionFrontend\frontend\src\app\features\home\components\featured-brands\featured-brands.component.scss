.featured-brands-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  color: white;
  position: relative;
}

// Mobile Action Buttons (TikTok/Instagram Style)
.mobile-action-buttons {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 10;

  .action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    ion-icon {
      font-size: 24px;
      margin-bottom: 2px;
    }

    .action-count, .action-text {
      font-size: 10px;
      font-weight: 600;
      line-height: 1;
    }

    &:hover {
      transform: scale(1.1);
      background: rgba(255, 255, 255, 0.3);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;

      &.like-btn {
        color: #ff3040;

        ion-icon {
          animation: heartBeat 0.6s ease-in-out;
        }
      }

      &.bookmark-btn {
        color: #ffd700;
      }
    }

    &.like-btn.active ion-icon {
      color: #ff3040;
    }

    &.music-btn {
      background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);

      &:hover {
        transform: scale(1.1) rotate(15deg);
      }
    }
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.3); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

// Hide action buttons on desktop
@media (min-width: 769px) {
  .mobile-action-buttons {
    display: none;
  }
}

// Header Section
.section-header {
  margin-bottom: 24px;
  
  .header-content {
    text-align: center;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #ffd700;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

// Loading State
.loading-container {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .loading-brand-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
    
    .loading-header {
      margin-bottom: 16px;
      
      .loading-brand-name {
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        margin-bottom: 8px;
        animation: loading 1.5s infinite;
      }
      
      .loading-stats {
        height: 16px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        width: 70%;
        animation: loading 1.5s infinite;
      }
    }
    
    .loading-products {
      display: flex;
      gap: 12px;
      
      .loading-product {
        flex: 1;
        height: 120px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        animation: loading 1.5s infinite;
      }
    }
  }
}

@keyframes loading {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #ff6b6b;
    margin-bottom: 16px;
  }
  
  .error-message {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
  }
  
  .retry-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Brands Grid
// Brands Slider Styles
.brands-slider-container {
  position: relative;
  margin: 0 -20px;

  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    ion-icon {
      font-size: 18px;
    }

    &.prev-btn {
      left: -20px;
    }

    &.next-btn {
      right: -20px;
    }
  }
}

.brands-slider-wrapper {
  overflow: hidden;
  padding: 0 20px;
}

.brands-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 20px;

  .brand-card {
    flex: 0 0 300px;
    width: 300px;
  }
}

// Responsive adjustments for brands
@media (max-width: 1200px) {
  .brands-slider {
    .brand-card {
      flex: 0 0 280px;
      width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .brands-slider-container {
    margin: 0 -10px;

    .slider-nav {
      width: 35px;
      height: 35px;

      &.prev-btn {
        left: -15px;
      }

      &.next-btn {
        right: -15px;
      }

      ion-icon {
        font-size: 16px;
      }
    }
  }

  .brands-slider-wrapper {
    padding: 0 10px;
  }

  .brands-slider {
    gap: 15px;

    .brand-card {
      flex: 0 0 260px;
      width: 260px;
    }
  }
}

.brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.brand-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  }
}

.brand-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .brand-info {
    flex: 1;
  }
  
  .brand-name {
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin: 0 0 12px 0;
  }
  
  .brand-stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      
      ion-icon {
        font-size: 14px;
        color: #ffd700;
      }
    }
  }
  
  .brand-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    
    ion-icon {
      font-size: 14px;
    }
  }
}

.top-products {
  margin-bottom: 20px;
  
  .products-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin: 0 0 16px 0;
  }
  
  .products-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    
    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
    }
  }
}

.product-item {
  flex: 0 0 140px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
    
    .product-actions {
      opacity: 1;
    }
  }
}

.product-image-container {
  position: relative;
  
  .product-image {
    width: 100%;
    height: 100px;
    object-fit: cover;
  }
  
  .product-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    .action-btn {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: none;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 14px;
        color: #333;
      }
      
      &:hover {
        background: white;
        transform: scale(1.1);
      }
      
      &.like-btn {
        &:hover ion-icon {
          color: #dc3545;
        }

        &.liked {
          background: rgba(220, 53, 69, 0.2);

          ion-icon {
            color: #dc3545;
          }
        }
      }

      &.share-btn:hover ion-icon {
        color: #007bff;
      }
    }
  }
}

.product-details {
  padding: 12px;
  
  .product-name {
    font-size: 12px;
    font-weight: 600;
    color: white;
    margin: 0 0 8px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .product-price {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    
    .current-price {
      font-size: 14px;
      font-weight: 700;
      color: #ffd700;
    }
    
    .original-price {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.6);
      text-decoration: line-through;
    }
  }
  
  .product-rating {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .stars {
      display: flex;
      gap: 1px;
      
      ion-icon {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.3);
        
        &.filled {
          color: #ffd700;
        }
      }
    }
    
    .rating-count {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.view-more-section {
  .view-more-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
    }
    
    ion-icon {
      font-size: 16px;
    }
  }
}

// Empty State
.empty-state, .empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: rgba(255, 255, 255, 0.4);
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
  }
  
  .empty-message {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// Desktop/Laptop Responsiveness (768px and above)
@media (min-width: 769px) {
  .brands-slider-container {
    .brands-slider {
      .brand-card {
        // Responsive card width based on screen size
        flex: 0 0 calc(33.333% - 14px); // 3 cards per row on medium screens
        width: calc(33.333% - 14px);
        max-width: 350px;
      }
    }
  }
}

@media (min-width: 1024px) {
  .brands-slider-container {
    .brands-slider {
      .brand-card {
        flex: 0 0 calc(25% - 15px); // 4 cards per row on large screens
        width: calc(25% - 15px);
        max-width: 320px;
      }
    }
  }
}

@media (min-width: 1200px) {
  .brands-slider-container {
    .brands-slider {
      .brand-card {
        flex: 0 0 calc(20% - 16px); // 5 cards per row on extra large screens
        width: calc(20% - 16px);
        max-width: 300px;
      }
    }
  }
}

@media (min-width: 1440px) {
  .brands-slider-container {
    .brands-slider {
      .brand-card {
        flex: 0 0 calc(16.666% - 17px); // 6 cards per row on very large screens
        width: calc(16.666% - 17px);
        max-width: 280px;
      }
    }
  }
}

// Mobile/Tablet Responsiveness (768px and below) - Keep existing
@media (max-width: 768px) {
  .featured-brands-container {
    padding: 16px;
  }
  
  .brands-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .brand-header {
    flex-direction: column;
    gap: 12px;
    
    .brand-badge {
      align-self: flex-start;
    }
  }
  
  .brand-stats {
    flex-direction: row !important;
    flex-wrap: wrap;
    gap: 12px !important;
  }
  
  .section-title {
    font-size: 20px;
  }
}
