{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ViewAddStoriesComponent } from '../home/<USER>/view-add-stories/view-add-stories.component';\nlet SocialFeedComponent = class SocialFeedComponent {\n  constructor(router, socialMediaService, socialFeaturesService, authService, cartService, wishlistService) {\n    this.router = router;\n    this.socialMediaService = socialMediaService;\n    this.socialFeaturesService = socialFeaturesService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.posts = [];\n    this.stories = [];\n    this.commentTexts = {};\n    this.selectedProduct = null;\n    this.currentUser = null;\n    this.loading = false;\n    this.hasMorePosts = true;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadStories();\n    this.loadPosts();\n    // Add some mock stories for testing\n    this.addMockStories();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadCurrentUser() {\n    this.currentUser = this.authService.currentUserValue;\n    if (!this.currentUser) {\n      // Fallback for non-authenticated users\n      this.currentUser = {\n        _id: 'guest-user',\n        username: 'guest',\n        fullName: 'Guest User',\n        avatar: '/assets/images/default-avatar.svg'\n      };\n    }\n    console.log('Social Feed - Current User loaded:', this.currentUser);\n  }\n  loadStories() {\n    this.subscriptions.push(this.socialMediaService.loadStories().subscribe({\n      next: response => {\n        if (response.success) {\n          // Group stories by user\n          const userStories = response.stories.reduce((acc, story) => {\n            const userId = story.user._id;\n            if (!acc[userId]) {\n              acc[userId] = {\n                user: story.user,\n                viewed: false,\n                stories: []\n              };\n            }\n            acc[userId].stories.push(story);\n            return acc;\n          }, {});\n          this.stories = Object.values(userStories);\n          console.log('Social Feed - Stories loaded:', this.stories);\n        }\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        // Set empty stories array so add story button still shows\n        this.stories = [];\n      }\n    }));\n  }\n  loadPosts() {\n    this.loading = true;\n    this.subscriptions.push(this.socialMediaService.loadPosts(1, 10).subscribe({\n      next: response => {\n        if (response.success) {\n          this.posts = response.posts.map(post => ({\n            ...post,\n            isLiked: this.checkIfUserLiked(post.likes),\n            isSaved: this.checkIfUserSaved(post.saves)\n          }));\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading posts:', error);\n        this.loading = false;\n      }\n    }));\n  }\n  checkIfUserLiked(likes) {\n    if (!this.currentUser || !likes) return false;\n    return likes.some(like => like.user === this.currentUser._id);\n  }\n  checkIfUserSaved(saves) {\n    if (!this.currentUser || !saves) return false;\n    return saves.some(save => save.user === this.currentUser._id);\n  }\n  loadMorePosts() {\n    if (this.loading || !this.hasMorePosts) return;\n    this.loading = true;\n    const page = Math.floor(this.posts.length / 10) + 1;\n    this.subscriptions.push(this.socialMediaService.loadPosts(page, 10).subscribe({\n      next: response => {\n        if (response.success && response.posts.length > 0) {\n          const newPosts = response.posts.map(post => ({\n            ...post,\n            isLiked: this.checkIfUserLiked(post.likes),\n            isSaved: this.checkIfUserSaved(post.saves)\n          }));\n          this.posts = [...this.posts, ...newPosts];\n          // Check if there are more posts\n          this.hasMorePosts = response.posts.length === 10;\n        } else {\n          this.hasMorePosts = false;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading more posts:', error);\n        this.loading = false;\n      }\n    }));\n  }\n  // Stories actions\n  createStory() {\n    this.router.navigate(['/create-story']);\n  }\n  viewStory(userId) {\n    // Navigate to stories viewer with user ID\n    console.log('Navigating to user stories:', userId);\n    this.router.navigate(['/stories', userId]);\n  }\n  viewStories() {\n    // Navigate to general stories viewer\n    console.log('Navigating to all stories');\n    this.router.navigate(['/stories']);\n  }\n  openStoryViewer(storyIndex = 0) {\n    // Open stories viewer starting from specific index\n    console.log('Opening story viewer at index:', storyIndex);\n    this.router.navigate(['/stories'], {\n      queryParams: {\n        index: storyIndex\n      }\n    });\n  }\n  onStoryClick(event) {\n    // Handle story click from carousel\n    this.openStoryViewer(event.index);\n  }\n  // Post actions\n  viewProfile(userId) {\n    this.router.navigate(['/profile', userId]);\n  }\n  showPostMenu(post) {\n    // TODO: Show post menu (report, share, etc.)\n    console.log('Show menu for post:', post);\n  }\n  toggleLike(post) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const wasLiked = post.isLiked;\n    // Optimistic update\n    post.isLiked = !post.isLiked;\n    const currentUser = this.authService.currentUserValue;\n    if (post.isLiked) {\n      post.likes.push({\n        user: currentUser?._id || '',\n        likedAt: new Date()\n      });\n    } else {\n      post.likes = post.likes.filter(like => like.user !== currentUser?._id);\n    }\n    // API call using new social features service\n    this.subscriptions.push(this.socialFeaturesService.togglePostLike(post._id).subscribe({\n      next: response => {\n        if (response.success) {\n          post.isLiked = response.isLiked;\n          // Update likes count from server response\n          if (response.likesCount !== undefined) {\n            if (post.analytics) {\n              post.analytics.likes = response.likesCount;\n            }\n          }\n        }\n      },\n      error: error => {\n        // Revert optimistic update on error\n        const currentUser = this.authService.currentUserValue;\n        post.isLiked = wasLiked;\n        if (wasLiked) {\n          post.likes.push({\n            user: currentUser?._id || '',\n            likedAt: new Date()\n          });\n        } else {\n          post.likes = post.likes.filter(like => like.user !== currentUser?._id);\n        }\n        console.error('Error toggling like:', error);\n      }\n    }));\n  }\n  toggleSave(post) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const wasSaved = post.isSaved;\n    // Optimistic update\n    const currentUser = this.authService.currentUserValue;\n    post.isSaved = !post.isSaved;\n    if (post.isSaved) {\n      post.saves.push({\n        user: currentUser?._id || '',\n        savedAt: new Date()\n      });\n    } else {\n      post.saves = post.saves.filter(save => save.user !== currentUser?._id);\n    }\n    // API call using new social features service\n    this.subscriptions.push(this.socialFeaturesService.togglePostSave(post._id).subscribe({\n      next: response => {\n        if (response.success) {\n          post.isSaved = response.isSaved;\n          // Update saves count from server response\n          if (response.savesCount !== undefined) {\n            if (post.analytics) {\n              post.analytics.saves = response.savesCount;\n            }\n          }\n        }\n      },\n      error: error => {\n        // Revert optimistic update on error\n        const currentUser = this.authService.currentUserValue;\n        post.isSaved = wasSaved;\n        if (wasSaved) {\n          post.saves.push({\n            user: currentUser?._id || '',\n            savedAt: new Date()\n          });\n        } else {\n          post.saves = post.saves.filter(save => save.user !== currentUser?._id);\n        }\n        console.error('Error toggling save:', error);\n      }\n    }));\n  }\n  sharePost(post) {\n    // TODO: Implement share functionality\n    console.log('Share post:', post);\n    if (navigator.share) {\n      navigator.share({\n        title: `${post.user.username}'s post`,\n        text: post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n  focusComment(postId) {\n    const commentInput = document.getElementById(`comment-${postId}`);\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n  addComment(post) {\n    const commentText = this.commentTexts[post._id];\n    if (!commentText?.trim()) return;\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentUser = this.authService.currentUserValue;\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: currentUser?._id || '',\n        username: currentUser?.username || '',\n        fullName: currentUser?.fullName || '',\n        avatar: currentUser?.avatar || ''\n      },\n      text: commentText.trim(),\n      commentedAt: new Date()\n    };\n    // Optimistic update\n    post.comments.push(newComment);\n    this.commentTexts[post._id] = '';\n    // API call using new social features service\n    this.subscriptions.push(this.socialFeaturesService.addPostComment(post._id, commentText.trim()).subscribe({\n      next: response => {\n        if (response.success) {\n          // Replace the optimistic comment with the server response\n          const commentIndex = post.comments.findIndex(c => c._id === newComment._id);\n          if (commentIndex !== -1) {\n            post.comments[commentIndex] = response.comment;\n          }\n          if (post.analytics) {\n            post.analytics.comments += 1;\n          }\n        }\n      },\n      error: error => {\n        // Remove optimistic comment on error\n        post.comments = post.comments.filter(c => c._id !== newComment._id);\n        this.commentTexts[post._id] = commentText; // Restore comment text\n        console.error('Error adding comment:', error);\n      }\n    }));\n  }\n  viewAllComments(post) {\n    this.router.navigate(['/post', post._id, 'comments']);\n  }\n  viewPost(post) {\n    // Navigate to post detail view\n    this.router.navigate(['/post', post._id]);\n  }\n  viewPostDetail(postId) {\n    // Navigate to post detail view by ID\n    this.router.navigate(['/post', postId]);\n  }\n  searchHashtag(hashtag) {\n    this.router.navigate(['/search'], {\n      queryParams: {\n        hashtag\n      }\n    });\n  }\n  // E-commerce actions\n  buyNow(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addToCart(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      const size = post.products[0].size;\n      const color = post.products[0].color;\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n      this.subscriptions.push(this.cartService.addToCart(product._id, 1, size, color).subscribe({\n        next: response => {\n          if (response.success) {\n            // Track analytics using new service\n            this.socialFeaturesService.trackProductClick('post', post._id, product._id, 'add_to_cart').subscribe();\n            alert(`${product.name} added to cart!`);\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Failed to add to cart. Please try again.');\n        }\n      }));\n    }\n  }\n  addToWishlist(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      const size = post.products[0].size;\n      const color = post.products[0].color;\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n      this.subscriptions.push(this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            // Track analytics using new service\n            this.socialFeaturesService.trackProductClick('post', post._id, product._id, 'add_to_wishlist').subscribe();\n            alert(`${product.name} added to wishlist!`);\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Failed to add to wishlist. Please try again.');\n        }\n      }));\n    }\n  }\n  viewProduct(post, product) {\n    // Track analytics\n    this.trackProductClick(post._id, product._id, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/products', product._id]);\n  }\n  trackProductClick(postId, productId, action) {\n    // Track product click analytics using new service\n    this.socialFeaturesService.trackProductClick('post', postId, productId, action).subscribe({\n      next: response => {\n        console.log('Analytics tracked:', response);\n      },\n      error: error => {\n        console.error('Error tracking analytics:', error);\n      }\n    });\n  }\n  // Product modal\n  showProductDetails(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n  addMockStories() {\n    // Removed mock stories - use real API data only\n    this.stories = [];\n    console.log('Mock stories removed - using real API data only');\n  }\n};\nSocialFeedComponent = __decorate([Component({\n  selector: 'app-social-feed',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ViewAddStoriesComponent],\n  templateUrl: './social-feed.component.html',\n  styles: [`\n    .social-feed {\n      max-width: 600px;\n      margin: 0 auto;\n      padding: 0 0 80px 0;\n    }\n\n    /* Stories Bar */\n    .stories-bar {\n      background: #fff;\n      border-bottom: 1px solid #eee;\n      padding: 16px 0;\n      margin-bottom: 20px;\n      position: sticky;\n      top: 0;\n      z-index: 100;\n    }\n\n    /* Swiper Carousel Styles */\n    .stories-swiper {\n      padding: 0 20px;\n      overflow: visible;\n    }\n\n    .story-slide {\n      width: auto !important;\n      flex-shrink: 0;\n    }\n\n    /* Legacy container styles (keeping for fallback) */\n    .stories-container {\n      display: flex;\n      gap: 16px;\n      padding: 0 20px;\n      overflow-x: auto;\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    }\n\n    .stories-container::-webkit-scrollbar {\n      display: none;\n    }\n\n    .story-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      min-width: 70px;\n    }\n\n    .story-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      padding: 2px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      transition: transform 0.2s ease;\n    }\n\n    .story-avatar:hover {\n      transform: scale(1.05);\n    }\n\n    .story-ring {\n      position: absolute;\n      top: -3px;\n      left: -3px;\n      right: -3px;\n      bottom: -3px;\n      border-radius: 50%;\n      background: linear-gradient(45deg, #ff6b6b, #ffa726, #ff6b6b);\n      z-index: -1;\n      animation: rotate 3s linear infinite;\n    }\n\n    .story-ring.viewed {\n      background: #ddd;\n      animation: none;\n    }\n\n    @keyframes rotate {\n      from { transform: rotate(0deg); }\n      to { transform: rotate(360deg); }\n    }\n\n    .story-avatar img {\n      width: 100%;\n      height: 100%;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .add-avatar {\n      background: #f8f9fa !important;\n      border-color: #ddd !important;\n      color: #666;\n      font-size: 1.2rem;\n    }\n\n    .story-username {\n      font-size: 0.8rem;\n      color: #333;\n      text-align: center;\n      max-width: 70px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    /* Post Cards */\n    .posts-container {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n    }\n\n    .post-card {\n      background: #fff;\n      border-radius: 12px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      overflow: hidden;\n    }\n\n    .post-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      cursor: pointer;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username-row {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n    }\n\n    .username {\n      font-weight: 600;\n      color: #333;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .verified {\n      color: #1da1f2;\n      font-size: 0.8rem;\n    }\n\n    .post-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .btn-menu {\n      background: none;\n      border: none;\n      color: #666;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n    }\n\n    .btn-menu:hover {\n      background: #f8f9fa;\n    }\n\n    /* Post Media */\n    .post-media {\n      position: relative;\n      background: #000;\n      cursor: pointer;\n    }\n\n    .media-container {\n      width: 100%;\n      aspect-ratio: 1;\n      overflow: hidden;\n    }\n\n    .post-image, .post-video {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .media-nav {\n      position: absolute;\n      bottom: 16px;\n      left: 50%;\n      transform: translateX(-50%);\n    }\n\n    .nav-dots {\n      display: flex;\n      gap: 6px;\n    }\n\n    .dot {\n      width: 6px;\n      height: 6px;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.5);\n    }\n\n    .dot.active {\n      background: #fff;\n    }\n\n    /* Post Actions */\n    .post-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 20px 8px;\n    }\n\n    .primary-actions, .secondary-actions {\n      display: flex;\n      gap: 16px;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      color: #333;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .action-btn:hover {\n      background: #f8f9fa;\n      transform: scale(1.1);\n    }\n\n    .action-btn.liked {\n      color: #ff6b6b;\n      animation: heartBeat 0.6s ease;\n    }\n\n    .action-btn.saved {\n      color: #333;\n    }\n\n    @keyframes heartBeat {\n      0%, 100% { transform: scale(1); }\n      50% { transform: scale(1.2); }\n    }\n\n    /* Post Stats */\n    .post-stats {\n      padding: 0 20px 8px;\n    }\n\n    .likes-count {\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    /* Post Caption */\n    .post-caption {\n      padding: 0 20px 12px;\n      line-height: 1.4;\n      cursor: pointer;\n    }\n\n    .caption-text {\n      margin-left: 8px;\n      color: #333;\n    }\n\n    .hashtags {\n      margin-top: 8px;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .hashtag {\n      color: #1da1f2;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .hashtag:hover {\n      text-decoration: underline;\n    }\n\n    /* E-commerce Actions */\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .ecom-btn {\n      flex: 1;\n      padding: 10px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      font-size: 0.85rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .ecom-btn:hover {\n      transform: translateY(-1px);\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    /* Comments */\n    .comments-preview {\n      padding: 0 20px 12px;\n    }\n\n    .view-all-comments {\n      color: #666;\n      font-size: 0.9rem;\n      cursor: pointer;\n      margin-bottom: 8px;\n    }\n\n    .view-all-comments:hover {\n      text-decoration: underline;\n    }\n\n    .comment {\n      margin-bottom: 4px;\n      font-size: 0.9rem;\n      line-height: 1.3;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      color: #333;\n      margin-right: 8px;\n    }\n\n    .comment-text {\n      color: #333;\n    }\n\n    /* Add Comment */\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-input {\n      flex: 1;\n      border: none;\n      outline: none;\n      font-size: 0.9rem;\n      padding: 8px 0;\n    }\n\n    .comment-input::placeholder {\n      color: #999;\n    }\n\n    .btn-post-comment {\n      background: none;\n      border: none;\n      color: #1da1f2;\n      font-weight: 600;\n      cursor: pointer;\n      font-size: 0.9rem;\n      padding: 8px;\n    }\n\n    .btn-post-comment:disabled {\n      color: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Load More */\n    .load-more {\n      text-align: center;\n      padding: 40px 20px;\n    }\n\n    .btn-load-more {\n      background: #007bff;\n      color: #fff;\n      border: none;\n      padding: 12px 24px;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin: 0 auto;\n    }\n\n    .btn-load-more:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Product Modal */\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      cursor: pointer;\n      color: #666;\n      padding: 4px;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-info {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .social-feed {\n        padding: 0 0 60px 0;\n      }\n\n      .stories-bar {\n        padding: 12px 0;\n      }\n\n      .stories-swiper {\n        padding: 0 16px;\n      }\n\n      .stories-container {\n        padding: 0 16px;\n        gap: 12px;\n      }\n\n      .story-avatar {\n        width: 50px;\n        height: 50px;\n      }\n\n      .story-ring {\n        top: -2px;\n        left: -2px;\n        right: -2px;\n        bottom: -2px;\n      }\n\n      .story-username {\n        font-size: 0.75rem;\n        max-width: 50px;\n      }\n\n      .post-header {\n        padding: 12px 16px;\n      }\n\n      .post-actions {\n        padding: 8px 16px 6px;\n      }\n\n      .post-stats, .post-caption, .comments-preview {\n        padding-left: 16px;\n        padding-right: 16px;\n      }\n\n      .ecommerce-actions {\n        padding: 8px 16px;\n        flex-direction: column;\n        gap: 6px;\n      }\n\n      .ecom-btn {\n        padding: 12px;\n        font-size: 0.9rem;\n      }\n\n      .add-comment {\n        padding: 8px 16px;\n      }\n    }\n  `]\n})], SocialFeedComponent);\nexport { SocialFeedComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ViewAddStoriesComponent", "SocialFeedComponent", "constructor", "router", "socialMediaService", "socialFeaturesService", "authService", "cartService", "wishlistService", "posts", "stories", "commentTexts", "selectedProduct", "currentUser", "loading", "hasMorePosts", "subscriptions", "ngOnInit", "loadCurrentUser", "loadStories", "loadPosts", "addMockStories", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "currentUserValue", "_id", "username", "fullName", "avatar", "console", "log", "push", "subscribe", "next", "response", "success", "userStories", "reduce", "acc", "story", "userId", "user", "viewed", "Object", "values", "error", "map", "post", "isLiked", "checkIfUserLiked", "likes", "isSaved", "checkIfUserSaved", "saves", "some", "like", "save", "loadMorePosts", "page", "Math", "floor", "length", "newPosts", "createStory", "navigate", "viewStory", "viewStories", "openStoryViewer", "storyIndex", "queryParams", "index", "onStoryClick", "event", "viewProfile", "showPostMenu", "toggleLike", "isAuthenticated", "wasLiked", "likedAt", "Date", "filter", "togglePostLike", "likesCount", "undefined", "analytics", "toggleSave", "wasSaved", "savedAt", "togglePostSave", "savesCount", "sharePost", "navigator", "share", "title", "text", "caption", "url", "window", "location", "href", "clipboard", "writeText", "alert", "focusComment", "postId", "commentInput", "document", "getElementById", "focus", "addComment", "commentText", "trim", "newComment", "now", "toString", "commentedAt", "comments", "addPostComment", "commentIndex", "findIndex", "c", "comment", "viewAllComments", "viewPost", "viewPostDetail", "searchHashtag", "hashtag", "buyNow", "products", "product", "productId", "source", "addToCart", "size", "color", "trackProductClick", "name", "addToWishlist", "viewProduct", "action", "showProductDetails", "closeProductModal", "buyProductNow", "addProductToCart", "addProductToWishlist", "getTimeAgo", "date", "diffMs", "getTime", "diffMinutes", "diffHours", "diffDays", "toLocaleDateString", "__decorate", "selector", "standalone", "imports", "templateUrl", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\posts\\social-feed.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { SocialMediaService } from '../../core/services/social-media.service';\nimport { SocialFeaturesService } from '../../core/services/social-features.service';\nimport { AuthService } from '../../core/services/auth.service';\nimport { CartService } from '../../core/services/cart.service';\nimport { WishlistService } from '../../core/services/wishlist.service';\nimport { Subscription } from 'rxjs';\nimport { ViewAddStoriesComponent, Story, CurrentUser } from '../home/<USER>/view-add-stories/view-add-stories.component';\n\ninterface Post {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n    isVerified?: boolean;\n  };\n  caption: string;\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    alt: string;\n  }[];\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n    size?: string;\n    color?: string;\n  }[];\n  hashtags: string[];\n  likes: { user: string; likedAt: Date }[];\n  comments: {\n    _id: string;\n    user: {\n      _id: string;\n      username: string;\n      fullName: string;\n      avatar?: string;\n    };\n    text: string;\n    commentedAt: Date;\n  }[];\n  shares: { user: string; sharedAt: Date }[];\n  saves: { user: string; savedAt: Date }[];\n  isLiked: boolean;\n  isSaved: boolean;\n  createdAt: Date;\n  analytics?: {\n    likes: number;\n    comments: number;\n    saves: number;\n    shares: number;\n  };\n}\n\n@Component({\n  selector: 'app-social-feed',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ViewAddStoriesComponent],\n  templateUrl: './social-feed.component.html',\n  styles: [`\n    .social-feed {\n      max-width: 600px;\n      margin: 0 auto;\n      padding: 0 0 80px 0;\n    }\n\n    /* Stories Bar */\n    .stories-bar {\n      background: #fff;\n      border-bottom: 1px solid #eee;\n      padding: 16px 0;\n      margin-bottom: 20px;\n      position: sticky;\n      top: 0;\n      z-index: 100;\n    }\n\n    /* Swiper Carousel Styles */\n    .stories-swiper {\n      padding: 0 20px;\n      overflow: visible;\n    }\n\n    .story-slide {\n      width: auto !important;\n      flex-shrink: 0;\n    }\n\n    /* Legacy container styles (keeping for fallback) */\n    .stories-container {\n      display: flex;\n      gap: 16px;\n      padding: 0 20px;\n      overflow-x: auto;\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    }\n\n    .stories-container::-webkit-scrollbar {\n      display: none;\n    }\n\n    .story-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      min-width: 70px;\n    }\n\n    .story-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      padding: 2px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      transition: transform 0.2s ease;\n    }\n\n    .story-avatar:hover {\n      transform: scale(1.05);\n    }\n\n    .story-ring {\n      position: absolute;\n      top: -3px;\n      left: -3px;\n      right: -3px;\n      bottom: -3px;\n      border-radius: 50%;\n      background: linear-gradient(45deg, #ff6b6b, #ffa726, #ff6b6b);\n      z-index: -1;\n      animation: rotate 3s linear infinite;\n    }\n\n    .story-ring.viewed {\n      background: #ddd;\n      animation: none;\n    }\n\n    @keyframes rotate {\n      from { transform: rotate(0deg); }\n      to { transform: rotate(360deg); }\n    }\n\n    .story-avatar img {\n      width: 100%;\n      height: 100%;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .add-avatar {\n      background: #f8f9fa !important;\n      border-color: #ddd !important;\n      color: #666;\n      font-size: 1.2rem;\n    }\n\n    .story-username {\n      font-size: 0.8rem;\n      color: #333;\n      text-align: center;\n      max-width: 70px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    /* Post Cards */\n    .posts-container {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n    }\n\n    .post-card {\n      background: #fff;\n      border-radius: 12px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      overflow: hidden;\n    }\n\n    .post-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      cursor: pointer;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username-row {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n    }\n\n    .username {\n      font-weight: 600;\n      color: #333;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .verified {\n      color: #1da1f2;\n      font-size: 0.8rem;\n    }\n\n    .post-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .btn-menu {\n      background: none;\n      border: none;\n      color: #666;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n    }\n\n    .btn-menu:hover {\n      background: #f8f9fa;\n    }\n\n    /* Post Media */\n    .post-media {\n      position: relative;\n      background: #000;\n      cursor: pointer;\n    }\n\n    .media-container {\n      width: 100%;\n      aspect-ratio: 1;\n      overflow: hidden;\n    }\n\n    .post-image, .post-video {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .media-nav {\n      position: absolute;\n      bottom: 16px;\n      left: 50%;\n      transform: translateX(-50%);\n    }\n\n    .nav-dots {\n      display: flex;\n      gap: 6px;\n    }\n\n    .dot {\n      width: 6px;\n      height: 6px;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.5);\n    }\n\n    .dot.active {\n      background: #fff;\n    }\n\n    /* Post Actions */\n    .post-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 20px 8px;\n    }\n\n    .primary-actions, .secondary-actions {\n      display: flex;\n      gap: 16px;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      color: #333;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .action-btn:hover {\n      background: #f8f9fa;\n      transform: scale(1.1);\n    }\n\n    .action-btn.liked {\n      color: #ff6b6b;\n      animation: heartBeat 0.6s ease;\n    }\n\n    .action-btn.saved {\n      color: #333;\n    }\n\n    @keyframes heartBeat {\n      0%, 100% { transform: scale(1); }\n      50% { transform: scale(1.2); }\n    }\n\n    /* Post Stats */\n    .post-stats {\n      padding: 0 20px 8px;\n    }\n\n    .likes-count {\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    /* Post Caption */\n    .post-caption {\n      padding: 0 20px 12px;\n      line-height: 1.4;\n      cursor: pointer;\n    }\n\n    .caption-text {\n      margin-left: 8px;\n      color: #333;\n    }\n\n    .hashtags {\n      margin-top: 8px;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .hashtag {\n      color: #1da1f2;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .hashtag:hover {\n      text-decoration: underline;\n    }\n\n    /* E-commerce Actions */\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .ecom-btn {\n      flex: 1;\n      padding: 10px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      font-size: 0.85rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .ecom-btn:hover {\n      transform: translateY(-1px);\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    /* Comments */\n    .comments-preview {\n      padding: 0 20px 12px;\n    }\n\n    .view-all-comments {\n      color: #666;\n      font-size: 0.9rem;\n      cursor: pointer;\n      margin-bottom: 8px;\n    }\n\n    .view-all-comments:hover {\n      text-decoration: underline;\n    }\n\n    .comment {\n      margin-bottom: 4px;\n      font-size: 0.9rem;\n      line-height: 1.3;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      color: #333;\n      margin-right: 8px;\n    }\n\n    .comment-text {\n      color: #333;\n    }\n\n    /* Add Comment */\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-input {\n      flex: 1;\n      border: none;\n      outline: none;\n      font-size: 0.9rem;\n      padding: 8px 0;\n    }\n\n    .comment-input::placeholder {\n      color: #999;\n    }\n\n    .btn-post-comment {\n      background: none;\n      border: none;\n      color: #1da1f2;\n      font-weight: 600;\n      cursor: pointer;\n      font-size: 0.9rem;\n      padding: 8px;\n    }\n\n    .btn-post-comment:disabled {\n      color: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Load More */\n    .load-more {\n      text-align: center;\n      padding: 40px 20px;\n    }\n\n    .btn-load-more {\n      background: #007bff;\n      color: #fff;\n      border: none;\n      padding: 12px 24px;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin: 0 auto;\n    }\n\n    .btn-load-more:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Product Modal */\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      cursor: pointer;\n      color: #666;\n      padding: 4px;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-info {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .social-feed {\n        padding: 0 0 60px 0;\n      }\n\n      .stories-bar {\n        padding: 12px 0;\n      }\n\n      .stories-swiper {\n        padding: 0 16px;\n      }\n\n      .stories-container {\n        padding: 0 16px;\n        gap: 12px;\n      }\n\n      .story-avatar {\n        width: 50px;\n        height: 50px;\n      }\n\n      .story-ring {\n        top: -2px;\n        left: -2px;\n        right: -2px;\n        bottom: -2px;\n      }\n\n      .story-username {\n        font-size: 0.75rem;\n        max-width: 50px;\n      }\n\n      .post-header {\n        padding: 12px 16px;\n      }\n\n      .post-actions {\n        padding: 8px 16px 6px;\n      }\n\n      .post-stats, .post-caption, .comments-preview {\n        padding-left: 16px;\n        padding-right: 16px;\n      }\n\n      .ecommerce-actions {\n        padding: 8px 16px;\n        flex-direction: column;\n        gap: 6px;\n      }\n\n      .ecom-btn {\n        padding: 12px;\n        font-size: 0.9rem;\n      }\n\n      .add-comment {\n        padding: 8px 16px;\n      }\n    }\n  `]\n})\nexport class SocialFeedComponent implements OnInit, OnDestroy {\n  posts: Post[] = [];\n  stories: Story[] = [];\n  commentTexts: { [key: string]: string } = {};\n  selectedProduct: any = null;\n  currentUser: CurrentUser | null = null;\n  loading = false;\n  hasMorePosts = true;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private socialMediaService: SocialMediaService,\n    private socialFeaturesService: SocialFeaturesService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadStories();\n    this.loadPosts();\n\n    // Add some mock stories for testing\n    this.addMockStories();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  loadCurrentUser() {\n    this.currentUser = this.authService.currentUserValue;\n    if (!this.currentUser) {\n      // Fallback for non-authenticated users\n      this.currentUser = {\n        _id: 'guest-user',\n        username: 'guest',\n        fullName: 'Guest User',\n        avatar: '/assets/images/default-avatar.svg'\n      };\n    }\n    console.log('Social Feed - Current User loaded:', this.currentUser);\n  }\n\n  loadStories() {\n    this.subscriptions.push(\n      this.socialMediaService.loadStories().subscribe({\n        next: (response) => {\n          if (response.success) {\n            // Group stories by user\n            const userStories = response.stories.reduce((acc: any, story: any) => {\n              const userId = story.user._id;\n              if (!acc[userId]) {\n                acc[userId] = {\n                  user: story.user,\n                  viewed: false, // TODO: Check if current user viewed this user's stories\n                  stories: []\n                };\n              }\n              acc[userId].stories.push(story);\n              return acc;\n            }, {});\n\n            this.stories = Object.values(userStories);\n            console.log('Social Feed - Stories loaded:', this.stories);\n          }\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          // Set empty stories array so add story button still shows\n          this.stories = [];\n        }\n      })\n    );\n  }\n\n  loadPosts() {\n    this.loading = true;\n\n    this.subscriptions.push(\n      this.socialMediaService.loadPosts(1, 10).subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.posts = response.posts.map((post: any) => ({\n              ...post,\n              isLiked: this.checkIfUserLiked(post.likes),\n              isSaved: this.checkIfUserSaved(post.saves)\n            }));\n          }\n          this.loading = false;\n        },\n        error: (error) => {\n          console.error('Error loading posts:', error);\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  private checkIfUserLiked(likes: any[]): boolean {\n    if (!this.currentUser || !likes) return false;\n    return likes.some(like => like.user === this.currentUser!._id);\n  }\n\n  private checkIfUserSaved(saves: any[]): boolean {\n    if (!this.currentUser || !saves) return false;\n    return saves.some(save => save.user === this.currentUser!._id);\n  }\n\n  loadMorePosts() {\n    if (this.loading || !this.hasMorePosts) return;\n\n    this.loading = true;\n    const page = Math.floor(this.posts.length / 10) + 1;\n\n    this.subscriptions.push(\n      this.socialMediaService.loadPosts(page, 10).subscribe({\n        next: (response) => {\n          if (response.success && response.posts.length > 0) {\n            const newPosts = response.posts.map((post: any) => ({\n              ...post,\n              isLiked: this.checkIfUserLiked(post.likes),\n              isSaved: this.checkIfUserSaved(post.saves)\n            }));\n            this.posts = [...this.posts, ...newPosts];\n\n            // Check if there are more posts\n            this.hasMorePosts = response.posts.length === 10;\n          } else {\n            this.hasMorePosts = false;\n          }\n          this.loading = false;\n        },\n        error: (error) => {\n          console.error('Error loading more posts:', error);\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  // Stories actions\n  createStory() {\n    this.router.navigate(['/create-story']);\n  }\n\n  viewStory(userId: string) {\n    // Navigate to stories viewer with user ID\n    console.log('Navigating to user stories:', userId);\n    this.router.navigate(['/stories', userId]);\n  }\n\n  viewStories() {\n    // Navigate to general stories viewer\n    console.log('Navigating to all stories');\n    this.router.navigate(['/stories']);\n  }\n\n  openStoryViewer(storyIndex: number = 0) {\n    // Open stories viewer starting from specific index\n    console.log('Opening story viewer at index:', storyIndex);\n    this.router.navigate(['/stories'], {\n      queryParams: { index: storyIndex }\n    });\n  }\n\n  onStoryClick(event: {story: Story, index: number}) {\n    // Handle story click from carousel\n    this.openStoryViewer(event.index);\n  }\n\n  // Post actions\n  viewProfile(userId: string) {\n    this.router.navigate(['/profile', userId]);\n  }\n\n  showPostMenu(post: Post) {\n    // TODO: Show post menu (report, share, etc.)\n    console.log('Show menu for post:', post);\n  }\n\n  toggleLike(post: Post) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n\n    const wasLiked = post.isLiked;\n\n    // Optimistic update\n    post.isLiked = !post.isLiked;\n    const currentUser = this.authService.currentUserValue;\n    if (post.isLiked) {\n      post.likes.push({\n        user: currentUser?._id || '',\n        likedAt: new Date()\n      });\n    } else {\n      post.likes = post.likes.filter(like => like.user !== currentUser?._id);\n    }\n\n    // API call using new social features service\n    this.subscriptions.push(\n      this.socialFeaturesService.togglePostLike(post._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            post.isLiked = response.isLiked!;\n            // Update likes count from server response\n            if (response.likesCount !== undefined) {\n              if (post.analytics) {\n                post.analytics.likes = response.likesCount;\n              }\n            }\n          }\n        },\n        error: (error) => {\n          // Revert optimistic update on error\n          const currentUser = this.authService.currentUserValue;\n          post.isLiked = wasLiked;\n          if (wasLiked) {\n            post.likes.push({\n              user: currentUser?._id || '',\n              likedAt: new Date()\n            });\n          } else {\n            post.likes = post.likes.filter(like => like.user !== currentUser?._id);\n          }\n          console.error('Error toggling like:', error);\n        }\n      })\n    );\n  }\n\n  toggleSave(post: Post) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n\n    const wasSaved = post.isSaved;\n\n    // Optimistic update\n    const currentUser = this.authService.currentUserValue;\n    post.isSaved = !post.isSaved;\n    if (post.isSaved) {\n      post.saves.push({\n        user: currentUser?._id || '',\n        savedAt: new Date()\n      });\n    } else {\n      post.saves = post.saves.filter(save => save.user !== currentUser?._id);\n    }\n\n    // API call using new social features service\n    this.subscriptions.push(\n      this.socialFeaturesService.togglePostSave(post._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            post.isSaved = response.isSaved!;\n            // Update saves count from server response\n            if (response.savesCount !== undefined) {\n              if (post.analytics) {\n                post.analytics.saves = response.savesCount;\n              }\n            }\n          }\n        },\n        error: (error) => {\n          // Revert optimistic update on error\n          const currentUser = this.authService.currentUserValue;\n          post.isSaved = wasSaved;\n          if (wasSaved) {\n            post.saves.push({\n              user: currentUser?._id || '',\n              savedAt: new Date()\n            });\n          } else {\n            post.saves = post.saves.filter(save => save.user !== currentUser?._id);\n          }\n          console.error('Error toggling save:', error);\n        }\n      })\n    );\n  }\n\n  sharePost(post: Post) {\n    // TODO: Implement share functionality\n    console.log('Share post:', post);\n\n    if (navigator.share) {\n      navigator.share({\n        title: `${post.user.username}'s post`,\n        text: post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n\n  focusComment(postId: string) {\n    const commentInput = document.getElementById(`comment-${postId}`) as HTMLInputElement;\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n\n  addComment(post: Post) {\n    const commentText = this.commentTexts[post._id];\n    if (!commentText?.trim()) return;\n\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n\n    const currentUser = this.authService.currentUserValue;\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: currentUser?._id || '',\n        username: currentUser?.username || '',\n        fullName: currentUser?.fullName || '',\n        avatar: currentUser?.avatar || ''\n      },\n      text: commentText.trim(),\n      commentedAt: new Date()\n    };\n\n    // Optimistic update\n    post.comments.push(newComment);\n    this.commentTexts[post._id] = '';\n\n    // API call using new social features service\n    this.subscriptions.push(\n      this.socialFeaturesService.addPostComment(post._id, commentText.trim()).subscribe({\n        next: (response) => {\n          if (response.success) {\n            // Replace the optimistic comment with the server response\n            const commentIndex = post.comments.findIndex(c => c._id === newComment._id);\n            if (commentIndex !== -1) {\n              post.comments[commentIndex] = response.comment;\n            }\n            if (post.analytics) {\n              post.analytics.comments += 1;\n            }\n          }\n        },\n        error: (error) => {\n          // Remove optimistic comment on error\n          post.comments = post.comments.filter(c => c._id !== newComment._id);\n          this.commentTexts[post._id] = commentText; // Restore comment text\n          console.error('Error adding comment:', error);\n        }\n      })\n    );\n  }\n\n  viewAllComments(post: Post) {\n    this.router.navigate(['/post', post._id, 'comments']);\n  }\n\n  viewPost(post: Post) {\n    // Navigate to post detail view\n    this.router.navigate(['/post', post._id]);\n  }\n\n  viewPostDetail(postId: string) {\n    // Navigate to post detail view by ID\n    this.router.navigate(['/post', postId]);\n  }\n\n  searchHashtag(hashtag: string) {\n    this.router.navigate(['/search'], { queryParams: { hashtag } });\n  }\n\n  // E-commerce actions\n  buyNow(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: { productId: product._id, source: 'post' }\n      });\n    }\n  }\n\n  addToCart(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      const size = post.products[0].size;\n      const color = post.products[0].color;\n\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n\n      this.subscriptions.push(\n        this.cartService.addToCart(product._id, 1, size, color).subscribe({\n          next: (response) => {\n            if (response.success) {\n              // Track analytics using new service\n              this.socialFeaturesService.trackProductClick('post', post._id, product._id, 'add_to_cart').subscribe();\n              alert(`${product.name} added to cart!`);\n            }\n          },\n          error: (error) => {\n            console.error('Error adding to cart:', error);\n            alert('Failed to add to cart. Please try again.');\n          }\n        })\n      );\n    }\n  }\n\n  addToWishlist(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      const size = post.products[0].size;\n      const color = post.products[0].color;\n\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n\n      this.subscriptions.push(\n        this.wishlistService.addToWishlist(product._id).subscribe({\n          next: (response) => {\n            if (response.success) {\n              // Track analytics using new service\n              this.socialFeaturesService.trackProductClick('post', post._id, product._id, 'add_to_wishlist').subscribe();\n              alert(`${product.name} added to wishlist!`);\n            }\n          },\n          error: (error) => {\n            console.error('Error adding to wishlist:', error);\n            alert('Failed to add to wishlist. Please try again.');\n          }\n        })\n      );\n    }\n  }\n\n  viewProduct(post: Post, product: any) {\n    // Track analytics\n    this.trackProductClick(post._id, product._id, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/products', product._id]);\n  }\n\n  private trackProductClick(postId: string, productId: string, action: string) {\n    // Track product click analytics using new service\n    this.socialFeaturesService.trackProductClick('post', postId, productId, action).subscribe({\n      next: (response) => {\n        console.log('Analytics tracked:', response);\n      },\n      error: (error) => {\n        console.error('Error tracking analytics:', error);\n      }\n    });\n  }\n\n  // Product modal\n  showProductDetails(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: { productId: this.selectedProduct._id, source: 'post' }\n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n\n  addMockStories() {\n    // Removed mock stories - use real API data only\n    this.stories = [];\n    console.log('Mock stories removed - using real API data only');\n  }\n\n}"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAO5C,SAASC,uBAAuB,QAA4B,gEAAgE;AA+tBrH,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAW9BC,YACUC,MAAc,EACdC,kBAAsC,EACtCC,qBAA4C,EAC5CC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC;IALhC,KAAAL,MAAM,GAANA,MAAM;IACN,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAhBzB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,YAAY,GAA8B,EAAE;IAC5C,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,IAAI;IAEX,KAAAC,aAAa,GAAmB,EAAE;EASvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAP,eAAeA,CAAA;IACb,IAAI,CAACL,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;IACpD,IAAI,CAAC,IAAI,CAACb,WAAW,EAAE;MACrB;MACA,IAAI,CAACA,WAAW,GAAG;QACjBc,GAAG,EAAE,YAAY;QACjBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;OACT;;IAEHC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACnB,WAAW,CAAC;EACrE;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACH,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC7B,kBAAkB,CAACe,WAAW,EAAE,CAACe,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB;UACA,MAAMC,WAAW,GAAGF,QAAQ,CAAC1B,OAAO,CAAC6B,MAAM,CAAC,CAACC,GAAQ,EAAEC,KAAU,KAAI;YACnE,MAAMC,MAAM,GAAGD,KAAK,CAACE,IAAI,CAAChB,GAAG;YAC7B,IAAI,CAACa,GAAG,CAACE,MAAM,CAAC,EAAE;cAChBF,GAAG,CAACE,MAAM,CAAC,GAAG;gBACZC,IAAI,EAAEF,KAAK,CAACE,IAAI;gBAChBC,MAAM,EAAE,KAAK;gBACblC,OAAO,EAAE;eACV;;YAEH8B,GAAG,CAACE,MAAM,CAAC,CAAChC,OAAO,CAACuB,IAAI,CAACQ,KAAK,CAAC;YAC/B,OAAOD,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC;UAEN,IAAI,CAAC9B,OAAO,GAAGmC,MAAM,CAACC,MAAM,CAACR,WAAW,CAAC;UACzCP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACtB,OAAO,CAAC;;MAE9D,CAAC;MACDqC,KAAK,EAAGA,KAAK,IAAI;QACfhB,OAAO,CAACgB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACrC,OAAO,GAAG,EAAE;MACnB;KACD,CAAC,CACH;EACH;EAEAU,SAASA,CAAA;IACP,IAAI,CAACN,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACE,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC7B,kBAAkB,CAACgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACc,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC5B,KAAK,GAAG2B,QAAQ,CAAC3B,KAAK,CAACuC,GAAG,CAAEC,IAAS,KAAM;YAC9C,GAAGA,IAAI;YACPC,OAAO,EAAE,IAAI,CAACC,gBAAgB,CAACF,IAAI,CAACG,KAAK,CAAC;YAC1CC,OAAO,EAAE,IAAI,CAACC,gBAAgB,CAACL,IAAI,CAACM,KAAK;WAC1C,CAAC,CAAC;;QAEL,IAAI,CAACzC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDiC,KAAK,EAAGA,KAAK,IAAI;QACfhB,OAAO,CAACgB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACjC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEQqC,gBAAgBA,CAACC,KAAY;IACnC,IAAI,CAAC,IAAI,CAACvC,WAAW,IAAI,CAACuC,KAAK,EAAE,OAAO,KAAK;IAC7C,OAAOA,KAAK,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,IAAI,CAAC9B,WAAY,CAACc,GAAG,CAAC;EAChE;EAEQ2B,gBAAgBA,CAACC,KAAY;IACnC,IAAI,CAAC,IAAI,CAAC1C,WAAW,IAAI,CAAC0C,KAAK,EAAE,OAAO,KAAK;IAC7C,OAAOA,KAAK,CAACC,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACf,IAAI,KAAK,IAAI,CAAC9B,WAAY,CAACc,GAAG,CAAC;EAChE;EAEAgC,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7C,OAAO,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;IAExC,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,MAAM8C,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACrD,KAAK,CAACsD,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;IAEnD,IAAI,CAAC/C,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC7B,kBAAkB,CAACgB,SAAS,CAACwC,IAAI,EAAE,EAAE,CAAC,CAAC1B,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAC3B,KAAK,CAACsD,MAAM,GAAG,CAAC,EAAE;UACjD,MAAMC,QAAQ,GAAG5B,QAAQ,CAAC3B,KAAK,CAACuC,GAAG,CAAEC,IAAS,KAAM;YAClD,GAAGA,IAAI;YACPC,OAAO,EAAE,IAAI,CAACC,gBAAgB,CAACF,IAAI,CAACG,KAAK,CAAC;YAC1CC,OAAO,EAAE,IAAI,CAACC,gBAAgB,CAACL,IAAI,CAACM,KAAK;WAC1C,CAAC,CAAC;UACH,IAAI,CAAC9C,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGuD,QAAQ,CAAC;UAEzC;UACA,IAAI,CAACjD,YAAY,GAAGqB,QAAQ,CAAC3B,KAAK,CAACsD,MAAM,KAAK,EAAE;SACjD,MAAM;UACL,IAAI,CAAChD,YAAY,GAAG,KAAK;;QAE3B,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDiC,KAAK,EAAGA,KAAK,IAAI;QACfhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACjC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEA;EACAmD,WAAWA,CAAA;IACT,IAAI,CAAC9D,MAAM,CAAC+D,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAC,SAASA,CAACzB,MAAc;IACtB;IACAX,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,MAAM,CAAC;IAClD,IAAI,CAACvC,MAAM,CAAC+D,QAAQ,CAAC,CAAC,UAAU,EAAExB,MAAM,CAAC,CAAC;EAC5C;EAEA0B,WAAWA,CAAA;IACT;IACArC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAAC7B,MAAM,CAAC+D,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAG,eAAeA,CAACC,UAAA,GAAqB,CAAC;IACpC;IACAvC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsC,UAAU,CAAC;IACzD,IAAI,CAACnE,MAAM,CAAC+D,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;MACjCK,WAAW,EAAE;QAAEC,KAAK,EAAEF;MAAU;KACjC,CAAC;EACJ;EAEAG,YAAYA,CAACC,KAAoC;IAC/C;IACA,IAAI,CAACL,eAAe,CAACK,KAAK,CAACF,KAAK,CAAC;EACnC;EAEA;EACAG,WAAWA,CAACjC,MAAc;IACxB,IAAI,CAACvC,MAAM,CAAC+D,QAAQ,CAAC,CAAC,UAAU,EAAExB,MAAM,CAAC,CAAC;EAC5C;EAEAkC,YAAYA,CAAC3B,IAAU;IACrB;IACAlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiB,IAAI,CAAC;EAC1C;EAEA4B,UAAUA,CAAC5B,IAAU;IACnB,IAAI,CAAC,IAAI,CAAC3C,WAAW,CAACwE,eAAe,EAAE;MACrC,IAAI,CAAC3E,MAAM,CAAC+D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMa,QAAQ,GAAG9B,IAAI,CAACC,OAAO;IAE7B;IACAD,IAAI,CAACC,OAAO,GAAG,CAACD,IAAI,CAACC,OAAO;IAC5B,MAAMrC,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;IACrD,IAAIuB,IAAI,CAACC,OAAO,EAAE;MAChBD,IAAI,CAACG,KAAK,CAACnB,IAAI,CAAC;QACdU,IAAI,EAAE9B,WAAW,EAAEc,GAAG,IAAI,EAAE;QAC5BqD,OAAO,EAAE,IAAIC,IAAI;OAClB,CAAC;KACH,MAAM;MACLhC,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAAC8B,MAAM,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK9B,WAAW,EAAEc,GAAG,CAAC;;IAGxE;IACA,IAAI,CAACX,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC5B,qBAAqB,CAAC8E,cAAc,CAAClC,IAAI,CAACtB,GAAG,CAAC,CAACO,SAAS,CAAC;MAC5DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBY,IAAI,CAACC,OAAO,GAAGd,QAAQ,CAACc,OAAQ;UAChC;UACA,IAAId,QAAQ,CAACgD,UAAU,KAAKC,SAAS,EAAE;YACrC,IAAIpC,IAAI,CAACqC,SAAS,EAAE;cAClBrC,IAAI,CAACqC,SAAS,CAAClC,KAAK,GAAGhB,QAAQ,CAACgD,UAAU;;;;MAIlD,CAAC;MACDrC,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,MAAMlC,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;QACrDuB,IAAI,CAACC,OAAO,GAAG6B,QAAQ;QACvB,IAAIA,QAAQ,EAAE;UACZ9B,IAAI,CAACG,KAAK,CAACnB,IAAI,CAAC;YACdU,IAAI,EAAE9B,WAAW,EAAEc,GAAG,IAAI,EAAE;YAC5BqD,OAAO,EAAE,IAAIC,IAAI;WAClB,CAAC;SACH,MAAM;UACLhC,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAAC8B,MAAM,CAACzB,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK9B,WAAW,EAAEc,GAAG,CAAC;;QAExEI,OAAO,CAACgB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC,CACH;EACH;EAEAwC,UAAUA,CAACtC,IAAU;IACnB,IAAI,CAAC,IAAI,CAAC3C,WAAW,CAACwE,eAAe,EAAE;MACrC,IAAI,CAAC3E,MAAM,CAAC+D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMsB,QAAQ,GAAGvC,IAAI,CAACI,OAAO;IAE7B;IACA,MAAMxC,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;IACrDuB,IAAI,CAACI,OAAO,GAAG,CAACJ,IAAI,CAACI,OAAO;IAC5B,IAAIJ,IAAI,CAACI,OAAO,EAAE;MAChBJ,IAAI,CAACM,KAAK,CAACtB,IAAI,CAAC;QACdU,IAAI,EAAE9B,WAAW,EAAEc,GAAG,IAAI,EAAE;QAC5B8D,OAAO,EAAE,IAAIR,IAAI;OAClB,CAAC;KACH,MAAM;MACLhC,IAAI,CAACM,KAAK,GAAGN,IAAI,CAACM,KAAK,CAAC2B,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACf,IAAI,KAAK9B,WAAW,EAAEc,GAAG,CAAC;;IAGxE;IACA,IAAI,CAACX,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC5B,qBAAqB,CAACqF,cAAc,CAACzC,IAAI,CAACtB,GAAG,CAAC,CAACO,SAAS,CAAC;MAC5DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBY,IAAI,CAACI,OAAO,GAAGjB,QAAQ,CAACiB,OAAQ;UAChC;UACA,IAAIjB,QAAQ,CAACuD,UAAU,KAAKN,SAAS,EAAE;YACrC,IAAIpC,IAAI,CAACqC,SAAS,EAAE;cAClBrC,IAAI,CAACqC,SAAS,CAAC/B,KAAK,GAAGnB,QAAQ,CAACuD,UAAU;;;;MAIlD,CAAC;MACD5C,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,MAAMlC,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;QACrDuB,IAAI,CAACI,OAAO,GAAGmC,QAAQ;QACvB,IAAIA,QAAQ,EAAE;UACZvC,IAAI,CAACM,KAAK,CAACtB,IAAI,CAAC;YACdU,IAAI,EAAE9B,WAAW,EAAEc,GAAG,IAAI,EAAE;YAC5B8D,OAAO,EAAE,IAAIR,IAAI;WAClB,CAAC;SACH,MAAM;UACLhC,IAAI,CAACM,KAAK,GAAGN,IAAI,CAACM,KAAK,CAAC2B,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACf,IAAI,KAAK9B,WAAW,EAAEc,GAAG,CAAC;;QAExEI,OAAO,CAACgB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC,CACH;EACH;EAEA6C,SAASA,CAAC3C,IAAU;IAClB;IACAlB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiB,IAAI,CAAC;IAEhC,IAAI4C,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,GAAG9C,IAAI,CAACN,IAAI,CAACf,QAAQ,SAAS;QACrCoE,IAAI,EAAE/C,IAAI,CAACgD,OAAO;QAClBC,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACL;MACAR,SAAS,CAACS,SAAS,CAACC,SAAS,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACnDG,KAAK,CAAC,2BAA2B,CAAC;;EAEtC;EAEAC,YAAYA,CAACC,MAAc;IACzB,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWH,MAAM,EAAE,CAAqB;IACrF,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEAC,UAAUA,CAAC9D,IAAU;IACnB,MAAM+D,WAAW,GAAG,IAAI,CAACrG,YAAY,CAACsC,IAAI,CAACtB,GAAG,CAAC;IAC/C,IAAI,CAACqF,WAAW,EAAEC,IAAI,EAAE,EAAE;IAE1B,IAAI,CAAC,IAAI,CAAC3G,WAAW,CAACwE,eAAe,EAAE;MACrC,IAAI,CAAC3E,MAAM,CAAC+D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMrD,WAAW,GAAG,IAAI,CAACP,WAAW,CAACoB,gBAAgB;IACrD,MAAMwF,UAAU,GAAG;MACjBvF,GAAG,EAAEsD,IAAI,CAACkC,GAAG,EAAE,CAACC,QAAQ,EAAE;MAC1BzE,IAAI,EAAE;QACJhB,GAAG,EAAEd,WAAW,EAAEc,GAAG,IAAI,EAAE;QAC3BC,QAAQ,EAAEf,WAAW,EAAEe,QAAQ,IAAI,EAAE;QACrCC,QAAQ,EAAEhB,WAAW,EAAEgB,QAAQ,IAAI,EAAE;QACrCC,MAAM,EAAEjB,WAAW,EAAEiB,MAAM,IAAI;OAChC;MACDkE,IAAI,EAAEgB,WAAW,CAACC,IAAI,EAAE;MACxBI,WAAW,EAAE,IAAIpC,IAAI;KACtB;IAED;IACAhC,IAAI,CAACqE,QAAQ,CAACrF,IAAI,CAACiF,UAAU,CAAC;IAC9B,IAAI,CAACvG,YAAY,CAACsC,IAAI,CAACtB,GAAG,CAAC,GAAG,EAAE;IAEhC;IACA,IAAI,CAACX,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC5B,qBAAqB,CAACkH,cAAc,CAACtE,IAAI,CAACtB,GAAG,EAAEqF,WAAW,CAACC,IAAI,EAAE,CAAC,CAAC/E,SAAS,CAAC;MAChFC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB;UACA,MAAMmF,YAAY,GAAGvE,IAAI,CAACqE,QAAQ,CAACG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/F,GAAG,KAAKuF,UAAU,CAACvF,GAAG,CAAC;UAC3E,IAAI6F,YAAY,KAAK,CAAC,CAAC,EAAE;YACvBvE,IAAI,CAACqE,QAAQ,CAACE,YAAY,CAAC,GAAGpF,QAAQ,CAACuF,OAAO;;UAEhD,IAAI1E,IAAI,CAACqC,SAAS,EAAE;YAClBrC,IAAI,CAACqC,SAAS,CAACgC,QAAQ,IAAI,CAAC;;;MAGlC,CAAC;MACDvE,KAAK,EAAGA,KAAK,IAAI;QACf;QACAE,IAAI,CAACqE,QAAQ,GAAGrE,IAAI,CAACqE,QAAQ,CAACpC,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAAC/F,GAAG,KAAKuF,UAAU,CAACvF,GAAG,CAAC;QACnE,IAAI,CAAChB,YAAY,CAACsC,IAAI,CAACtB,GAAG,CAAC,GAAGqF,WAAW,CAAC,CAAC;QAC3CjF,OAAO,CAACgB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC,CACH;EACH;EAEA6E,eAAeA,CAAC3E,IAAU;IACxB,IAAI,CAAC9C,MAAM,CAAC+D,QAAQ,CAAC,CAAC,OAAO,EAAEjB,IAAI,CAACtB,GAAG,EAAE,UAAU,CAAC,CAAC;EACvD;EAEAkG,QAAQA,CAAC5E,IAAU;IACjB;IACA,IAAI,CAAC9C,MAAM,CAAC+D,QAAQ,CAAC,CAAC,OAAO,EAAEjB,IAAI,CAACtB,GAAG,CAAC,CAAC;EAC3C;EAEAmG,cAAcA,CAACpB,MAAc;IAC3B;IACA,IAAI,CAACvG,MAAM,CAAC+D,QAAQ,CAAC,CAAC,OAAO,EAAEwC,MAAM,CAAC,CAAC;EACzC;EAEAqB,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAAC7H,MAAM,CAAC+D,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAAEK,WAAW,EAAE;QAAEyD;MAAO;IAAE,CAAE,CAAC;EACjE;EAEA;EACAC,MAAMA,CAAChF,IAAU;IACf,IAAIA,IAAI,CAACiF,QAAQ,CAACnE,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMoE,OAAO,GAAGlF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;MACxC,IAAI,CAAChI,MAAM,CAAC+D,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCK,WAAW,EAAE;UAAE6D,SAAS,EAAED,OAAO,CAACxG,GAAG;UAAE0G,MAAM,EAAE;QAAM;OACtD,CAAC;;EAEN;EAEAC,SAASA,CAACrF,IAAU;IAClB,IAAIA,IAAI,CAACiF,QAAQ,CAACnE,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMoE,OAAO,GAAGlF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;MACxC,MAAMI,IAAI,GAAGtF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACK,IAAI;MAClC,MAAMC,KAAK,GAAGvF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACM,KAAK;MAEpC,IAAI,CAAC,IAAI,CAAClI,WAAW,CAACwE,eAAe,EAAE;QACrC,IAAI,CAAC3E,MAAM,CAAC+D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACrC;;MAGF,IAAI,CAAClD,aAAa,CAACiB,IAAI,CACrB,IAAI,CAAC1B,WAAW,CAAC+H,SAAS,CAACH,OAAO,CAACxG,GAAG,EAAE,CAAC,EAAE4G,IAAI,EAAEC,KAAK,CAAC,CAACtG,SAAS,CAAC;QAChEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA,IAAI,CAAChC,qBAAqB,CAACoI,iBAAiB,CAAC,MAAM,EAAExF,IAAI,CAACtB,GAAG,EAAEwG,OAAO,CAACxG,GAAG,EAAE,aAAa,CAAC,CAACO,SAAS,EAAE;YACtGsE,KAAK,CAAC,GAAG2B,OAAO,CAACO,IAAI,iBAAiB,CAAC;;QAE3C,CAAC;QACD3F,KAAK,EAAGA,KAAK,IAAI;UACfhB,OAAO,CAACgB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CyD,KAAK,CAAC,0CAA0C,CAAC;QACnD;OACD,CAAC,CACH;;EAEL;EAEAmC,aAAaA,CAAC1F,IAAU;IACtB,IAAIA,IAAI,CAACiF,QAAQ,CAACnE,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMoE,OAAO,GAAGlF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;MACxC,MAAMI,IAAI,GAAGtF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACK,IAAI;MAClC,MAAMC,KAAK,GAAGvF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAACM,KAAK;MAEpC,IAAI,CAAC,IAAI,CAAClI,WAAW,CAACwE,eAAe,EAAE;QACrC,IAAI,CAAC3E,MAAM,CAAC+D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACrC;;MAGF,IAAI,CAAClD,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACzB,eAAe,CAACmI,aAAa,CAACR,OAAO,CAACxG,GAAG,CAAC,CAACO,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA,IAAI,CAAChC,qBAAqB,CAACoI,iBAAiB,CAAC,MAAM,EAAExF,IAAI,CAACtB,GAAG,EAAEwG,OAAO,CAACxG,GAAG,EAAE,iBAAiB,CAAC,CAACO,SAAS,EAAE;YAC1GsE,KAAK,CAAC,GAAG2B,OAAO,CAACO,IAAI,qBAAqB,CAAC;;QAE/C,CAAC;QACD3F,KAAK,EAAGA,KAAK,IAAI;UACfhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDyD,KAAK,CAAC,8CAA8C,CAAC;QACvD;OACD,CAAC,CACH;;EAEL;EAEAoC,WAAWA,CAAC3F,IAAU,EAAEkF,OAAY;IAClC;IACA,IAAI,CAACM,iBAAiB,CAACxF,IAAI,CAACtB,GAAG,EAAEwG,OAAO,CAACxG,GAAG,EAAE,cAAc,CAAC;IAE7D;IACA,IAAI,CAACxB,MAAM,CAAC+D,QAAQ,CAAC,CAAC,WAAW,EAAEiE,OAAO,CAACxG,GAAG,CAAC,CAAC;EAClD;EAEQ8G,iBAAiBA,CAAC/B,MAAc,EAAE0B,SAAiB,EAAES,MAAc;IACzE;IACA,IAAI,CAACxI,qBAAqB,CAACoI,iBAAiB,CAAC,MAAM,EAAE/B,MAAM,EAAE0B,SAAS,EAAES,MAAM,CAAC,CAAC3G,SAAS,CAAC;MACxFC,IAAI,EAAGC,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,QAAQ,CAAC;MAC7C,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACfhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA;EACA+F,kBAAkBA,CAACX,OAAY;IAC7B,IAAI,CAACvH,eAAe,GAAGuH,OAAO;EAChC;EAEAY,iBAAiBA,CAAA;IACf,IAAI,CAACnI,eAAe,GAAG,IAAI;EAC7B;EAEAoI,aAAaA,CAAA;IACX,IAAI,IAAI,CAACpI,eAAe,EAAE;MACxB,IAAI,CAACT,MAAM,CAAC+D,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCK,WAAW,EAAE;UAAE6D,SAAS,EAAE,IAAI,CAACxH,eAAe,CAACe,GAAG;UAAE0G,MAAM,EAAE;QAAM;OACnE,CAAC;;EAEN;EAEAY,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACrI,eAAe,EAAE;MACxB;MACAmB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACpB,eAAe,CAAC;MACzD4F,KAAK,CAAC,GAAG,IAAI,CAAC5F,eAAe,CAAC8H,IAAI,iBAAiB,CAAC;MACpD,IAAI,CAACK,iBAAiB,EAAE;;EAE5B;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACtI,eAAe,EAAE;MACxB;MACAmB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACpB,eAAe,CAAC;MAC7D4F,KAAK,CAAC,GAAG,IAAI,CAAC5F,eAAe,CAAC8H,IAAI,qBAAqB,CAAC;MACxD,IAAI,CAACK,iBAAiB,EAAE;;EAE5B;EAEAI,UAAUA,CAACC,IAAU;IACnB,MAAMjC,GAAG,GAAG,IAAIlC,IAAI,EAAE;IACtB,MAAMoE,MAAM,GAAGlC,GAAG,CAACmC,OAAO,EAAE,GAAG,IAAIrE,IAAI,CAACmE,IAAI,CAAC,CAACE,OAAO,EAAE;IACvD,MAAMC,WAAW,GAAG1F,IAAI,CAACC,KAAK,CAACuF,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMG,SAAS,GAAG3F,IAAI,CAACC,KAAK,CAACuF,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMI,QAAQ,GAAG5F,IAAI,CAACC,KAAK,CAACuF,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE3D,IAAIE,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK;IACjC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,IAAIC,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,GAAG;IACvC,OAAO,IAAIxE,IAAI,CAACmE,IAAI,CAAC,CAACM,kBAAkB,EAAE;EAC5C;EAEArI,cAAcA,CAAA;IACZ;IACA,IAAI,CAACX,OAAO,GAAG,EAAE;IACjBqB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;EAChE;CAED;AA5gBY/B,mBAAmB,GAAA0J,UAAA,EAtqB/B9J,SAAS,CAAC;EACT+J,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChK,YAAY,EAAEC,WAAW,EAAEC,uBAAuB,CAAC;EAC7D+J,WAAW,EAAE,8BAA8B;EAC3CC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+pBR;CACF,CAAC,C,EACW/J,mBAAmB,CA4gB/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}