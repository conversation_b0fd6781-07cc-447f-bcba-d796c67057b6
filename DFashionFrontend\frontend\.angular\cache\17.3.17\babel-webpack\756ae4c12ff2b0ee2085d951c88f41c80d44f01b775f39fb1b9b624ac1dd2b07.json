{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nfunction HomeComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h1\", 15);\n    i0.ɵɵtext(3, \"DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ion-icon\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17);\n    i0.ɵɵelement(6, \"ion-icon\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTabMenu());\n    });\n    i0.ɵɵelement(8, \"ion-icon\", 20);\n    i0.ɵɵtemplate(9, HomeComponent_div_1_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSidebar());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasNotifications);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 37);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 38);\n  }\n}\nfunction HomeComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_9_Template_div_click_0_listener() {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewStory(story_r5));\n    })(\"touchstart\", function HomeComponent_div_2_div_9_Template_div_touchstart_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchStart($event, story_r5));\n    })(\"touchend\", function HomeComponent_div_2_div_9_Template_div_touchend_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchEnd($event, story_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 27);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵtemplate(3, HomeComponent_div_2_div_9_div_3_Template, 1, 0, \"div\", 35)(4, HomeComponent_div_2_div_9_div_4_Template, 1, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-story\", story_r5.hasStory)(\"viewed\", story_r5.viewed)(\"touching\", story_r5.touching);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", story_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r5.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.username);\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createStory());\n    });\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵelement(4, \"img\", 28);\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵelement(6, \"ion-icon\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8, \"Your story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, HomeComponent_div_2_div_9_Template, 7, 11, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.instagramStories)(\"ngForTrackBy\", ctx_r1.trackByStoryId);\n  }\n}\nfunction HomeComponent_app_view_add_stories_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-view-add-stories\");\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h3\");\n    i0.ɵɵtext(3, \"Discover\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"trending\"));\n    });\n    i0.ɵɵelementStart(7, \"div\", 46);\n    i0.ɵɵelement(8, \"ion-icon\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 48);\n    i0.ɵɵtext(10, \"Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 49);\n    i0.ɵɵtext(12, \"Hot products right now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"brands\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 50);\n    i0.ɵɵelement(15, \"ion-icon\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 48);\n    i0.ɵɵtext(17, \"Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 49);\n    i0.ɵɵtext(19, \"Top fashion brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"arrivals\"));\n    });\n    i0.ɵɵelementStart(21, \"div\", 52);\n    i0.ɵɵelement(22, \"ion-icon\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 48);\n    i0.ɵɵtext(24, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 49);\n    i0.ɵɵtext(26, \"Latest arrivals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"suggested\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 54);\n    i0.ɵɵelement(29, \"ion-icon\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 48);\n    i0.ɵɵtext(31, \"For You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 49);\n    i0.ɵɵtext(33, \"Personalized picks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"influencers\"));\n    });\n    i0.ɵɵelementStart(35, \"div\", 56);\n    i0.ɵɵelement(36, \"ion-icon\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 48);\n    i0.ɵɵtext(38, \"Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 49);\n    i0.ɵɵtext(40, \"Top fashion creators\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_41_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"categories\"));\n    });\n    i0.ɵɵelementStart(42, \"div\", 58);\n    i0.ɵɵelement(43, \"ion-icon\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 48);\n    i0.ɵɵtext(45, \"Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 49);\n    i0.ɵɵtext(47, \"Browse by category\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"div\", 63);\n    i0.ɵɵelement(4, \"img\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"h3\");\n    i0.ɵɵtext(7, \"Your Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"@username\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_11_Template_ion_icon_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 66);\n    i0.ɵɵelement(12, \"app-sidebar\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-trending-products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-featured-brands\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-new-arrivals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-suggested-for-you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"app-top-fashion-influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"ion-icon\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r11.name);\n  }\n}\nfunction HomeComponent_div_12_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵtemplate(2, HomeComponent_div_12_div_11_div_2_Template, 5, 2, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction HomeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_12_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebarContent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 69);\n    i0.ɵɵtemplate(6, HomeComponent_div_12_div_6_Template, 2, 0, \"div\", 70)(7, HomeComponent_div_12_div_7_Template, 2, 0, \"div\", 70)(8, HomeComponent_div_12_div_8_Template, 2, 0, \"div\", 70)(9, HomeComponent_div_12_div_9_Template, 2, 0, \"div\", 70)(10, HomeComponent_div_12_div_10_Template, 2, 0, \"div\", 70)(11, HomeComponent_div_12_div_11_Template, 3, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarContentOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentSidebarTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"trending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"brands\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"arrivals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"suggested\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"influencers\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"categories\");\n  }\n}\nfunction HomeComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"ion-icon\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 80);\n    i0.ɵɵelement(4, \"ion-icon\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 80);\n    i0.ɵɵelement(6, \"ion-icon\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 80);\n    i0.ɵɵelement(8, \"ion-icon\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"div\", 84);\n    i0.ɵɵelement(11, \"img\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor() {\n      this.isMobile = false;\n      this.isSidebarOpen = false;\n      this.isTabMenuOpen = false;\n      this.isSidebarContentOpen = false;\n      this.currentSidebarTab = '';\n      this.currentSidebarTitle = '';\n      this.hasNotifications = true; // Example notification state\n      this.window = window; // For template access\n      // TikTok-style interaction states\n      this.isLiked = false;\n      // Instagram Stories Data - Enhanced for responsive design and mobile app\n      this.instagramStories = [{\n        id: 1,\n        username: 'zara',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }, {\n        id: 2,\n        username: 'nike',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }, {\n        id: 3,\n        username: 'adidas',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: true,\n        touching: false\n      }, {\n        id: 4,\n        username: 'h&m',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }, {\n        id: 5,\n        username: 'uniqlo',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }, {\n        id: 6,\n        username: 'gucci',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: true,\n        touching: false\n      }, {\n        id: 7,\n        username: 'prada',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }, {\n        id: 8,\n        username: 'versace',\n        avatar: '/assets/images/default-avatar.svg',\n        hasStory: true,\n        viewed: false,\n        touching: false\n      }];\n      // Categories Data\n      this.categories = [{\n        name: 'Women',\n        icon: 'woman'\n      }, {\n        name: 'Men',\n        icon: 'man'\n      }, {\n        name: 'Kids',\n        icon: 'happy'\n      }, {\n        name: 'Shoes',\n        icon: 'footsteps'\n      }, {\n        name: 'Bags',\n        icon: 'bag'\n      }, {\n        name: 'Accessories',\n        icon: 'watch'\n      }, {\n        name: 'Beauty',\n        icon: 'flower'\n      }, {\n        name: 'Sports',\n        icon: 'fitness'\n      }];\n      this.preventScroll = e => {\n        if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n          e.preventDefault();\n        }\n      };\n    }\n    ngOnInit() {\n      this.checkScreenSize();\n      console.log('Home component initialized:', {\n        isMobile: this.isMobile,\n        instagramStories: this.instagramStories.length\n      });\n      // Prevent body scroll when sidebar is open\n      document.addEventListener('touchmove', this.preventScroll, {\n        passive: false\n      });\n    }\n    ngOnDestroy() {\n      document.removeEventListener('touchmove', this.preventScroll);\n    }\n    onResize(event) {\n      this.checkScreenSize();\n      if (!this.isMobile && this.isSidebarOpen) {\n        this.closeSidebar();\n      }\n    }\n    checkScreenSize() {\n      // More comprehensive mobile detection\n      const width = window.innerWidth;\n      const userAgent = navigator.userAgent;\n      const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n      // Consider it mobile if width <= 768px OR if it's a mobile user agent\n      this.isMobile = width <= 768 || isMobileUserAgent;\n      console.log('Screen size check:', {\n        width: width,\n        height: window.innerHeight,\n        isMobile: this.isMobile,\n        isMobileUserAgent: isMobileUserAgent,\n        userAgent: userAgent\n      });\n    }\n    toggleSidebar() {\n      this.isSidebarOpen = !this.isSidebarOpen;\n      this.toggleBodyScroll();\n    }\n    closeSidebar() {\n      this.isSidebarOpen = false;\n      this.toggleBodyScroll();\n    }\n    toggleBodyScroll() {\n      if (this.isSidebarOpen) {\n        document.body.style.overflow = 'hidden';\n      } else {\n        document.body.style.overflow = '';\n      }\n    }\n    // Tab Menu Methods\n    toggleTabMenu() {\n      this.isTabMenuOpen = !this.isTabMenuOpen;\n      this.toggleBodyScroll();\n    }\n    closeTabMenu() {\n      this.isTabMenuOpen = false;\n      this.toggleBodyScroll();\n    }\n    openSidebarTab(tabType) {\n      this.currentSidebarTab = tabType;\n      this.isSidebarContentOpen = true;\n      this.isTabMenuOpen = false;\n      // Set title based on tab type\n      const titles = {\n        'trending': 'Trending Products',\n        'brands': 'Featured Brands',\n        'arrivals': 'New Arrivals',\n        'suggested': 'Suggested for You',\n        'influencers': 'Fashion Influencers',\n        'categories': 'Categories'\n      };\n      this.currentSidebarTitle = titles[tabType] || 'Discover';\n      this.toggleBodyScroll();\n    }\n    closeSidebarContent() {\n      this.isSidebarContentOpen = false;\n      this.currentSidebarTab = '';\n      this.toggleBodyScroll();\n    }\n    // TikTok-style interaction methods\n    toggleLike() {\n      this.isLiked = !this.isLiked;\n      // TODO: Implement like functionality with backend\n      console.log('Like toggled:', this.isLiked);\n    }\n    openComments() {\n      // TODO: Implement comments modal/page\n      console.log('Opening comments...');\n    }\n    shareContent() {\n      // TODO: Implement share functionality\n      console.log('Sharing content...');\n      if (navigator.share) {\n        navigator.share({\n          title: 'DFashion',\n          text: 'Check out this amazing fashion content!',\n          url: window.location.href\n        });\n      }\n    }\n    openMusic() {\n      // TODO: Implement music/audio functionality\n      console.log('Opening music...');\n    }\n    // Stories functionality\n    createStory() {\n      console.log('Create story clicked');\n      // TODO: Implement story creation\n    }\n    viewStory(story) {\n      console.log('View story:', story);\n      // TODO: Implement story viewer\n    }\n    trackByStoryId(index, story) {\n      return story.id || index;\n    }\n    // Enhanced touch interactions for mobile app\n    onStoryTouchStart(event, story) {\n      story.touching = true;\n      // Add haptic feedback if available\n      if ('vibrate' in navigator) {\n        navigator.vibrate(10);\n      }\n    }\n    onStoryTouchEnd(event, story) {\n      story.touching = false;\n    }\n    // TikTok-style interaction methods\n    onLikeClick() {\n      this.isLiked = !this.isLiked;\n      console.log('Like clicked:', this.isLiked);\n      // TODO: Implement like functionality with backend\n    }\n    onCommentClick() {\n      console.log('Comment clicked');\n      // TODO: Implement comment functionality\n    }\n    onShareClick() {\n      console.log('Share clicked');\n      // TODO: Implement share functionality\n    }\n    onBookmarkClick() {\n      console.log('Bookmark clicked');\n      // TODO: Implement bookmark functionality\n    }\n    // Mobile quick actions navigation methods\n    navigateToTrending() {\n      console.log('Navigate to trending');\n      // TODO: Implement navigation to trending page\n    }\n    navigateToNewArrivals() {\n      console.log('Navigate to new arrivals');\n      // TODO: Implement navigation to new arrivals page\n    }\n    navigateToOffers() {\n      console.log('Navigate to offers');\n      // TODO: Implement navigation to offers page\n    }\n    navigateToCategories() {\n      console.log('Navigate to categories');\n      // TODO: Implement navigation to categories page\n    }\n    navigateToWishlist() {\n      console.log('Navigate to wishlist');\n      // TODO: Implement navigation to wishlist page\n    }\n    navigateToCart() {\n      console.log('Navigate to cart');\n      // TODO: Implement navigation to cart page\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"resize\", function HomeComponent_resize_HostBindingHandler($event) {\n              return ctx.onResize($event);\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 14,\n        vars: 15,\n        consts: [[1, \"home-container\"], [\"class\", \"mobile-header instagram-style\", 4, \"ngIf\"], [\"class\", \"instagram-stories-section\", 4, \"ngIf\"], [1, \"content-grid\"], [1, \"main-content\"], [4, \"ngIf\"], [1, \"desktop-sidebar\"], [\"class\", \"tab-menu-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"sidebar-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"instagram-tab-menu\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"mobile-sidebar\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"sidebar-content-modal\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"instagram-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-header\", \"instagram-style\"], [1, \"header-left\"], [1, \"app-logo\"], [\"name\", \"chevron-down\", 1, \"logo-dropdown\"], [1, \"header-right\"], [\"name\", \"heart-outline\", 1, \"header-icon\"], [1, \"menu-icon-container\", 3, \"click\"], [\"name\", \"grid-outline\", 1, \"header-icon\", \"menu-icon\"], [\"class\", \"notification-dot\", 4, \"ngIf\"], [\"name\", \"menu-outline\", 1, \"header-icon\", \"menu-icon\"], [1, \"notification-dot\"], [1, \"instagram-stories-section\"], [1, \"stories-container\"], [1, \"story-item\", \"your-story\", 3, \"click\"], [1, \"story-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Your story\"], [1, \"add-story-btn\"], [\"name\", \"add\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", \"touchstart\", \"touchend\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"story-item\", 3, \"click\", \"touchstart\", \"touchend\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [\"class\", \"story-ring\", 4, \"ngIf\"], [\"class\", \"story-gradient-ring\", 4, \"ngIf\"], [1, \"story-ring\"], [1, \"story-gradient-ring\"], [1, \"tab-menu-overlay\", 3, \"click\"], [1, \"sidebar-overlay\", 3, \"click\"], [1, \"instagram-tab-menu\"], [1, \"tab-menu-header\"], [\"name\", \"close-outline\", 1, \"close-icon\", 3, \"click\"], [1, \"tab-menu-grid\"], [1, \"tab-item\", 3, \"click\"], [1, \"tab-icon\", \"trending\"], [\"name\", \"trending-up\"], [1, \"tab-label\"], [1, \"tab-tooltip\"], [1, \"tab-icon\", \"brands\"], [\"name\", \"diamond\"], [1, \"tab-icon\", \"arrivals\"], [\"name\", \"sparkles\"], [1, \"tab-icon\", \"suggested\"], [\"name\", \"heart\"], [1, \"tab-icon\", \"influencers\"], [\"name\", \"people\"], [1, \"tab-icon\", \"categories\"], [\"name\", \"grid\"], [1, \"mobile-sidebar\"], [1, \"sidebar-header\"], [1, \"user-profile\"], [1, \"profile-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Profile\"], [1, \"profile-info\"], [1, \"sidebar-content\"], [1, \"sidebar-content-modal\"], [1, \"modal-header\"], [1, \"modal-content\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\"], [1, \"category-icon\"], [3, \"name\"], [1, \"instagram-bottom-nav\"], [1, \"nav-item\", \"active\"], [\"name\", \"home\"], [1, \"nav-item\"], [\"name\", \"search\"], [\"name\", \"add-circle-outline\"], [\"name\", \"play-circle-outline\"], [1, \"profile-avatar-nav\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 12, 1, \"div\", 1)(2, HomeComponent_div_2_Template, 10, 2, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n            i0.ɵɵtemplate(5, HomeComponent_app_view_add_stories_5_Template, 1, 0, \"app-view-add-stories\", 5);\n            i0.ɵɵelement(6, \"app-feed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(7, \"app-sidebar\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, HomeComponent_div_8_Template, 1, 2, \"div\", 7)(9, HomeComponent_div_9_Template, 1, 2, \"div\", 8)(10, HomeComponent_div_10_Template, 48, 2, \"div\", 9)(11, HomeComponent_div_11_Template, 13, 2, \"div\", 10)(12, HomeComponent_div_12_Template, 12, 9, \"div\", 11)(13, HomeComponent_div_13_Template, 12, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mobile-instagram\", ctx.isMobile)(\"sidebar-open\", ctx.isSidebarOpen)(\"mobile\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IonicModule, i2.IonIcon, ViewAddStoriesComponent, FeedComponent, SidebarComponent, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent],\n        styles: [\".home-container[_ngcontent-%COMP%]{padding:20px 0;min-height:calc(100vh - 60px);position:relative;background:#fff}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fff!important;color:#262626!important;padding:0!important;min-height:100vh!important}.mobile-header[_ngcontent-%COMP%]{display:none;position:fixed;top:0;left:0;right:0;height:60px;background:#fff;border-bottom:1px solid #dbdbdb;z-index:1001;padding:0 16px;align-items:center;justify-content:space-between}.mobile-header.instagram-style[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #dbdbdb}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%]{font-size:28px;font-weight:400;color:#262626;margin:0;font-family:Billabong,cursive,-apple-system,BlinkMacSystemFont,sans-serif;letter-spacing:.5px}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%]{font-size:16px;color:#262626;margin-top:2px}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{font-size:24px;color:#262626;cursor:pointer;transition:all .2s ease;padding:8px;border-radius:50%}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover{background-color:#0000000d}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]{position:relative;cursor:pointer}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%]{position:absolute;top:-2px;right:-2px;width:8px;height:8px;background:#ff3040;border-radius:50%;border:2px solid #ffffff}.content-grid[_ngcontent-%COMP%]{display:grid}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{display:none;position:fixed;top:60px;left:0;right:0;background:#fff;border-bottom:1px solid #dbdbdb;z-index:999;padding:12px 0;height:100px;box-shadow:0 2px 8px #00000014;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{background:#fffffff2}}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{display:flex;gap:12px;padding:0 16px;overflow-x:auto;overflow-y:hidden;scrollbar-width:none;-ms-overflow-style:none;height:100%;align-items:center;min-width:max-content;scroll-behavior:smooth;position:relative;z-index:998;-webkit-overflow-scrolling:touch;overscroll-behavior-x:contain;scroll-snap-type:x proximity;will-change:scroll-position;transform:translateZ(0)}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}@media (min-width: 320px) and (max-width: 768px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 12px;gap:10px;scroll-snap-type:x mandatory;touch-action:pan-x;-webkit-overflow-scrolling:touch;overscroll-behavior-x:contain;contain:layout style paint}}@media (min-width: 320px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 8px;gap:8px;scroll-padding-left:8px;scroll-padding-right:8px}}@media (min-width: 320px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 6px;gap:6px;scroll-padding-left:6px;scroll-padding-right:6px}}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:6px;min-width:70px;max-width:70px;cursor:pointer;flex-shrink:0;transition:transform .2s ease;scroll-snap-align:start;scroll-snap-stop:normal;position:relative}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active{transform:scale(.95)}@media (min-width: 320px) and (max-width: 768px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:65px;max-width:65px;gap:5px;padding:4px;margin:-4px}}@media (min-width: 320px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:60px;max-width:60px;gap:4px}}@media (min-width: 320px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:55px;max-width:55px;gap:3px}}@media (max-width: 320px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:50px;max-width:50px;gap:2px}}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{position:relative}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:20px;height:20px;background:#0095f6;border:2px solid #ffffff;border-radius:50%;display:flex;align-items:center;justify-content:center}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px;color:#fff}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;overflow:hidden;border:2px solid #dbdbdb;position:relative;flex-shrink:0;transition:all .2s ease}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]{border:2px solid transparent;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);padding:2px}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%;border:2px solid #ffffff}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed){animation:_ngcontent-%COMP%_storyPulse 2s infinite}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%]{border:2px solid #c7c7c7;background:#c7c7c7}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_storyTouchFeedback .2s ease}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%]{position:absolute;inset:-3px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-1;opacity:.8}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%]{position:absolute;inset:-5px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-2;opacity:.3;animation:_ngcontent-%COMP%_storyRingGradient 3s infinite;filter:blur(2px)}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;display:block;transition:transform .2s ease;position:relative;z-index:1}@media (min-width: 320px) and (max-width: 768px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:55px;height:55px}}@media (min-width: 320px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:50px;height:50px}}@media (min-width: 320px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:45px;height:45px}}@media (max-width: 320px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:40px;height:40px}}.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:11px;color:#262626;text-align:center;max-width:70px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:1.2;font-weight:400;margin-top:4px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility}@media (min-width: 320px) and (max-width: 768px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:10px;max-width:65px;font-weight:500}}@media (min-width: 320px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:9px;max-width:60px;line-height:1.1}}@media (min-width: 320px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:8px;max-width:55px;line-height:1.1}}@media (max-width: 320px){.content-grid[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:7px;max-width:50px;line-height:1;font-weight:600}}@keyframes _ngcontent-%COMP%_storyPulse{0%{transform:scale(1) translateZ(0);box-shadow:0 0 #f09433b3}70%{transform:scale(1.05) translateZ(0);box-shadow:0 0 0 10px #f0943300}to{transform:scale(1) translateZ(0);box-shadow:0 0 #f0943300}}@keyframes _ngcontent-%COMP%_storyRingGradient{0%{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}25%{background:linear-gradient(90deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}50%{background:linear-gradient(135deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}75%{background:linear-gradient(180deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}to{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}}@keyframes _ngcontent-%COMP%_storyTouchFeedback{0%{transform:scale(1) translateZ(0)}50%{transform:scale(.95) translateZ(0)}to{transform:scale(1) translateZ(0)}}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]{display:none;position:fixed;bottom:0;left:0;right:0;background:#fff;border-top:1px solid #dbdbdb;justify-content:space-around;align-items:center;padding:8px 0;z-index:1000;height:60px;box-shadow:0 -1px 3px #0000001a;padding-bottom:max(8px,env(safe-area-inset-bottom))}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;padding:4px 8px;border-radius:8px;transition:all .2s ease;min-width:44px;min-height:44px;position:relative}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover{background-color:#0000000d}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#262626;transform:scale(1.1)}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;transition:all .2s ease}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;overflow:hidden;border:1px solid #8e8e8e}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%]{border:2px solid #262626}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.content-grid[_ngcontent-%COMP%]   .instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%]{position:absolute;top:2px;right:2px;background:#ff3040;color:#fff;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;min-width:16px;text-align:center;line-height:1.2}.content-grid[_ngcontent-%COMP%]   .tab-menu-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#000c;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.content-grid[_ngcontent-%COMP%]   .tab-menu-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;background:#000;border-top-left-radius:20px;border-top-right-radius:20px;z-index:1600;transform:translateY(100%);transition:transform .3s ease;max-height:70vh;overflow-y:auto}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu.active[_ngcontent-%COMP%]{transform:translateY(0)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px 24px 16px;border-bottom:1px solid #262626}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin:0}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:24px;padding:24px}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px;cursor:pointer;padding:16px;border-radius:16px;transition:all .2s ease;position:relative}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover{background-color:#ffffff0d;transform:scale(1.05)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ff8e53)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4ecdc4,#44a08d)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]{background:linear-gradient(135deg,#a8edea,#fed6e3)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9a9e,#fecfef)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#fff;text-align:center}.content-grid[_ngcontent-%COMP%]   .instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;text-align:center;line-height:1.3}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]{position:fixed;inset:0;background:#000;z-index:1700;transform:translate(100%);transition:transform .3s ease;overflow-y:auto}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal.active[_ngcontent-%COMP%]{transform:translate(0)}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]{position:sticky;top:0;background:#000;display:flex;justify-content:space-between;align-items:center;padding:20px 24px;border-bottom:1px solid #262626;z-index:10}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#fff;margin:0}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{padding:0}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]{background:#000;color:#fff;min-height:calc(100vh - 80px)}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     *{background-color:transparent!important;color:#fff!important}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item{background:#1a1a1a!important;border:1px solid #262626!important}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark{color:#fff!important}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white{background:#1a1a1a!important}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;padding:24px}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px;padding:20px;background:#1a1a1a;border-radius:16px;border:1px solid #262626;cursor:pointer;transition:all .2s ease}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover{background:#262626;transform:scale(1.02)}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.content-grid[_ngcontent-%COMP%]   .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#fff;text-align:center}.content-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 400px;gap:40px;max-width:1000px;margin:0 auto;background:#fff;padding:0 20px}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.desktop-sidebar[_ngcontent-%COMP%]{display:block}.sidebar-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#000000a6;z-index:200;opacity:0;visibility:hidden;transition:all .3s cubic-bezier(.25,.46,.45,.94)}.sidebar-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.mobile-sidebar[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:85%;max-width:400px;height:100%;background:#fff;z-index:300;transition:right .3s cubic-bezier(.25,.46,.45,.94);box-shadow:-2px 0 10px #0000001a;display:flex;flex-direction:column}.mobile-sidebar.active[_ngcontent-%COMP%]{right:0}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:20px 16px;border-bottom:1px solid #dbdbdb;background:#fafafa}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;overflow:hidden;border:2px solid #dbdbdb}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:600;color:#262626}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:#8e8e8e}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#262626;cursor:pointer;padding:8px;margin:-8px;transition:color .2s ease}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{color:#8e8e8e}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:16px 0}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:2px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}@media (max-width: 1024px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;max-width:100%;padding:0 16px}}@media (max-width: 1024px) and (min-width: 768px){.content-grid[_ngcontent-%COMP%]{max-width:768px;margin:0 auto;padding:0 24px}}@media (max-width: 1024px){.desktop-sidebar[_ngcontent-%COMP%]{display:none}.mobile-header[_ngcontent-%COMP%]{display:flex}.home-container[_ngcontent-%COMP%]{padding-top:80px}}@media (max-width: 1024px) and (min-width: 768px){.home-container[_ngcontent-%COMP%]{padding:80px 0 0}}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fff!important;min-height:100vh!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%]{display:flex!important;visibility:visible!important;opacity:1!important;width:100%!important;height:60px!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important;box-shadow:0 1px 3px #0000001a!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{display:block!important;visibility:visible!important;opacity:1!important;width:100%!important;height:100px!important;padding:8px 0!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important;transform:translateZ(0)!important;will-change:scroll-position!important;contain:layout style paint!important;backdrop-filter:blur(10px)!important;-webkit-backdrop-filter:blur(10px)!important}@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))){.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{background:#fffffff2!important}}.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:160px 0 60px!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}@media (min-width: 768px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{padding:160px 16px 60px!important;max-width:768px!important;margin:0 auto!important;gap:16px!important}}@media (min-width: 1024px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 300px!important;padding:160px 24px 60px!important;max-width:1200px!important;gap:32px!important}}@media (min-width: 1200px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 400px!important;padding:160px 32px 60px!important;max-width:1400px!important;gap:40px!important}}@media (min-width: 1440px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{padding:160px 48px 60px!important;max-width:1600px!important;gap:48px!important}}.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%]{background:#fff!important;border-top:1px solid #dbdbdb!important;box-shadow:0 -1px 3px #0000001a!important}@media (min-width: 320px) and (max-width: 768px){.home-container[_ngcontent-%COMP%]{background:#fafafa!important;padding:0!important}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fafafa!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:5px 0!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}.content-grid[_ngcontent-%COMP%]{padding:0;margin:0;background:#fafafa}.content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{background:#fff;border-radius:0;box-shadow:none;margin:0;padding:0}.mobile-header[_ngcontent-%COMP%]{display:flex!important;visibility:visible!important;opacity:1!important;width:100%!important;height:60px!important}.instagram-stories-section[_ngcontent-%COMP%]{display:block!important;visibility:visible!important;opacity:1!important;width:100%!important;height:100px!important;padding:8px 0!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important}.instagram-bottom-nav[_ngcontent-%COMP%]{display:flex!important}.desktop-sidebar[_ngcontent-%COMP%]{display:none!important}.home-container[_ngcontent-%COMP%]{background:#fff!important;min-height:100vh;padding:0!important}.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:160px 0 60px!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important}.main-content[_ngcontent-%COMP%]{background:#fff!important;color:#262626!important;gap:0;padding:0;width:100%!important;max-width:100%!important}.mobile-sidebar[_ngcontent-%COMP%]{width:90%;background:#fff;color:#262626}.sidebar-overlay[_ngcontent-%COMP%]{background:#000c}}@media (max-width: 480px){.mobile-sidebar[_ngcontent-%COMP%]{width:95%}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{gap:12px}}@media (max-width: 480px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 60px!important;min-height:calc(100vh - 220px)!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}}@media (max-width: 480px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 60px!important;min-height:calc(100vh - 220px)!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}.main-content[_ngcontent-%COMP%]{width:100%!important;max-width:100%!important;overflow-x:hidden!important}}@media (max-width: 480px) and (max-width: 360px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 60px!important;min-height:calc(100vh - 220px)!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}}@media (min-width: 769px){.mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%]{display:none!important}.desktop-sidebar[_ngcontent-%COMP%]{display:block}.mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%]{display:none!important}.home-container[_ngcontent-%COMP%]{background:#fff;padding:20px}.content-grid[_ngcontent-%COMP%]{background:#fff;color:#262626;padding:0;margin:0 auto;grid-template-columns:1fr 300px;gap:32px;max-width:1000px}}@media (min-width: 769px) and (min-width: 1024px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 350px;gap:36px;max-width:1200px}}@media (min-width: 769px) and (min-width: 1200px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 400px;gap:40px;max-width:1400px}}@media (min-width: 769px) and (min-width: 1440px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 450px;gap:48px;max-width:1600px}}@media (min-width: 769px){.main-content[_ngcontent-%COMP%]{background:#fff;color:#262626}.instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%]{display:none!important}}@media (min-width: 320px) and (max-width: 768px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:5px 0!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}