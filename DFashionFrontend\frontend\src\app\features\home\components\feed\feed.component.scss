// Instagram-style Feed
.instagram-feed {
  max-width: 470px;
  margin: 0 auto;
  background: #fafafa;
  min-height: 100vh;

  // Mobile responsive
  @media (max-width: 768px) {
    max-width: 100%;
    background: #ffffff;
    padding: 0;
    margin: 0;
  }
}

// Loading Skeletons
.loading-container {
  padding: 0;
}

// No posts message
.no-posts-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: white;
  border-radius: 8px;
  margin: 20px 0;

  p {
    margin: 0;
    font-size: 16px;
  }
}

.post-skeleton {
  background: white;
  margin-bottom: 12px;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  overflow: hidden;
}

.skeleton-header {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.skeleton-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-user-info {
  flex: 1;
}

.skeleton-username {
  width: 100px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 6px;
}

.skeleton-time {
  width: 60px;
  height: 10px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-image {
  width: 100%;
  height: 400px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-actions {
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

// Instagram Posts
.feed-posts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.instagram-post {
  background: white;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  overflow: visible; // Changed to visible to prevent button cutoff

  // Ensure content is fully visible in responsive design
  @media (max-width: 768px) {
    overflow: visible;
    min-height: auto;
  }
}

// Post Header
.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-container {
  position: relative;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0;
  line-height: 1.2;
}

.post-location {
  font-size: 12px;
  color: #8e8e8e;
  line-height: 1.2;
}

.post-options {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #262626;

  &:hover {
    color: #8e8e8e;
  }
}

// Post Media
.post-media-container {
  position: relative;
  width: 100%;
  max-height: 600px;
  overflow: hidden;
}

.post-image-container,
.post-video-container {
  width: 100%;
  position: relative;
}

.post-image {
  width: 100%;
  height: auto;
  display: block;
}

.post-video {
  width: 100%;
  height: auto;
  display: block;
  max-height: 600px;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;

  .post-video-container:hover & {
    opacity: 1;
  }
}

.play-pause-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reel-indicator {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

// Product Tags Overlay
.product-tags-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.product-tag-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid white;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    background: rgba(0, 0, 0, 0.9);
  }
}

// Post Actions
.post-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
}

.primary-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  font-size: 24px;
  color: #262626;
  transition: all 0.2s ease;

  &:hover {
    color: #8e8e8e;
  }

  &.liked {
    color: #ed4956;
    animation: likeAnimation 0.3s ease;
  }

  &.saved {
    color: #262626;
  }
}

@keyframes likeAnimation {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

// Likes Section
.likes-section {
  padding: 0 16px 8px;
}

.likes-count {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

// Post Caption
.post-caption {
  padding: 0 16px 8px;
  font-size: 14px;
  line-height: 1.4;

  .username {
    font-weight: 600;
    color: #262626;
    margin-right: 8px;
  }

  .caption-text {
    color: #262626;
  }
}

.hashtags {
  margin-top: 8px;
}

.hashtag {
  color: #00376b;
  margin-right: 8px;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

// Comments
.comments-preview {
  padding: 0 16px 8px;
}

.view-comments-btn {
  background: none;
  border: none;
  color: #8e8e8e;
  font-size: 14px;
  cursor: pointer;
  padding: 0;

  &:hover {
    color: #262626;
  }
}

.post-time {
  padding: 0 16px 8px;
  font-size: 10px;
  color: #8e8e8e;
  text-transform: uppercase;
  letter-spacing: 0.2px;
}

// Add Comment
.add-comment-section {
  display: flex;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #efefef;
  gap: 12px;
}

.comment-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #262626;

  &::placeholder {
    color: #8e8e8e;
  }
}

.post-comment-btn {
  background: none;
  border: none;
  color: #0095f6;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;

  &:disabled {
    color: #b2dffc;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    color: #00376b;
  }
}

// E-commerce Section - Enhanced for Responsive Design
.ecommerce-section {
  border-top: 1px solid #efefef;
  padding: 16px;
  background: #fafafa;
  margin-top: 8px;

  // Ensure full visibility in responsive design
  position: relative;
  z-index: 1;
  overflow: visible;

  @media (max-width: 768px) {
    padding: 12px 16px 16px 16px; // Extra bottom padding
    background: #ffffff;
    border-top: 1px solid #efefef;
    margin-top: 0;
  }
}

.product-showcase {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (max-width: 768px) {
    gap: 8px;
  }

  @media (max-width: 425px) {
    gap: 6px;
  }

  @media (max-width: 320px) {
    gap: 4px;
  }
}

.featured-product {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #efefef;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: white;

  // Ensure buttons are visible
  overflow: visible;
  position: relative;

  &:hover {
    background: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: 10px;
    gap: 10px;
    border-radius: 6px;
  }

  // Switch to column layout for mobile (425px and below)
  @media (max-width: 425px) {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

// Product Header (Image + Title inline) - Only for mobile
.product-header {
  // Hidden by default (web uses original layout)
  display: none;

  // Show only on mobile (425px and below)
  @media (max-width: 425px) {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding-bottom: 5px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }

  @media (max-width: 375px) {
    gap: 6px;
    padding-bottom: 4px;
  }

  @media (max-width: 320px) {
    gap: 5px;
    padding-bottom: 3px;
  }
}

.product-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;

  // Hide web thumbnail on mobile (425px and below)
  @media (max-width: 425px) {
    display: none;
  }
}

// Mobile thumbnail (inside product-header)
.product-header .product-thumbnail {
  width: 45px;
  height: 45px;
  border-radius: 6px;
  display: block; // Override the hide rule

  @media (max-width: 375px) {
    width: 40px;
    height: 40px;
  }

  @media (max-width: 320px) {
    width: 35px;
    height: 35px;
  }
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0; // Allow text truncation

  // Hide web product-details on mobile (425px and below)
  @media (max-width: 425px) {
    display: none;
  }
}

// Mobile product-details (inside product-header)
.product-header .product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; // Allow text truncation
}

.product-name {
  font-size: 12px;
  font-weight: 600;
  color: #262626;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // Mobile sizes (when in product-header)
  @media (max-width: 425px) {
    font-size: 13px;
  }

  @media (max-width: 375px) {
    font-size: 12px;
  }

  @media (max-width: 320px) {
    font-size: 11px;
  }
}

.product-price {
  font-size: 12px;
  color: #8e8e8e;
  font-weight: 500;

  // Mobile sizes (when in product-header)
  @media (max-width: 425px) {
    font-size: 12px;
  }

  @media (max-width: 375px) {
    font-size: 11px;
  }

  @media (max-width: 320px) {
    font-size: 10px;
  }
}

.product-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 8px;
  padding: 8px 0;

  // Ensure buttons are fully visible
  overflow: visible;
  position: relative;
  z-index: 2;

  // Prevent buttons from being cut off
  min-height: 44px; // Ensure minimum touch target height

  @media (max-width: 768px) {
    gap: 6px;
    padding: 8px 0 12px 0; // Extra bottom padding
    flex-wrap: wrap; // Allow wrapping if needed
    justify-content: flex-start;
  }

  @media (max-width: 480px) {
    gap: 4px;
    padding: 6px 0 10px 0;
  }

  // Mobile layout (425px and below) - buttons below image/title
  @media (max-width: 425px) {
    width: 100%;
    justify-content: flex-start;
    gap: 6px;
    margin-top: 5px;
    padding: 5px 0 0 0;
    min-height: 36px;
  }

  @media (max-width: 375px) {
    gap: 5px;
    margin-top: 4px;
    padding: 4px 0 0 0;
    min-height: 32px;
  }

  @media (max-width: 320px) {
    gap: 4px;
    margin-top: 3px;
    padding: 3px 0 0 0;
    min-height: 28px;
  }
}

.shop-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 18px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 80px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: 425px) {
    min-width: 70px;
    height: 32px;
    font-size: 11px;
    padding: 6px 12px;
  }

  @media (max-width: 375px) {
    min-width: 65px;
    height: 30px;
    font-size: 10px;
    padding: 5px 10px;
  }

  @media (max-width: 320px) {
    min-width: 60px;
    height: 28px;
    font-size: 9px;
    padding: 4px 8px;
  }
}

.cart-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: none;
  padding: 0;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  display: flex !important;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  visibility: visible !important;
  opacity: 1 !important;
  flex-shrink: 0;

  i {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);

    i {
      transform: scale(1.1);
    }

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(1);
  }

  @media (max-width: 425px) {
    width: 32px;
    height: 32px;

    i {
      font-size: 12px;
    }
  }

  @media (max-width: 375px) {
    width: 30px;
    height: 30px;

    i {
      font-size: 11px;
    }
  }

  @media (max-width: 320px) {
    width: 28px;
    height: 28px;

    i {
      font-size: 10px;
    }
  }
}

.wishlist-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #e91e63;
  border: none;
  padding: 0;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
  display: flex !important;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  visibility: visible !important;
  opacity: 1 !important;
  flex-shrink: 0;

  i {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #fecfef 0%, #ff9a9e 100%);
    color: #c2185b;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);

    i {
      transform: scale(1.1);
    }

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(1);
  }

  &.active {
    background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #c2185b 0%, #e91e63 100%);
      color: white;
    }
  }

  @media (max-width: 425px) {
    width: 32px;
    height: 32px;

    i {
      font-size: 12px;
    }
  }

  @media (max-width: 375px) {
    width: 30px;
    height: 30px;

    i {
      font-size: 11px;
    }
  }

  @media (max-width: 320px) {
    width: 28px;
    height: 28px;

    i {
      font-size: 10px;
    }
  }
}

.buy-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  padding: 0;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
  display: flex !important;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  visibility: visible !important;
  opacity: 1 !important;
  flex-shrink: 0;

  i {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);

    i {
      transform: scale(1.1);
    }

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(1);
  }

  @media (max-width: 425px) {
    width: 32px;
    height: 32px;

    i {
      font-size: 12px;
    }
  }

  @media (max-width: 375px) {
    width: 30px;
    height: 30px;

    i {
      font-size: 11px;
    }
  }

  @media (max-width: 320px) {
    width: 28px;
    height: 28px;

    i {
      font-size: 10px;
    }
  }
}

// Enhanced Responsive Design for Product Actions
@media (max-width: 768px) {
  .ecommerce-section {
    padding: 12px 16px 20px 16px; // Increased bottom padding
    background: #ffffff;
    border-top: 1px solid #efefef;
    margin-bottom: 8px; // Add margin to prevent cutoff
  }

  .product-actions {
    gap: 8px; // Increased gap for better spacing
    padding: 10px 0 15px 0; // More padding to prevent cutoff
    overflow: visible;
    position: relative;
  }

  .shop-btn {
    padding: 8px 14px; // Larger touch target
    font-size: 12px; // Slightly larger text
    border-radius: 18px;
    min-width: 60px; // Ensure minimum width
  }

  .cart-btn,
  .wishlist-btn,
  .buy-btn {
    width: 36px; // Larger touch targets
    height: 36px;
    padding: 8px;

    i {
      font-size: 14px; // Larger icons
    }
  }
}

// Small Mobile Screens (480px and below)
@media (max-width: 480px) {
  .ecommerce-section {
    padding: 12px 8px 25px 8px; // Increased bottom padding significantly
    margin-bottom: 15px; // More margin to prevent cutoff
    background: #f8f9fa; // Make it more visible
    border-top: 2px solid #dee2e6;
  }

  .featured-product {
    flex-direction: column; // Stack vertically for more space
    align-items: flex-start;
    padding: 12px 8px;
    gap: 8px;
  }

  .product-details {
    width: 100%;
    margin-bottom: 8px;
  }

  .product-actions {
    width: 100%; // Full width for buttons
    gap: 8px;
    flex-wrap: wrap;
    padding: 10px 0 15px 0;
    justify-content: space-between; // Better distribution
  }

  .shop-btn {
    flex: 1; // Take available space
    padding: 8px 12px;
    font-size: 11px;
    border-radius: 16px;
    min-width: 70px;
    max-width: 120px;
  }

  .cart-btn,
  .wishlist-btn,
  .buy-btn {
    width: 36px; // Larger touch targets
    height: 36px;
    padding: 8px;
    flex-shrink: 0; // Don't shrink

    i {
      font-size: 12px;
    }
  }
}

// Enhanced Mobile Screens (425px to 375px) - Improved E-commerce Experience
@media (max-width: 425px) {
  .instagram-post {
    margin-bottom: 12px !important;
    border-radius: 0;
    overflow: visible !important;
    position: relative;
  }

  .ecommerce-section {
    padding: 16px 10px 35px 10px !important;
    margin-bottom: 20px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 2px solid #dee2e6 !important;
    border-radius: 12px !important;
    position: relative !important;
    z-index: 100 !important;
    min-height: 140px !important;
    overflow: visible !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1) !important;
  }

  .product-showcase {
    gap: 12px !important;
    overflow: visible !important;
  }

  .featured-product {
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
    padding: 12px !important;
    background: white !important;
    border: 1px solid #e9ecef !important;
    border-radius: 10px !important;
    margin-bottom: 10px !important;
    overflow: visible !important;
    position: relative !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
    transition: all 0.3s ease !important;
    gap: 8px !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 15px rgba(0,0,0,0.12) !important;
    }
  }

  // Override for web thumbnail (hide on mobile)
  .featured-product > .product-thumbnail {
    display: none !important;
  }

  // Override for web product-details (hide on mobile)
  .featured-product > .product-details {
    display: none !important;
  }

  // Product header styles (mobile layout)
  .product-header {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;
    padding-bottom: 5px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    margin-bottom: 0 !important;

    .product-thumbnail {
      width: 45px !important;
      height: 45px !important;
      border-radius: 6px !important;
      border: 1px solid #e9ecef !important;
      object-fit: cover !important;
      flex-shrink: 0 !important;
      display: block !important;
    }

    .product-details {
      flex: 1 !important;
      display: flex !important;
      flex-direction: column !important;
      gap: 4px !important;
      min-width: 0 !important;
    }

    .product-name {
      font-size: 13px !important;
      font-weight: 600 !important;
      margin-bottom: 4px !important;
      color: #212529 !important;
      display: block !important;
      line-height: 1.3 !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .product-price {
      font-size: 12px !important;
      color: #6c757d !important;
      font-weight: 500 !important;
      display: block !important;
    }
  }

  .product-actions {
    display: flex !important;
    flex-direction: row !important;
    gap: 6px !important;
    padding: 5px 0 0 0 !important;
    align-items: center !important;
    justify-content: flex-start !important;
    overflow: visible !important;
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
    margin-top: 5px !important;
  }

  .shop-btn {
    width: 70px !important;
    padding: 6px 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    border-radius: 16px !important;
    min-height: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 0 !important;
    margin-right: 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
  }

  .cart-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3) !important;

    i {
      font-size: 12px !important;
      font-weight: bold !important;
    }
  }

  .wishlist-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    color: #e91e63 !important;
    box-shadow: 0 2px 6px rgba(255, 154, 158, 0.3) !important;

    i {
      font-size: 12px !important;
      font-weight: bold !important;
    }
  }

  .buy-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(79, 172, 254, 0.3) !important;

    i {
      font-size: 12px !important;
      font-weight: bold !important;
    }
  }

  .cart-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3) !important;
  }

  .wishlist-btn {
    background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(255, 159, 243, 0.3) !important;
  }

  .buy-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(79, 172, 254, 0.3) !important;
  }

  .cart-btn i,
  .wishlist-btn i,
  .buy-btn i {
    font-size: 14px !important;
    font-weight: bold !important;
  }

  // Enhanced hover effects
  .shop-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5) !important;
  }

  .cart-btn:hover,
  .wishlist-btn:hover,
  .buy-btn:hover {
    transform: translateY(-1px) scale(1.05) !important;
  }
}

// Small Mobile Screens (375px to 350px) - Optimized Layout
@media (max-width: 375px) {
  .ecommerce-section {
    padding: 14px 8px 30px 8px !important;
    margin-bottom: 18px !important;
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%) !important;
    border: 1px solid #dadce0 !important;
    border-radius: 10px !important;
    min-height: 130px !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08) !important;
  }

  .featured-product {
    padding: 10px !important;
    margin-bottom: 8px !important;
    border-radius: 8px !important;
  }

  .product-thumbnail {
    width: 50px !important;
    height: 50px !important;
    margin-right: 10px !important;
    border-radius: 6px !important;
  }

  .product-name {
    font-size: 12px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
  }

  .product-price {
    font-size: 11px !important;
    font-weight: 500 !important;
  }

  .product-actions {
    min-width: 70px !important;
    gap: 4px !important;
  }

  .shop-btn {
    width: 65px !important;
    padding: 6px 8px !important;
    font-size: 10px !important;
    min-height: 28px !important;
    border-radius: 14px !important;
  }

  .cart-btn,
  .wishlist-btn,
  .buy-btn {
    width: 28px !important;
    height: 28px !important;
    padding: 4px !important;

    i {
      font-size: 12px !important;
    }
  }
}

// Ultra Small Screens (350px to 320px) - Maximum Compatibility
@media (max-width: 350px) {
  .ecommerce-section {
    padding: 12px 6px 25px 6px !important;
    margin-bottom: 15px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid #ced4da !important;
    border-radius: 8px !important;
    min-height: 120px !important;
    box-shadow: 0 3px 12px rgba(0,0,0,0.06) !important;
  }

  .featured-product {
    padding: 8px !important;
    margin-bottom: 6px !important;
    border-radius: 6px !important;
    gap: 8px !important;
  }

  .product-thumbnail {
    width: 45px !important;
    height: 45px !important;
    margin-right: 8px !important;
    border-radius: 5px !important;
  }

  .product-details {
    margin-right: 6px !important;
  }

  .product-name {
    font-size: 11px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    margin-bottom: 2px !important;
  }

  .product-price {
    font-size: 10px !important;
    font-weight: 500 !important;
  }

  .product-actions {
    min-width: 60px !important;
    gap: 3px !important;
  }

  .shop-btn {
    width: 55px !important;
    padding: 5px 6px !important;
    font-size: 9px !important;
    min-height: 24px !important;
    border-radius: 12px !important;
    margin-bottom: 2px !important;
  }

  .cart-btn,
  .wishlist-btn,
  .buy-btn {
    width: 24px !important;
    height: 24px !important;
    padding: 3px !important;
    margin: 0.5px !important;

    i {
      font-size: 10px !important;
    }
  }
}

// iPhone SE and similar (320px) - Absolute Minimum
@media (max-width: 320px) {
  .ecommerce-section {
    padding: 10px 4px 20px 4px !important;
    margin-bottom: 12px !important;
    background: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
    min-height: 110px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05) !important;
  }

  .featured-product {
    padding: 6px !important;
    margin-bottom: 4px !important;
    border-radius: 4px !important;
    gap: 6px !important;
  }

  .product-thumbnail {
    width: 40px !important;
    height: 40px !important;
    margin-right: 6px !important;
    border-radius: 4px !important;
  }

  .product-details {
    margin-right: 4px !important;
  }

  .product-name {
    font-size: 10px !important;
    font-weight: 600 !important;
    line-height: 1.1 !important;
    margin-bottom: 1px !important;
  }

  .product-price {
    font-size: 9px !important;
    font-weight: 500 !important;
  }

  .product-actions {
    min-width: 50px !important;
    gap: 2px !important;
  }

  .shop-btn {
    width: 45px !important;
    padding: 4px 5px !important;
    font-size: 8px !important;
    min-height: 20px !important;
    border-radius: 10px !important;
    margin-bottom: 1px !important;
  }

  .cart-btn,
  .wishlist-btn,
  .buy-btn {
    width: 20px !important;
    height: 20px !important;
    padding: 2px !important;
    margin: 0.5px !important;

    i {
      font-size: 8px !important;
    }
  }
}

// Load More
.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  background: #0095f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;

  &:hover {
    background: #00376b;
  }
}

// Empty State
.empty-feed {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  margin: 20px;
}

.empty-content {
  i {
    font-size: 48px;
    color: #dbdbdb;
    margin-bottom: 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
  }

  p {
    font-size: 14px;
    color: #8e8e8e;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .instagram-feed {
    max-width: 100%;
  }

  .instagram-post {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
  }

  .feed-posts {
    gap: 0;
  }
}

@media (max-width: 480px) {
  .post-header {
    padding: 12px;
  }

  .post-actions {
    padding: 6px 12px;
  }

  .likes-section,
  .post-caption,
  .comments-preview,
  .post-time {
    padding-left: 12px;
    padding-right: 12px;
  }

  .add-comment-section {
    padding: 12px;
  }

  .ecommerce-section {
    padding: 12px 16px 20px 16px; // Increased bottom padding
    background: #f8f9fa; // Subtle background to make it visible
    border-top: 2px solid #e9ecef; // More prominent border
    margin-bottom: 8px;

    // Ensure it's always visible
    position: relative;
    z-index: 1;
    overflow: visible;
  }
}
