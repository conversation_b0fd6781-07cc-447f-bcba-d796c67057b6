{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nfunction TopFashionInfluencersComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 19);\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 23);\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵelement(3, \"div\", 30)(4, \"div\", 31)(5, \"div\", 32);\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵelement(7, \"div\", 34)(8, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, TopFashionInfluencersComponent_div_9_div_2_Template, 9, 0, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"ion-icon\", 36);\n    i0.ɵɵelementStart(2, \"p\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 39);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"ion-icon\", 41);\n    i0.ɵɵelementStart(2, \"h3\", 42);\n    i0.ɵɵtext(3, \"No Top Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 43);\n    i0.ɵɵtext(5, \"Top fashion influencers will appear here when available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"ion-icon\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_div_7_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", brand_r7, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_div_7_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const influencer_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", influencer_r6.topBrands.length - 2, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_12_div_7_Template_div_click_0_listener() {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(influencer_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 53);\n    i0.ɵɵelement(2, \"img\", 54);\n    i0.ɵɵtemplate(3, TopFashionInfluencersComponent_div_12_div_7_div_3_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"h3\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 59);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 60)(12, \"div\", 61)(13, \"span\", 62);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 63);\n    i0.ɵɵtext(16, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 61)(18, \"span\", 62);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 63);\n    i0.ɵɵtext(21, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 64)(23, \"span\", 65);\n    i0.ɵɵtext(24, \"Works with:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 66);\n    i0.ɵɵtemplate(26, TopFashionInfluencersComponent_div_12_div_7_span_26_Template, 2, 1, \"span\", 67)(27, TopFashionInfluencersComponent_div_12_div_7_span_27_Template, 2, 1, \"span\", 68);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_12_div_7_Template_button_click_28_listener($event) {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowInfluencer(influencer_r6, $event));\n    });\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"ion-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const influencer_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.isVerified);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", influencer_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.category);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFollowerCount(influencer_r6.followerCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r6.engagementRate, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", influencer_r6.topBrands.slice(0, 2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.topBrands.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", influencer_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", influencer_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 49);\n    i0.ɵɵlistener(\"mouseenter\", function TopFashionInfluencersComponent_div_12_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TopFashionInfluencersComponent_div_12_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵtemplate(7, TopFashionInfluencersComponent_div_12_div_7_Template, 32, 14, \"div\", 51);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.topInfluencers)(\"ngForTrackBy\", ctx_r1.trackByInfluencerId);\n  }\n}\nfunction TopFashionInfluencersComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵelement(1, \"ion-icon\", 41);\n    i0.ɵɵelementStart(2, \"h3\", 42);\n    i0.ɵɵtext(3, \"No Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 43);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TopFashionInfluencersComponent {\n  constructor(router) {\n    this.router = router;\n    this.topInfluencers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 240; // Width of each influencer card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 6000; // 6 seconds for influencers\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 512;\n    this.sectionComments = 234;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadTopInfluencers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadTopInfluencers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for top fashion influencers\n        _this.topInfluencers = [{\n          id: '1',\n          username: 'fashionista_queen',\n          fullName: 'Priya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followerCount: 2500000,\n          category: 'High Fashion',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 8.5,\n          recentPosts: 24,\n          topBrands: ['Gucci', 'Prada', 'Versace']\n        }, {\n          id: '2',\n          username: 'street_style_king',\n          fullName: 'Arjun Kapoor',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1800000,\n          category: 'Streetwear',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 12.3,\n          recentPosts: 18,\n          topBrands: ['Nike', 'Adidas', 'Supreme']\n        }, {\n          id: '3',\n          username: 'boho_goddess',\n          fullName: 'Ananya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1200000,\n          category: 'Boho Chic',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 9.7,\n          recentPosts: 32,\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\n        }, {\n          id: '4',\n          username: 'luxury_lifestyle',\n          fullName: 'Kavya Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followerCount: 3200000,\n          category: 'Luxury',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 6.8,\n          recentPosts: 15,\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n        }, {\n          id: '5',\n          username: 'minimalist_maven',\n          fullName: 'Ravi Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followerCount: 950000,\n          category: 'Minimalist',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 11.2,\n          recentPosts: 21,\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\n        }, {\n          id: '6',\n          username: 'vintage_vibes',\n          fullName: 'Meera Patel',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followerCount: 780000,\n          category: 'Vintage',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 13.5,\n          recentPosts: 28,\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnInfluencersLoad();\n      } catch (error) {\n        console.error('Error loading top influencers:', error);\n        _this.error = 'Failed to load top influencers';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onInfluencerClick(influencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n  onFollowInfluencer(influencer, event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    if (influencer.isFollowing) {\n      influencer.followerCount++;\n    } else {\n      influencer.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadTopInfluencers();\n  }\n  trackByInfluencerId(index, influencer) {\n    return influencer.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 200;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 220;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 240;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 260;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when influencers load\n  updateSliderOnInfluencersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for top fashion influencers section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Top Fashion Influencers',\n        text: 'Follow the top fashion trendsetters!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for top fashion influencers');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function TopFashionInfluencersComponent_Factory(t) {\n      return new (t || TopFashionInfluencersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TopFashionInfluencersComponent,\n      selectors: [[\"app-top-fashion-influencers\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 6,\n      consts: [[1, \"top-influencers-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"star\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"influencers-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-influencer-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-influencer-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"loading-stats\"], [1, \"loading-stat\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"empty-state\"], [\"name\", \"star-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"influencers-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"influencers-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"influencers-slider\"], [\"class\", \"influencer-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"influencer-card\", 3, \"click\"], [1, \"influencer-avatar-container\"], [\"loading\", \"lazy\", 1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"username\"], [1, \"category\"], [1, \"stats-container\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"top-brands\"], [1, \"brands-label\"], [1, \"brands-list\"], [\"class\", \"brand-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-brands\", 4, \"ngIf\"], [1, \"follow-btn\", 3, \"click\"], [1, \"verified-badge\"], [\"name\", \"checkmark\"], [1, \"brand-tag\"], [1, \"more-brands\"], [1, \"empty-container\"]],\n      template: function TopFashionInfluencersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TopFashionInfluencersComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Top Fashion Influencers \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Follow the trendsetters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, TopFashionInfluencersComponent_div_9_Template, 3, 2, \"div\", 7)(10, TopFashionInfluencersComponent_div_10_Template, 7, 1, \"div\", 8)(11, TopFashionInfluencersComponent_div_11_Template, 6, 0, \"div\", 9)(12, TopFashionInfluencersComponent_div_12_Template, 8, 6, \"div\", 10)(13, TopFashionInfluencersComponent_div_13_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon],\n      styles: [\".top-influencers-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 215, 0, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: #ffd700;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 215, 0, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 48, 64, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #ffd700 100%);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]   .loading-stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.influencers-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.influencers-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.influencers-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  width: 220px;\\n}\\n\\n.influencer-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.influencer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.influencer-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 20px;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 4px solid #ffd700;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 55px);\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  border: 3px solid white;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ffd700;\\n  background: rgba(255, 215, 0, 0.1);\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 16px 0;\\n  font-weight: 600;\\n}\\n\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 2px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.top-brands[_ngcontent-%COMP%]   .brands-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  display: block;\\n  margin-bottom: 6px;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n  justify-content: center;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .brand-tag[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  background: rgba(108, 92, 231, 0.1);\\n  color: #6c5ce7;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .more-brands[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #999;\\n  font-weight: 500;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: #1a1a1a;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 200px;\\n    width: 200px;\\n    padding: 20px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(33.333% - 14px);\\n    width: calc(33.333% - 14px);\\n    max-width: 250px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(25% - 15px);\\n    width: calc(25% - 15px);\\n    max-width: 230px;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(20% - 16px);\\n    width: calc(20% - 16px);\\n    max-width: 220px;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(16.666% - 17px);\\n    width: calc(16.666% - 17px);\\n    max-width: 210px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .influencers-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .influencers-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 180px;\\n    width: 180px;\\n    padding: 16px;\\n  }\\n  .influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TopFashionInfluencersComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TopFashionInfluencersComponent_div_1_Template_button_click_5_listener", "openComments", "TopFashionInfluencersComponent_div_1_Template_button_click_9_listener", "shareSection", "TopFashionInfluencersComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "TopFashionInfluencersComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "TopFashionInfluencersComponent_div_9_div_2_Template", "ɵɵpureFunction0", "_c0", "TopFashionInfluencersComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "brand_r7", "influencer_r6", "topBrands", "length", "TopFashionInfluencersComponent_div_12_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onInfluencerClick", "TopFashionInfluencersComponent_div_12_div_7_div_3_Template", "TopFashionInfluencersComponent_div_12_div_7_span_26_Template", "TopFashionInfluencersComponent_div_12_div_7_span_27_Template", "TopFashionInfluencersComponent_div_12_div_7_Template_button_click_28_listener", "$event", "onFollowInfluencer", "avatar", "ɵɵsanitizeUrl", "fullName", "isVerified", "username", "category", "formatFollowerCount", "followerCount", "engagementRate", "slice", "isFollowing", "TopFashionInfluencersComponent_div_12_Template_button_click_1_listener", "_r4", "slidePrev", "TopFashionInfluencersComponent_div_12_Template_button_click_3_listener", "slideNext", "TopFashionInfluencersComponent_div_12_Template_div_mouseenter_5_listener", "pauseAutoSlide", "TopFashionInfluencersComponent_div_12_Template_div_mouseleave_5_listener", "resumeAutoSlide", "TopFashionInfluencersComponent_div_12_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "topInfluencers", "trackByInfluencerId", "TopFashionInfluencersComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "loadTopInfluencers", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "recentPosts", "updateSliderOnInfluencersLoad", "console", "influencer", "navigate", "event", "stopPropagation", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "log", "navigator", "share", "title", "text", "url", "location", "href", "clipboard", "writeText", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TopFashionInfluencersComponent_Template", "rf", "ctx", "TopFashionInfluencersComponent_div_1_Template", "TopFashionInfluencersComponent_div_9_Template", "TopFashionInfluencersComponent_div_10_Template", "TopFashionInfluencersComponent_div_11_Template", "TopFashionInfluencersComponent_div_12_Template", "TopFashionInfluencersComponent_div_13_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\n\ninterface TopInfluencer {\n  id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followerCount: number;\n  category: string;\n  isVerified: boolean;\n  isFollowing: boolean;\n  engagementRate: number;\n  recentPosts: number;\n  topBrands: string[];\n}\n\n@Component({\n  selector: 'app-top-fashion-influencers',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './top-fashion-influencers.component.html',\n  styleUrls: ['./top-fashion-influencers.component.scss']\n})\nexport class TopFashionInfluencersComponent implements OnInit, OnDestroy {\n  topInfluencers: TopInfluencer[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 240; // Width of each influencer card including margin\n  visibleCards = 3; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 6000; // 6 seconds for influencers\n  isAutoSliding = true;\n  isPaused = false;\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 512;\n  sectionComments = 234;\n  isMobile = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTopInfluencers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadTopInfluencers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for top fashion influencers\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: 'fashionista_queen',\n          fullName: 'Priya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followerCount: 2500000,\n          category: 'High Fashion',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 8.5,\n          recentPosts: 24,\n          topBrands: ['Gucci', 'Prada', 'Versace']\n        },\n        {\n          id: '2',\n          username: 'street_style_king',\n          fullName: 'Arjun Kapoor',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1800000,\n          category: 'Streetwear',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 12.3,\n          recentPosts: 18,\n          topBrands: ['Nike', 'Adidas', 'Supreme']\n        },\n        {\n          id: '3',\n          username: 'boho_goddess',\n          fullName: 'Ananya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1200000,\n          category: 'Boho Chic',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 9.7,\n          recentPosts: 32,\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\n        },\n        {\n          id: '4',\n          username: 'luxury_lifestyle',\n          fullName: 'Kavya Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followerCount: 3200000,\n          category: 'Luxury',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 6.8,\n          recentPosts: 15,\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n        },\n        {\n          id: '5',\n          username: 'minimalist_maven',\n          fullName: 'Ravi Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followerCount: 950000,\n          category: 'Minimalist',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 11.2,\n          recentPosts: 21,\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\n        },\n        {\n          id: '6',\n          username: 'vintage_vibes',\n          fullName: 'Meera Patel',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followerCount: 780000,\n          category: 'Vintage',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 13.5,\n          recentPosts: 28,\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnInfluencersLoad();\n    } catch (error) {\n      console.error('Error loading top influencers:', error);\n      this.error = 'Failed to load top influencers';\n      this.isLoading = false;\n    }\n  }\n\n  onInfluencerClick(influencer: TopInfluencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n\n  onFollowInfluencer(influencer: TopInfluencer, event: Event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    \n    if (influencer.isFollowing) {\n      influencer.followerCount++;\n    } else {\n      influencer.followerCount--;\n    }\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadTopInfluencers();\n  }\n\n  trackByInfluencerId(index: number, influencer: TopInfluencer): string {\n    return influencer.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 200;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 220;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 240;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 260;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when influencers load\n  private updateSliderOnInfluencersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for top fashion influencers section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Top Fashion Influencers',\n        text: 'Follow the top fashion trendsetters!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for top fashion influencers');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n\n\n}\n", "<div class=\"top-influencers-container\">\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"star\" class=\"title-icon\"></ion-icon>\n        Top Fashion Influencers\n      </h2>\n      <p class=\"section-subtitle\">Follow the trendsetters</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3]\" class=\"loading-influencer-card\">\n        <div class=\"loading-avatar\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n          <div class=\"loading-stats\">\n            <div class=\"loading-stat\"></div>\n            <div class=\"loading-stat\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- No Data State -->\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length === 0\" class=\"empty-state\">\n    <ion-icon name=\"star-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Top Influencers</h3>\n    <p class=\"empty-message\">Top fashion influencers will appear here when available</p>\n  </div>\n\n  <!-- Influencers Slider -->\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length > 0\" class=\"influencers-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n    \n    <!-- Slider Wrapper -->\n    <div class=\"influencers-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"influencers-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div \n          *ngFor=\"let influencer of topInfluencers; trackBy: trackByInfluencerId\" \n          class=\"influencer-card\"\n          (click)=\"onInfluencerClick(influencer)\"\n        >\n          <!-- Influencer Avatar -->\n          <div class=\"influencer-avatar-container\">\n            <img \n              [src]=\"influencer.avatar\"\n              [alt]=\"influencer.fullName\"\n              class=\"influencer-avatar\"\n              loading=\"lazy\"\n            />\n            <div *ngIf=\"influencer.isVerified\" class=\"verified-badge\">\n              <ion-icon name=\"checkmark\"></ion-icon>\n            </div>\n          </div>\n\n          <!-- Influencer Info -->\n          <div class=\"influencer-info\">\n            <h3 class=\"influencer-name\">{{ influencer.fullName }}</h3>\n            <p class=\"username\">&#64;{{ influencer.username }}</p>\n            <p class=\"category\">{{ influencer.category }}</p>\n            \n            <!-- Stats -->\n            <div class=\"stats-container\">\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ formatFollowerCount(influencer.followerCount) }}</span>\n                <span class=\"stat-label\">Followers</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ influencer.engagementRate }}%</span>\n                <span class=\"stat-label\">Engagement</span>\n              </div>\n            </div>\n            \n            <!-- Top Brands -->\n            <div class=\"top-brands\">\n              <span class=\"brands-label\">Works with:</span>\n              <div class=\"brands-list\">\n                <span *ngFor=\"let brand of influencer.topBrands.slice(0, 2)\" class=\"brand-tag\">\n                  {{ brand }}\n                </span>\n                <span *ngIf=\"influencer.topBrands.length > 2\" class=\"more-brands\">\n                  +{{ influencer.topBrands.length - 2 }}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Follow Button -->\n          <button \n            class=\"follow-btn\"\n            [class.following]=\"influencer.isFollowing\"\n            (click)=\"onFollowInfluencer(influencer, $event)\"\n          >\n            <span>{{ influencer.isFollowing ? 'Following' : 'Follow' }}</span>\n            <ion-icon [name]=\"influencer.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div> <!-- End influencers-slider-wrapper -->\n  </div> <!-- End influencers-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"star-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Influencers</h3>\n    <p class=\"empty-message\">Check back later for top fashion influencers</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;ICDxCC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,sEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,sEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,uEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,uEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IAuBxE7B,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAU,SAAA,cAAkC;IAClCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IACrCV,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAU,SAAA,cAAgC,cACA;IAGtCV,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;IAZRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,mDAAA,kBAAkE;IAatE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAboBZ,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAU;;;;;;IAgBpCjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,uEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAQtCrC,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,yBAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,8DAAuD;IAClFX,EADkF,CAAAY,YAAA,EAAI,EAChF;;;;;IA4BIZ,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAU,SAAA,mBAAsC;IACxCV,EAAA,CAAAY,YAAA,EAAM;;;;;IAyBFZ,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADLZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAC,QAAA,MACF;;;;;IACAvC,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADLZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,OAAAE,aAAA,CAAAC,SAAA,CAAAC,MAAA,UACF;;;;;;IA7CR1C,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAyC,0EAAA;MAAA,MAAAH,aAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwC,iBAAA,CAAAN,aAAA,CAA6B;IAAA,EAAC;IAGvCxC,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAU,SAAA,cAKE;IACFV,EAAA,CAAA8B,UAAA,IAAAiB,0DAAA,kBAA0D;IAG5D/C,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,cAA6B,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC1DZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,GAA8B;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACtDZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,IAAyB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAK7CZ,EAFJ,CAAAC,cAAA,eAA6B,eACT,gBACS;IAAAD,EAAA,CAAAW,MAAA,IAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnFZ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAW,MAAA,iBAAS;IACpCX,EADoC,CAAAY,YAAA,EAAO,EACrC;IAEJZ,EADF,CAAAC,cAAA,eAAkB,gBACS;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChEZ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAW,MAAA,kBAAU;IAEvCX,EAFuC,CAAAY,YAAA,EAAO,EACtC,EACF;IAIJZ,EADF,CAAAC,cAAA,eAAwB,gBACK;IAAAD,EAAA,CAAAW,MAAA,mBAAW;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7CZ,EAAA,CAAAC,cAAA,eAAyB;IAIvBD,EAHA,CAAA8B,UAAA,KAAAkB,4DAAA,mBAA+E,KAAAC,4DAAA,mBAGb;IAKxEjD,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;IAGNZ,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAgD,8EAAAC,MAAA;MAAA,MAAAX,aAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,kBAAA,CAAAZ,aAAA,EAAAW,MAAA,CAAsC;IAAA,EAAC;IAEhDnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAAqD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAClEZ,EAAA,CAAAU,SAAA,oBAA2E;IAE/EV,EADE,CAAAY,YAAA,EAAS,EACL;;;;;IAnDAZ,EAAA,CAAAqB,SAAA,GAAyB;IACzBrB,EADA,CAAAwB,UAAA,QAAAgB,aAAA,CAAAa,MAAA,EAAArD,EAAA,CAAAsD,aAAA,CAAyB,QAAAd,aAAA,CAAAe,QAAA,CACE;IAIvBvD,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAgB,UAAA,CAA2B;IAOLxD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAe,QAAA,CAAyB;IACjCvD,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsC,kBAAA,MAAAE,aAAA,CAAAiB,QAAA,KAA8B;IAC9BzD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAkB,QAAA,CAAyB;IAKhB1D,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAqD,mBAAA,CAAAnB,aAAA,CAAAoB,aAAA,EAAmD;IAInD5D,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAsC,kBAAA,KAAAE,aAAA,CAAAqB,cAAA,MAAgC;IASjC7D,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAwB,UAAA,YAAAgB,aAAA,CAAAC,SAAA,CAAAqB,KAAA,OAAmC;IAGpD9D,EAAA,CAAAqB,SAAA,EAAqC;IAArCrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAC,SAAA,CAAAC,MAAA,KAAqC;IAUhD1C,EAAA,CAAAqB,SAAA,EAA0C;IAA1CrB,EAAA,CAAAsB,WAAA,cAAAkB,aAAA,CAAAuB,WAAA,CAA0C;IAGpC/D,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAuB,WAAA,0BAAqD;IACjD/D,EAAA,CAAAqB,SAAA,EAAqD;IAArDrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAuB,WAAA,uBAAqD;;;;;;IAnEvE/D,EAFF,CAAAC,cAAA,cAAoG,iBAER;IAAtDD,EAAA,CAAAE,UAAA,mBAAA8D,uEAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4D,SAAA,EAAW;IAAA,EAAC;IACvDlE,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAiE,uEAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8D,SAAA,EAAW;IAAA,EAAC;IACvDpE,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAyG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAAmE,yEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAgE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,yEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAkE,eAAA,EAAiB;IAAA,EAAC;IACtGxE,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAA8B,UAAA,IAAA2C,oDAAA,oBAIC;IA0DPzE,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAxEsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoE,YAAA,OAA+B;IAG/B1E,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoE,YAAA,IAAApE,MAAA,CAAAqE,QAAA,CAAqC;IAM7D3E,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAA4E,WAAA,8BAAAtE,MAAA,CAAAuE,WAAA,SAAuD;IAE5D7E,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAwE,cAAA,CAAmB,iBAAAxE,MAAA,CAAAyE,mBAAA,CAA4B;;;;;IAgE9E/E,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,mDAA4C;IACvEX,EADuE,CAAAY,YAAA,EAAI,EACrE;;;ADrIR,OAAM,MAAOoE,8BAA8B;EA0BzCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAzB1B,KAAAJ,cAAc,GAAoB,EAAE;IACpC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAA9C,KAAK,GAAkB,IAAI;IACnB,KAAA+C,YAAY,GAAiB,IAAItF,YAAY,EAAE;IAEvD;IACA,KAAA4E,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAlE,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAA8D,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,YAAY,CAACa,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcN,kBAAkBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAChB,SAAS,GAAG,IAAI;QACrBgB,KAAI,CAAC9D,KAAK,GAAG,IAAI;QAEjB;QACA8D,KAAI,CAACrB,cAAc,GAAG,CACpB;UACEuB,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,cAAc;UACxBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,cAAc;UACxBF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,WAAW;UACrBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,MAAM;SACnD,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,0FAA0F;UAClGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,QAAQ;UAClBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe;SAC9C,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,YAAY;UACtBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,eAAe;UACzBF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,SAAS;UACnBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,QAAQ;SACvD,CACF;QAED0D,KAAI,CAAChB,SAAS,GAAG,KAAK;QACtBgB,KAAI,CAACI,6BAA6B,EAAE;OACrC,CAAC,OAAOlE,KAAK,EAAE;QACdmE,OAAO,CAACnE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD8D,KAAI,CAAC9D,KAAK,GAAG,gCAAgC;QAC7C8D,KAAI,CAAChB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEArC,iBAAiBA,CAAC2D,UAAyB;IACzC,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,UAAU,EAAED,UAAU,CAAChD,QAAQ,CAAC,CAAC;EACzD;EAEAL,kBAAkBA,CAACqD,UAAyB,EAAEE,KAAY;IACxDA,KAAK,CAACC,eAAe,EAAE;IACvBH,UAAU,CAAC1C,WAAW,GAAG,CAAC0C,UAAU,CAAC1C,WAAW;IAEhD,IAAI0C,UAAU,CAAC1C,WAAW,EAAE;MAC1B0C,UAAU,CAAC7C,aAAa,EAAE;KAC3B,MAAM;MACL6C,UAAU,CAAC7C,aAAa,EAAE;;EAE9B;EAEAD,mBAAmBA,CAACkD,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA3E,OAAOA,CAAA;IACL,IAAI,CAACwD,kBAAkB,EAAE;EAC3B;EAEAb,mBAAmBA,CAACiC,KAAa,EAAEP,UAAyB;IAC1D,OAAOA,UAAU,CAACJ,EAAE;EACtB;EAEA;EACQY,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACS,aAAa,EAAE;IACpB,IAAI,CAACgB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAAC1B,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACpC,MAAM,GAAG,IAAI,CAAC4C,YAAY,EAAE;QACpE,IAAI,CAAC8B,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC7B,cAAc,CAAC;EACzB;EAEQW,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACgB,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQE,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC1C,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC4C,iBAAiB,EAAE;EAC1B;EAEAhD,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACS,aAAa,EAAE;EACtB;EAEA1B,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACwB,cAAc,EAAE;EACvB;EAEA;EACQpB,wBAAwBA,CAAA;IAC9B,MAAM0B,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACoC,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQxB,mBAAmBA,CAAA;IACzB0B,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC9B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA6B,kBAAkBA,CAAA;IAChB,IAAI,CAAC/C,QAAQ,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/C,cAAc,CAACpC,MAAM,GAAG,IAAI,CAAC4C,YAAY,CAAC;EAC7E;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEA1D,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACzC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQyC,gCAAgCA,CAAA;IACtC,IAAI,CAAC5B,aAAa,EAAE;IACpB6B,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,6BAA6BA,CAAA;IACnCwB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAChD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACoC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAxG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACV0F,OAAO,CAACwB,GAAG,CAAC,sDAAsD,CAAC;EACrE;EAEAhH,YAAYA,CAAA;IACV,IAAIiH,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,sCAAsC;QAC5CC,GAAG,EAAEb,MAAM,CAACc,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACLN,SAAS,CAACO,SAAS,CAACC,SAAS,CAACjB,MAAM,CAACc,QAAQ,CAACC,IAAI,CAAC;MACnD/B,OAAO,CAACwB,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEA5G,eAAeA,CAAA;IACboF,OAAO,CAACwB,GAAG,CAAC,kDAAkD,CAAC;EACjE;EAEAtG,WAAWA,CAACmF,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEQhB,iBAAiBA,CAAA;IACvB,IAAI,CAACL,QAAQ,GAAG8B,MAAM,CAACC,UAAU,IAAI,GAAG;EAC1C;;;uBAhUWzC,8BAA8B,EAAAhF,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9B5D,8BAA8B;MAAA6D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/I,EAAA,CAAAgJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3B3CtJ,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAA0H,6CAAA,kBAAoD;UAiChDxJ,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAoD;UACpDV,EAAA,CAAAW,MAAA,gCACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,8BAAuB;UAEvDX,EAFuD,CAAAY,YAAA,EAAI,EACnD,EACF;UAmHNZ,EAhHA,CAAA8B,UAAA,IAAA2H,6CAAA,iBAAiD,KAAAC,8CAAA,iBAkBQ,KAAAC,8CAAA,iBAU4B,KAAAC,8CAAA,kBAOe,KAAAC,8CAAA,kBA6EX;UAK3F7J,EAAA,CAAAY,YAAA,EAAM;;;UA/JgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAA7D,QAAA,CAAc;UA0C5C1F,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAApE,SAAA,CAAe;UAkBfnF,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAAlH,KAAA,KAAAkH,GAAA,CAAApE,SAAA,CAAyB;UAUzBnF,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+H,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAlH,KAAA,IAAAkH,GAAA,CAAAzE,cAAA,CAAApC,MAAA,OAAyD;UAOzD1C,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAA+H,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAlH,KAAA,IAAAkH,GAAA,CAAAzE,cAAA,CAAApC,MAAA,KAAuD;UA6EvD1C,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+H,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAlH,KAAA,IAAAkH,GAAA,CAAAzE,cAAA,CAAApC,MAAA,OAAyD;;;qBDrIrD7C,YAAY,EAAAiK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjK,WAAW,EAAAkK,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}