{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template, 6, 5, \"ng-template\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵelement(6, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"div\", 22)(12, \"owl-carousel-o\", 23);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_2_ng_container_13_Template, 2, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 62);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 63);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"div\", 70);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_3_div_21_div_1_Template, 8, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 77);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 18);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30, 0)(3, \"div\", 31);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_3_div_4_Template, 2, 4, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"div\", 35);\n    i0.ɵɵelement(8, \"div\", 36);\n    i0.ɵɵelementStart(9, \"div\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 39);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 42);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_3_video_18_Template, 1, 1, \"video\", 43)(19, ViewAddStoriesComponent_div_3_div_19_Template, 1, 2, \"div\", 44)(20, ViewAddStoriesComponent_div_3_div_20_Template, 2, 1, \"div\", 45)(21, ViewAddStoriesComponent_div_3_div_21_Template, 2, 1, \"div\", 46)(22, ViewAddStoriesComponent_div_3_div_22_Template, 5, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 48)(24, \"div\", 49)(25, \"button\", 50);\n    i0.ɵɵelement(26, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵelement(28, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 54);\n    i0.ɵɵelement(30, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_3_div_31_Template, 13, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 57)(33, \"div\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 59, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81);\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 83)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 84);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // Owl Carousel Options\n    this.customOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false\n        },\n        400: {\n          items: 4,\n          nav: false\n        },\n        740: {\n          items: 5,\n          nav: true\n        },\n        940: {\n          items: 6,\n          nav: true\n        }\n      },\n      nav: true,\n      margin: 2,\n      stagePadding: 0,\n      autoplay: true,\n      autoplayTimeout: 4000,\n      autoplayHoverPause: true,\n      autoplaySpeed: 1000 // Animation speed for auto sliding\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: 'user1',\n        username: 'zara',\n        fullName: 'Zara Official',\n        avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'New Summer Collection 🌞',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '2',\n      user: {\n        _id: 'user2',\n        username: 'nike',\n        fullName: 'Nike',\n        avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Just Do It ✨',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 2340,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '3',\n      user: {\n        _id: 'user3',\n        username: 'adidas',\n        fullName: 'Adidas',\n        avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Impossible is Nothing 🔥',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 1890,\n      isActive: true,\n      isViewed: false\n    }, {\n      _id: '4',\n      user: {\n        _id: 'user4',\n        username: 'hm',\n        fullName: 'H&M',\n        avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n      mediaType: 'image',\n      caption: 'Fashion for Everyone 💫',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 3420,\n      isActive: true,\n      isViewed: false\n    }];\n    this.isLoadingStories = false;\n  }\n  // Removed fallback stories - only use database data\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        stories: \"stories\",\n        showAddStory: \"showAddStory\",\n        currentUser: \"currentUser\"\n      },\n      outputs: {\n        storyClick: \"storyClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 14, 4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_3_Template, 36, 17, \"div\", 5)(4, ViewAddStoriesComponent_div_4_Template, 9, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n  width: 100%;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (min-width: 769px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    border: 1px solid #dbdbdb;\\n    border-radius: 8px;\\n    padding: 20px;\\n    margin-bottom: 24px;\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n    position: relative;\\n    z-index: 10;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n    border: none;\\n    border-radius: 0;\\n    border-bottom: 1px solid #efefef;\\n    background: #fafafa;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n  }\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 0;\\n}\\n@media (min-width: 769px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 20px;\\n    align-items: center;\\n    min-height: 120px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 16px;\\n  }\\n}\\n\\n.add-story-static[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 82px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  max-width: calc(100% - 98px);\\n  position: relative;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 100%;\\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);\\n  pointer-events: none;\\n  z-index: 5;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]::after {\\n  opacity: 1;\\n}\\n\\n.stories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow: visible; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  min-height: 120px;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5); \\n\\n  color: #fff; \\n\\n  border: none;\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  pointer-events: all;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas {\\n  font-size: 14px;\\n  color: #fff;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -20px; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -20px; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n    display: none;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer {\\n  position: relative;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_autoSlideIndicator 4s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_autoSlideIndicator {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 66px;\\n}\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  animation-play-state: paused;\\n}\\n\\n.slider-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.slider-nav-btn.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n}\\n\\n.slider-nav-left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n\\n.slider-nav-right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n\\n.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  width: 82px;\\n  min-width: 82px;\\n  position: relative;\\n}\\n@media (min-width: 769px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    min-width: 90px;\\n    padding: 8px;\\n    border-radius: 12px;\\n  }\\n  .story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n    background: rgba(0, 0, 0, 0.05);\\n    transform: scale(1.08);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 76px;\\n    min-width: 76px;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%] {\\n  animation-duration: 1s;\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n}\\n.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #405de6;\\n  font-weight: 600;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-container[_ngcontent-%COMP%] {\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 2px solid white;\\n  position: relative;\\n  z-index: 2;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    border: 1.5px solid #fff;\\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n}\\n.story-ring.viewed[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  animation: none;\\n}\\n.story-ring.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n  box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n    font-weight: 500;\\n  }\\n}\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  position: relative;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 3;\\n}\\n.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n  font-weight: bold;\\n}\\n\\n.current-user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 12px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 82px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    min-width: 70px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 0 8px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    min-width: 60px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n  .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .current-user-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n.middle-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 4;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .middle-navigation[_ngcontent-%COMP%] {\\n    bottom: 80px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 12px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵlistener", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "i_r4", "ɵɵnextContext", "index", "ctx_r1", "ɵɵresetView", "openStories", "ɵɵtext", "ɵɵstyleProp", "story_r5", "user", "avatar", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "username", "ɵɵelementContainerStart", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template", "ViewAddStoriesComponent_div_2_Template_div_click_2_listener", "_r1", "openAddStoryModal", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener", "$event", "onInitialized", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener", "onSlideChanged", "ViewAddStoriesComponent_div_2_ng_container_13_Template", "getCurrentUserAvatar", "customOptions", "stories", "i_r7", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener", "product_r9", "_r8", "$implicit", "viewProduct", "_id", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_3_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener", "_r10", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener", "_r11", "buyNow", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener", "addToWishlist", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener", "addToCart", "ViewAddStoriesComponent_div_3_div_4_Template", "ViewAddStoriesComponent_div_3_Template_div_click_5_listener", "_r6", "onStoryClick", "ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_3_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_3_video_18_Template", "ViewAddStoriesComponent_div_3_div_19_Template", "ViewAddStoriesComponent_div_3_div_20_Template", "ViewAddStoriesComponent_div_3_div_21_Template", "ViewAddStoriesComponent_div_3_div_22_Template", "ViewAddStoriesComponent_div_3_div_31_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "responsive", "items", "nav", "margin", "stagePadding", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "subscriptions", "ngOnInit", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "Date", "now", "toISOString", "expiresAt", "isActive", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "num", "undefined", "toFixed", "toString", "showStory", "document", "body", "style", "overflow", "emit", "story", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "touches", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "products", "toLocaleString", "viewProductDetails", "product", "console", "log", "navigate", "queryParams", "productId", "source", "trackProductClick", "viewCategory", "categoryId", "action", "subscribe", "next", "response", "success", "alert", "error", "startPosition", "updateSlideAnalytics", "currentStory", "toggleAutoPlay", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "CartService", "i4", "WishlistService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "ViewAddStoriesComponent_div_4_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mponent, OnInit, OnD<PERSON>roy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\nimport { environment } from '../../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // Owl Carousel Options\n  customOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    responsive: {\n      0: {\n        items: 3,\n        nav: false\n      },\n      400: {\n        items: 4,\n        nav: false\n      },\n      740: {\n        items: 5,\n        nav: true\n      },\n      940: {\n        items: 6,\n        nav: true\n      }\n    },\n    nav: true,\n    margin: 2, // Minimal gap between items\n    stagePadding: 0,\n    autoplay: true, // Enable auto sliding\n    autoplayTimeout: 4000, // 4 seconds between slides\n    autoplayHoverPause: true, // Pause on hover\n    autoplaySpeed: 1000 // Animation speed for auto sliding\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Use mock stories data for now since stories API is not implemented\n    this.stories = [\n      {\n        _id: '1',\n        user: {\n          _id: 'user1',\n          username: 'zara',\n          fullName: 'Zara Official',\n          avatar: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=face'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'New Summer Collection 🌞',\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(), // 22 hours from now\n        views: 1250,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '2',\n        user: {\n          _id: 'user2',\n          username: 'nike',\n          fullName: 'Nike',\n          avatar: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Just Do It ✨',\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(), // 20 hours from now\n        views: 2340,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '3',\n        user: {\n          _id: 'user3',\n          username: 'adidas',\n          fullName: 'Adidas',\n          avatar: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Impossible is Nothing 🔥',\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now\n        views: 1890,\n        isActive: true,\n        isViewed: false\n      },\n      {\n        _id: '4',\n        user: {\n          _id: 'user4',\n          username: 'hm',\n          fullName: 'H&M',\n          avatar: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=100&fit=crop&crop=center'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        caption: 'Fashion for Everyone 💫',\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(), // 16 hours from now\n        views: 3420,\n        isActive: true,\n        isViewed: false\n      }\n    ];\n\n    this.isLoadingStories = false;\n  }\n\n  // Removed fallback stories - only use database data\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (!num || num === undefined || num === null) {\n      return '0';\n    }\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || '/assets/images/default-avatar.svg';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Section -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Add Story Button (Static - Outside Slider) -->\n    <div class=\"add-story-static\">\n      <div class=\"story-item add-story-item\" (click)=\"openAddStoryModal()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"add-story-avatar\">\n            <div class=\"add-story-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"current-user-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\"></div>\n          </div>\n        </div>\n        <div class=\"story-username\">Add Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider-wrapper\">\n      <!-- Owl Carousel Stories Slider -->\n      <div class=\"stories-slider-container\">\n        <owl-carousel-o\n          [options]=\"customOptions\"\n          (initialized)=\"onInitialized($event)\"\n          (changed)=\"onSlideChanged($event)\">\n\n          <!-- User Stories -->\n          <ng-container *ngFor=\"let story of stories; let i = index\">\n            <ng-template carouselSlide>\n              <div class=\"story-slide\" (click)=\"openStories(i)\">\n                <div class=\"story-avatar-container\">\n                  <div class=\"story-avatar\"\n                       [style.background-image]=\"'url(' + story.user.avatar + ')'\">\n                  </div>\n                  <div class=\"story-ring\"\n                       [class.viewed]=\"story.isViewed\">\n                  </div>\n                </div>\n                <div class=\"story-username\">{{ story.user.username }}</div>\n              </div>\n            </ng-template>\n          </ng-container>\n        </owl-carousel-o>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product._id)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Middle Point Navigation Button -->\n        <div class=\"middle-navigation\" *ngIf=\"hasProducts()\">\n          <button class=\"middle-nav-btn\" (click)=\"viewProduct(getStoryProducts()[0]._id)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>Shop Now</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\" *ngIf=\"hasProducts()\">\n          <button class=\"ecommerce-btn buy-now-btn\" (click)=\"buyNow()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\" (click)=\"addToCart()\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2FA,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICH3DC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAmC1BT,EAAA,CAAAC,cAAA,cAAkD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,0FAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAC/Cd,EAAA,CAAAC,cAAA,cAAoC;IAIlCD,EAHA,CAAAE,SAAA,cAEM,cAGA;IACRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IACvDpB,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAPGH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAqB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3DxB,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAyB,WAAA,WAAAH,QAAA,CAAAI,QAAA,CAA+B;IAGV1B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA2B,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAK,QAAA,CAAyB;;;;;IAX3D5B,EAAA,CAAA6B,uBAAA,GAA2D;IACzD7B,EAAA,CAAAI,UAAA,IAAA0B,oEAAA,0BAA2B;;;;;;;IAxBjC9B,EAHJ,CAAAC,cAAA,cAAuD,cAEvB,cACyC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAqB,4DAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgB,iBAAA,EAAmB;IAAA,EAAC;IAG9DjC,EAFJ,CAAAC,cAAA,cAAoC,cACJ,cACA;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAwG;IAE5GF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,gBAAS;IAEzCpB,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;IAMFH,EAHJ,CAAAC,cAAA,eAAoC,eAEI,0BAIC;IAAnCD,EADA,CAAAU,UAAA,yBAAAwB,8EAAAC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAeD,MAAA,CAAAmB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,qBAAAE,0EAAAF,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAC1BD,MAAA,CAAAqB,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAGlCnC,EAAA,CAAAI,UAAA,KAAAmC,sDAAA,2BAA2D;IAkBnEvC,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACF;;;;IAnCqCH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAAuB,oBAAA,SAAgE;IAYnGxC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAwB,aAAA,CAAyB;IAKOzC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;;;;;IA2BhD1C,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAAyB,WAAA,WAAAkB,IAAA,KAAA1B,MAAA,CAAA2B,YAAA,CAAmC,cAAAD,IAAA,GAAA1B,MAAA,CAAA2B,YAAA,CACC;;;;;IA6BpC5C,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAU,MAAA,CAAA4B,eAAA,GAAAC,QAAA,EAAA9C,EAAA,CAAA+C,aAAA,CAAkC;;;;;IAQpC/C,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItE9C,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgD,kBAAA,MAAA/B,MAAA,CAAA4B,eAAA,GAAAI,OAAA,MACF;;;;;;IAIEjD,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAU,UAAA,mBAAAwC,yEAAA;MAAA,MAAAC,UAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAApC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAAH,UAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IAClCvD,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAoB,MAAA,yBAAG;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAoB,MAAA,GAAkB;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAoB,MAAA,GAAgC;IAEnEpB,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAA2B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACjBxD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAwC,WAAA,CAAAN,UAAA,CAAAO,KAAA,EAAgC;;;;;IARrE1D,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAI,UAAA,IAAAuD,mDAAA,kBAGqC;IAOvC3D,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAA2C,gBAAA,GAAqB;;;;;;IAa3C5D,EADF,CAAAC,cAAA,cAAqD,iBAC6B;IAAjDD,EAAA,CAAAU,UAAA,mBAAAmD,sEAAA;MAAA7D,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAA7C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAYrC,MAAA,CAAA2C,gBAAA,EAAkB,CAAC,CAAC,EAAAL,GAAA,CAAM;IAAA,EAAC;IAC7EvD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAElBpB,EAFkB,CAAAG,YAAA,EAAO,EACd,EACL;;;;;;IAmBJH,EADF,CAAAC,cAAA,cAA4D,iBACG;IAAnBD,EAAA,CAAAU,UAAA,mBAAAqD,sEAAA;MAAA/D,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgD,MAAA,EAAQ;IAAA,EAAC;IAC1DjE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,cAAO;IACfpB,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA1BD,EAAA,CAAAU,UAAA,mBAAAwD,sEAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAkD,aAAA,EAAe;IAAA,EAAC;IAClEnE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAChBpB,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,iBAA6D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0D,sEAAA;MAAApE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAoD,SAAA,EAAW;IAAA,EAAC;IAC1DrE,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,mBAAW;IAErBpB,EAFqB,CAAAG,YAAA,EAAO,EACjB,EACL;;;;;;IA3GVH,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAkE,4CAAA,kBAIuC;IAGzCtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAA6D,4DAAApC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAwD,YAAA,CAAAtC,MAAA,CAAoB;IAAA,EAAC,wBAAAuC,iEAAAvC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAChBD,MAAA,CAAA0D,YAAA,CAAAxC,MAAA,CAAoB;IAAA,EAAC,uBAAAyC,gEAAAzC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACtBD,MAAA,CAAA4D,WAAA,CAAA1C,MAAA,CAAmB;IAAA,EAAC,sBAAA2C,+DAAA3C,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACrBD,MAAA,CAAA8D,UAAA,CAAA5C,MAAA,CAAkB;IAAA,EAAC;IAIhCnC,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAAqC;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAA6C;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAoB,MAAA,IAAiD;IAC7EpB,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAsE,gEAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgE,YAAA,EAAc;IAAA,EAAC;IACnDjF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAuC1BD,EArCA,CAAAI,UAAA,KAAA8E,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP,KAAAC,6CAAA,kBAcF;IAMvDtF,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAmF,6CAAA,mBAA4D;IAc9DvF,EAAA,CAAAG,YAAA,EAAM;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IA1HuBH,EAAA,CAAAyB,WAAA,YAAAR,MAAA,CAAAuE,MAAA,CAAwB;IAM3BxF,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;IAU7B1C,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAC,MAAA,OAAuE;IACzExB,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAkE,QAAA,CAAqC;IACrCzF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAyE,UAAA,CAAAzE,MAAA,CAAA4B,eAAA,GAAA8C,SAAA,EAA6C;IAC5C3F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAgD,kBAAA,KAAA/B,MAAA,CAAA2E,YAAA,CAAA3E,MAAA,CAAA4B,eAAA,GAAAgD,KAAA,YAAiD;IAW1E7F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAW7C9F,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAM1C9F,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAI,OAAA,CAA+B;IAK/BjD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAcO/F,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuBZ/F,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuB5B/F,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAyB,WAAA,cAAAR,MAAA,CAAAuE,MAAA,CAA0B;;;;;IAK9DxF,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,qBAAc;IACtBpB,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAoB,MAAA,sBAAe;IAAApB,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;ADjJN,OAAM,MAAO6F,uBAAuB;EAsElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IArEhB,KAAA3D,OAAO,GAAY,EAAE;IACrB,KAAA4D,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAI3G,YAAY,EAAmC;IAE1E,KAAA4G,gBAAgB,GAAG,IAAI;IAEvB,KAAA7D,YAAY,GAAG,CAAC;IAChB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAkB,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA7E,aAAa,GAAe;MAC1B8E,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;;OAER;MACDA,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,IAAI,CAAC;KACrB;IAEO,KAAAC,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAAC9F,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+F,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACjC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACkC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAC/D,OAAO,GAAG,CACb;MACEa,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,eAAe;QACzBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,MAAM;QAChB6D,QAAQ,EAAE,MAAM;QAChBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,gFAAgF;MAC1FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,cAAc;MACvB0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,QAAQ;QAClB6D,QAAQ,EAAE,QAAQ;QAClBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,0BAA0B;MACnC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,EACD;MACE6B,GAAG,EAAE,GAAG;MACRhC,IAAI,EAAE;QACJgC,GAAG,EAAE,OAAO;QACZ3B,QAAQ,EAAE,IAAI;QACd6D,QAAQ,EAAE,KAAK;QACfjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,mFAAmF;MAC7FgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,yBAAyB;MAClC0C,SAAS,EAAE,IAAIsD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEtD,KAAK,EAAE,IAAI;MACXwD,QAAQ,EAAE,IAAI;MACd3H,QAAQ,EAAE;KACX,CACF;IAED,IAAI,CAAC+E,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EAEA5D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAgD,UAAUA,CAAC4D,UAAkB;IAC3B,MAAMJ,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMM,IAAI,GAAG,IAAIN,IAAI,CAACK,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACR,GAAG,CAACS,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAjE,YAAYA,CAACkE,GAAW;IACtB,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;MAC7C,OAAO,GAAG;;IAEZ,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOF,GAAG,CAACG,QAAQ,EAAE;EACvB;EAEA9I,WAAWA,CAACH,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAACwE,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC0E,SAAS,CAAClJ,KAAK,CAAC;IACrBmJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAAC5H,OAAO,CAAC1B,KAAK,CAAC,EAAE;MACvB,IAAI,CAACwF,UAAU,CAAC+D,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAAC9H,OAAO,CAAC1B,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAiE,YAAYA,CAAA;IACV,IAAI,CAACO,MAAM,GAAG,KAAK;IACnB,IAAI,CAACiF,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAb,SAASA,CAAClJ,KAAa;IACrB,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAAC4F,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAAC8D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACrI,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC+F,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC5B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACwE,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAEAkG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACvI,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACiE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACwE,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAGAmG,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC7F,MAAM,EAAE;IAElB,QAAQ6F,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAAChG,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAAC4G,KAAiB;IAC5B,IAAI,IAAI,CAAC3E,UAAU,EAAE;IAErB,MAAM6E,IAAI,GAAIF,KAAK,CAACG,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACV,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEAtG,YAAYA,CAAC0G,KAAiB;IAC5B,IAAI,CAAC1E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGsE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC1C,IAAI,CAAC3E,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEAlC,WAAWA,CAACwG,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC1E,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAGqE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC5C,MAAMI,YAAY,GAAG,IAAI,CAAC/E,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAMiF,WAAW,GAAGvC,IAAI,CAACwC,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAAC/E,0BAA0B,EAAE;MACjD,IAAI8E,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACZ,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAACtE,UAAU,GAAG,KAAK;;EAE3B;EAEA5B,UAAUA,CAACqH,MAAkB;IAC3B,IAAI,CAACzF,UAAU,GAAG,KAAK;EACzB;EAEQgC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGMyB,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGlC,QAAQ,CAACmC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACxD,OAAO,CAAC0D,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQtB,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACxE,UAAU,EAAE;IAEtB,MAAM+F,IAAI,GAAG,IAAI,CAAC5F,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAI6F,IAAI,GAAG,GAAG;IAE1B,IAAIhD,IAAI,CAACwC,GAAG,CAACQ,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAAC7F,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAAClE,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACkE,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAAClE,YAAY,EAAE;;MAGrB,IAAI,CAACiE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAAC4D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,6BAA6B,IAAI,CAACpE,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnBgG,qBAAqB,CAAC,MAAM,IAAI,CAACxB,MAAM,EAAE,CAAC;;EAE9C;EAEAnF,WAAWA,CAAA;IACT,MAAMyE,KAAK,GAAG,IAAI,CAAC3H,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE2H,KAAK,EAAEmC,QAAQ,IAAInC,KAAK,CAACmC,QAAQ,CAAClE,MAAM,GAAG,CAAC,CAAC;EACzD;EAEA7E,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,eAAe,EAAE,CAAC8J,QAAQ,IAAI,EAAE;EAC9C;EAEAlJ,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAEkJ,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACxC;IACA,IAAI,CAAC5G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,WAAW,EAAEH,OAAO,CAACvJ,GAAG,CAAC,CAAC;EAClD;EAEAf,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAAC+D,WAAW,EAAE/E,MAAM,IAAI,mCAAmC;EACxE;EAEAS,iBAAiBA,CAAA;IACf8K,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAAC9G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAhJ,MAAMA,CAAA;IACJ,MAAM0I,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7BI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvC;MACA,IAAI,CAAC5G,MAAM,CAAC+G,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,EAAE;UACXC,SAAS,EAAEL,OAAO,CAACvJ,GAAG;UACtB6J,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACA9J,WAAWA,CAAC6J,SAAiB;IAC3B;IACA,IAAI,CAACE,iBAAiB,CAACF,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAACjH,MAAM,CAAC+G,QAAQ,CAAC,CAAC,eAAe,EAAEE,SAAS,CAAC,CAAC;EACpD;EAEAG,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAACrH,MAAM,CAAC+G,QAAQ,CAAC,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACF,SAAiB,EAAEK,MAAc;IACzD;IACAT,OAAO,CAACC,GAAG,CAAC,iBAAiBQ,MAAM,WAAW,EAAEL,SAAS,CAAC;IAC1D;EACF;EAEAhJ,aAAaA,CAAA;IACX,MAAMwI,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;MAC3BI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;MAE3C,IAAI,CAACzG,eAAe,CAAClC,aAAa,CAAC2I,OAAO,CAACvJ,GAAG,CAAC,CAACkK,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDD,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEAxJ,SAASA,CAAA;IACP,MAAMsI,QAAQ,GAAG,IAAI,CAAC/I,gBAAgB,EAAE;IACxC,IAAI+I,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqE,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;MAC3BI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MAEvC,IAAI,CAAC1G,WAAW,CAAC/B,SAAS,CAACyI,OAAO,CAACvJ,GAAG,EAAE,CAAC,EAAEwG,SAAS,EAAEA,SAAS,CAAC,CAAC0D,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpBC,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CD,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACAvL,cAAcA,CAAC+I,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAAC0C,aAAa,KAAKhE,SAAS,EAAE;MAC9C,IAAI,CAACzC,iBAAiB,GAAG+D,KAAK,CAAC0C,aAAa;MAE5C;MACAhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAAC1F,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAAC0G,oBAAoB,EAAE;;EAE/B;EAEA5L,aAAaA,CAACgK,MAAW;IACvB;IACA,IAAI,CAAChF,qBAAqB,GAAG,IAAI;IACjC2F,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQgB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAACtL,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC,EAAE;MACxD,MAAM2G,YAAY,GAAG,IAAI,CAACvL,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC;MACzDyF,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,YAAY,CAAC1M,IAAI,CAACK,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACAsM,cAAcA,CAAA;IACZ,IAAI,CAAC7G,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACA0F,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAC3F,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;;;uBA9fWrB,uBAAuB,EAAAhG,EAAA,CAAAmO,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArO,EAAA,CAAAmO,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAvO,EAAA,CAAAmO,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAzO,EAAA,CAAAmO,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB3I,uBAAuB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB/O,EAAA,CAAAU,UAAA,qBAAAuO,mDAAA9M,MAAA;YAAA,OAAA6M,GAAA,CAAA5D,aAAA,CAAAjJ,MAAA,CAAqB;UAAA,UAAAnC,EAAA,CAAAkP,iBAAA,CAAE;;;;;;;;;;;;;;;;;;UC/CpClP,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAAI,UAAA,IAAA+O,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC;UA6CzDpP,EAAA,CAAAG,YAAA,EAAM;UAgINH,EA7HA,CAAAI,UAAA,IAAAiP,sCAAA,mBAAqE,IAAAC,sCAAA,iBA6HxB;;;UArLrCtP,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAvI,gBAAA,CAAsB;UAQEzG,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAAyO,GAAA,CAAAvI,gBAAA,CAAuB;UAgDAzG,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAxJ,MAAA,CAAY;UA6HpCxF,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyO,GAAA,CAAAxJ,MAAA,CAAY;;;qBD5I/B1F,YAAY,EAAAyP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1P,cAAc,EAAA2P,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}