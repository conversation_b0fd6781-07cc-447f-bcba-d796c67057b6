<div class="suggested-users-container">
  <!-- Debug: Component Status -->
  <div style="background: #f3e5f5; padding: 8px; margin-bottom: 10px; font-size: 11px; border-radius: 4px;">
    <strong>Suggested For You Component</strong><br>
    Loading: {{ isLoading }}, Error: {{ error }}, Users: {{ suggestedUsers.length }}, Mobile: {{ isMobile }}
  </div>

  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->
  <div class="mobile-action-buttons" *ngIf="isMobile">
    <button class="action-btn like-btn"
            [class.active]="isSectionLiked"
            (click)="toggleSectionLike()">
      <ion-icon [name]="isSectionLiked ? 'heart' : 'heart-outline'"></ion-icon>
      <span class="action-count">{{ formatCount(sectionLikes) }}</span>
    </button>

    <button class="action-btn comment-btn" (click)="openComments()">
      <ion-icon name="chatbubble-outline"></ion-icon>
      <span class="action-count">{{ formatCount(sectionComments) }}</span>
    </button>

    <button class="action-btn share-btn" (click)="shareSection()">
      <ion-icon name="arrow-redo-outline"></ion-icon>
      <span class="action-text">Share</span>
    </button>

    <button class="action-btn bookmark-btn"
            [class.active]="isSectionBookmarked"
            (click)="toggleSectionBookmark()">
      <ion-icon [name]="isSectionBookmarked ? 'bookmark' : 'bookmark-outline'"></ion-icon>
    </button>

    <button class="action-btn music-btn" (click)="openMusicPlayer()">
      <ion-icon name="musical-notes"></ion-icon>
      <span class="action-text">Music</span>
    </button>
  </div>

  <!-- Header -->
  <div class="section-header">
    <div class="header-content">
      <h2 class="section-title">
        <ion-icon name="people" class="title-icon"></ion-icon>
        Suggested for you
      </h2>
      <p class="section-subtitle">Discover amazing fashion creators</p>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-grid">
      <div *ngFor="let item of [1,2,3,4]" class="loading-user-card">
        <div class="loading-avatar"></div>
        <div class="loading-content">
          <div class="loading-line short"></div>
          <div class="loading-line medium"></div>
          <div class="loading-line long"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <ion-icon name="alert-circle" class="error-icon"></ion-icon>
    <p class="error-message">{{ error }}</p>
    <button class="retry-btn" (click)="onRetry()">
      <ion-icon name="refresh"></ion-icon>
      Try Again
    </button>
  </div>

  <!-- Users Slider -->
  <div *ngIf="!isLoading && !error && suggestedUsers.length > 0" class="users-slider-container">
    <!-- Navigation Buttons -->
    <button class="slider-nav prev-btn" (click)="slidePrev()" [disabled]="currentSlide === 0">
      <ion-icon name="chevron-back"></ion-icon>
    </button>
    <button class="slider-nav next-btn" (click)="slideNext()" [disabled]="currentSlide >= maxSlide">
      <ion-icon name="chevron-forward"></ion-icon>
    </button>
    
    <!-- Slider Wrapper -->
    <div class="users-slider-wrapper" (mouseenter)="pauseAutoSlide()" (mouseleave)="resumeAutoSlide()">
      <div class="users-slider" [style.transform]="'translateX(' + slideOffset + 'px)'">
        <div 
          *ngFor="let user of suggestedUsers; trackBy: trackByUserId" 
          class="user-card"
          (click)="onUserClick(user)"
        >
          <!-- User Avatar -->
          <div class="user-avatar-container">
            <img 
              [src]="user.avatar"
              [alt]="user.fullName"
              class="user-avatar"
              loading="lazy"
            />
            <div *ngIf="user.isInfluencer" class="influencer-badge">
              <ion-icon name="star"></ion-icon>
            </div>
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h3 class="user-name">{{ user.fullName }}</h3>
            <p class="username">&#64;{{ user.username }}</p>
            <p class="follower-count">{{ formatFollowerCount(user.followerCount) }} followers</p>
            <p class="category-tag">{{ user.category }}</p>
            <p class="followed-by">{{ user.followedBy }}</p>
          </div>

          <!-- Follow Button -->
          <button 
            class="follow-btn"
            [class.following]="user.isFollowing"
            (click)="onFollowUser(user, $event)"
          >
            <span>{{ user.isFollowing ? 'Following' : 'Follow' }}</span>
            <ion-icon [name]="user.isFollowing ? 'checkmark' : 'add'"></ion-icon>
          </button>
        </div>
      </div>
    </div> <!-- End users-slider-wrapper -->
  </div> <!-- End users-slider-container -->

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && suggestedUsers.length === 0" class="empty-container">
    <ion-icon name="people-outline" class="empty-icon"></ion-icon>
    <h3 class="empty-title">No Suggestions</h3>
    <p class="empty-message">Check back later for user suggestions</p>
  </div>
</div>
