{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction NewArrivalsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 15);\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 18);\n    i0.ɵɵelementStart(7, \"span\", 16);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 20);\n    i0.ɵɵelementStart(11, \"span\", 21);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 24);\n    i0.ɵɵelementStart(17, \"span\", 21);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵelement(3, \"div\", 31)(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_12_div_2_Template, 6, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction NewArrivalsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"ion-icon\", 40);\n    i0.ɵɵelementStart(2, \"h3\", 41);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 42);\n    i0.ɵɵtext(5, \"New arrivals will appear here when available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_15_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r6), \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_15_div_7_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction NewArrivalsComponent_div_15_div_7_ion_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 15);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_15_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 52);\n    i0.ɵɵelement(2, \"img\", 53);\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵelement(4, \"ion-icon\", 55);\n    i0.ɵɵtext(5, \" New \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_15_div_7_div_8_Template, 2, 1, \"div\", 57);\n    i0.ɵɵelementStart(9, \"div\", 58)(10, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_div_7_Template_button_click_10_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_div_7_Template_button_click_12_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(13, \"ion-icon\", 59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 60)(15, \"div\", 61);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h3\", 62);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 63)(20, \"span\", 64);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_15_div_7_span_22_Template, 2, 1, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 66)(24, \"div\", 67);\n    i0.ɵɵtemplate(25, NewArrivalsComponent_div_15_div_7_ion_icon_25_Template, 1, 3, \"ion-icon\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 69);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 70)(29, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_div_7_Template_button_click_29_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r6, $event));\n    });\n    i0.ɵɵelement(30, \"ion-icon\", 72);\n    i0.ɵɵtext(31, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_div_7_Template_button_click_32_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r6, $event));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 74);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.cardWidth, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDaysAgo(product_r6.createdAt), \" days ago \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r6) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(17, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_15_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"ion-icon\", 78);\n    i0.ɵɵelementStart(2, \"h3\", 41);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 42);\n    i0.ɵɵtext(5, \"Check back soon for fresh new styles\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"mouseenter\", function NewArrivalsComponent_div_15_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function NewArrivalsComponent_div_15_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevSlide());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_15_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextSlide());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47)(6, \"div\", 48);\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_15_div_7_Template, 34, 18, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_15_div_8_Template, 6, 0, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoPrev);\n    i0.ɵɵattribute(\"aria-label\", \"Previous products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoNext);\n    i0.ɵɵattribute(\"aria-label\", \"Next products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(-\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && !ctx_r1.error && ctx_r1.newArrivals.length === 0);\n  }\n}\nexport class NewArrivalsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.newArrivals = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280;\n    this.visibleCards = 4;\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 421;\n    this.sectionComments = 156;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeNewArrivals() {\n    this.subscription.add(this.trendingService.newArrivals$.subscribe(products => {\n      this.newArrivals = products;\n      this.isLoading = false;\n      this.calculateMaxSlide();\n      this.currentSlide = 0;\n      this.updateSlidePosition();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadNewArrivals() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadNewArrivals(1, 6);\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n        _this.error = 'Failed to load new arrivals';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  getDaysAgo(createdAt) {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n  onRetry() {\n    this.loadNewArrivals();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'new-arrivals'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider methods\n  initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n  updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n  calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n  updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n  startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n  get canGoPrev() {\n    return this.currentSlide > 0;\n  }\n  get canGoNext() {\n    return this.currentSlide < this.maxSlide;\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for new arrivals section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'New Arrivals',\n        text: 'Check out these fresh new fashion arrivals!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for new arrivals');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function NewArrivalsComponent_Factory(t) {\n      return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewArrivalsComponent,\n      selectors: [[\"app-new-arrivals\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 5,\n      consts: [[1, \"new-arrivals-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"sparkles\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"empty-state\"], [\"name\", \"bag-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"width\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [\"name\", \"sparkles\"], [1, \"days-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"sparkles-outline\", 1, \"empty-icon\"]],\n      template: function NewArrivalsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NewArrivalsComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" New Arrivals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Fresh styles just landed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_9_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(10, \" View All \");\n          i0.ɵɵelement(11, \"ion-icon\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NewArrivalsComponent_div_12_Template, 3, 2, \"div\", 9)(13, NewArrivalsComponent_div_13_Template, 7, 1, \"div\", 10)(14, NewArrivalsComponent_div_14_Template, 6, 0, \"div\", 11)(15, NewArrivalsComponent_div_15_Template, 9, 9, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon],\n      styles: [\".new-arrivals-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 10px;\\n}\\n\\n.products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50px;\\n  left: 12px;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.15);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (min-width: 769px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(25% - 15px);\\n    width: calc(25% - 15px);\\n    max-width: 280px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(20% - 16px);\\n    width: calc(20% - 16px);\\n    max-width: 260px;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(16.666% - 17px);\\n    width: calc(16.666% - 17px);\\n    max-width: 240px;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(14.285% - 18px);\\n    width: calc(14.285% - 18px);\\n    max-width: 220px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 5px;\\n  }\\n  .products-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 220px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-state[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelementStart", "ɵɵlistener", "NewArrivalsComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "NewArrivalsComponent_div_1_Template_button_click_5_listener", "openComments", "NewArrivalsComponent_div_1_Template_button_click_9_listener", "shareSection", "NewArrivalsComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "NewArrivalsComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "NewArrivalsComponent_div_12_div_2_Template", "ɵɵpureFunction0", "_c0", "NewArrivalsComponent_div_13_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r6", "formatPrice", "originalPrice", "star_r7", "rating", "average", "NewArrivalsComponent_div_15_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onProductClick", "NewArrivalsComponent_div_15_div_7_div_8_Template", "NewArrivalsComponent_div_15_div_7_Template_button_click_10_listener", "$event", "onLikeProduct", "NewArrivalsComponent_div_15_div_7_Template_button_click_12_listener", "onShareProduct", "NewArrivalsComponent_div_15_div_7_span_22_Template", "NewArrivalsComponent_div_15_div_7_ion_icon_25_Template", "NewArrivalsComponent_div_15_div_7_Template_button_click_29_listener", "onAddToCart", "NewArrivalsComponent_div_15_div_7_Template_button_click_32_listener", "onAddToWishlist", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON>", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "getDaysAgo", "createdAt", "isProductLiked", "_id", "brand", "price", "_c1", "count", "NewArrivalsComponent_div_15_Template_div_mouseenter_0_listener", "_r4", "pauseAutoSlide", "NewArrivalsComponent_div_15_Template_div_mouseleave_0_listener", "resumeAutoSlide", "NewArrivalsComponent_div_15_Template_button_click_1_listener", "prevSlide", "NewArrivalsComponent_div_15_Template_button_click_3_listener", "nextSlide", "NewArrivalsComponent_div_15_div_7_Template", "NewArrivalsComponent_div_15_div_8_Template", "canGoPrev", "canGoNext", "slideOffset", "newArrivals", "trackByProductId", "isLoading", "length", "NewArrivalsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "likedProducts", "Set", "subscription", "currentSlide", "visibleCards", "maxSlide", "autoSlideDelay", "isMobile", "ngOnInit", "loadNewArrivals", "subscribeNewArrivals", "subscribeLikedProducts", "initializeSlider", "startAutoSlide", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "newArrivals$", "subscribe", "products", "calculateMaxSlide", "updateSlidePosition", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "now", "Date", "created", "diffTime", "abs", "getTime", "diffDays", "ceil", "onViewAll", "queryParams", "filter", "productId", "has", "index", "updateResponsiveSettings", "addEventListener", "containerWidth", "innerWidth", "max", "autoSlideInterval", "setInterval", "clearInterval", "share", "title", "text", "href", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NewArrivalsComponent_Template", "rf", "ctx", "NewArrivalsComponent_div_1_Template", "NewArrivalsComponent_Template_button_click_9_listener", "NewArrivalsComponent_div_12_Template", "NewArrivalsComponent_div_13_Template", "NewArrivalsComponent_div_14_Template", "NewArrivalsComponent_div_15_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\n\n@Component({\n  selector: 'app-new-arrivals',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './new-arrivals.component.html',\n  styleUrls: ['./new-arrivals.component.scss']\n})\nexport class NewArrivalsComponent implements OnInit, OnDestroy {\n  newArrivals: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 280;\n  visibleCards = 4;\n  maxSlide = 0;\n  autoSlideInterval: any;\n  autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n\n  // Section interaction properties\n  isSectionLiked = false;\n  isSectionBookmarked = false;\n  sectionLikes = 421;\n  sectionComments = 156;\n  isMobile = false;\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n    this.checkMobileDevice();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private subscribeNewArrivals() {\n    this.subscription.add(\n      this.trendingService.newArrivals$.subscribe(products => {\n        this.newArrivals = products;\n        this.isLoading = false;\n        this.calculateMaxSlide();\n        this.currentSlide = 0;\n        this.updateSlidePosition();\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadNewArrivals() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadNewArrivals(1, 6);\n    } catch (error) {\n      console.error('Error loading new arrivals:', error);\n      this.error = 'Failed to load new arrivals';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  getDaysAgo(createdAt: Date): number {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n\n  onRetry() {\n    this.loadNewArrivals();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], { \n      queryParams: { filter: 'new-arrivals' } \n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Slider methods\n  private initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n\n  private updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n\n  private calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n\n  private updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n\n  private startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n\n  get canGoPrev(): boolean {\n    return this.currentSlide > 0;\n  }\n\n  get canGoNext(): boolean {\n    return this.currentSlide < this.maxSlide;\n  }\n\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n\n  openComments() {\n    console.log('Opening comments for new arrivals section');\n  }\n\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'New Arrivals',\n        text: 'Check out these fresh new fashion arrivals!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n\n  openMusicPlayer() {\n    console.log('Opening music player for new arrivals');\n  }\n\n  formatCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  private checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n\n\n}\n", "<div class=\"new-arrivals-container\">\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"sparkles\" class=\"title-icon\"></ion-icon>\n        New Arrivals\n      </h2>\n      <p class=\"section-subtitle\">Fresh styles just landed</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- No Data State -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length === 0\" class=\"empty-state\">\n    <ion-icon name=\"bag-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No New Arrivals</h3>\n    <p class=\"empty-message\">New arrivals will appear here when available</p>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length > 0\" class=\"products-slider-container\"\n       (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n\n    <!-- Navigation Buttons -->\n    <button class=\"nav-btn prev-btn\"\n            [disabled]=\"!canGoPrev\"\n            (click)=\"prevSlide()\"\n            [attr.aria-label]=\"'Previous products'\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n\n    <button class=\"nav-btn next-btn\"\n            [disabled]=\"!canGoNext\"\n            (click)=\"nextSlide()\"\n            [attr.aria-label]=\"'Next products'\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <div class=\"products-slider-wrapper\">\n      <div class=\"products-slider\"\n           [style.transform]=\"'translateX(-' + slideOffset + 'px)'\">\n        <div\n          *ngFor=\"let product of newArrivals; trackBy: trackByProductId\"\n          class=\"product-card\"\n          [style.width.px]=\"cardWidth\"\n          (click)=\"onProductClick(product)\"\n        >\n      <!-- Product Image -->\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n        <!-- New Badge -->\n        <div class=\"new-badge\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n          New\n        </div>\n\n        <!-- Days Badge -->\n        <div class=\"days-badge\">\n          {{ getDaysAgo(product.createdAt) }} days ago\n        </div>\n\n        <!-- Discount Badge -->\n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n        <!-- Price Section -->\n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n        <!-- Rating -->\n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"sparkles-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No New Arrivals</h3>\n    <p class=\"empty-message\">Check back soon for fresh new styles</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICNxCC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,4DAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,4DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,6DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,6DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA2BxE7B,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAU,SAAA,cAAiC;IACjCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,0CAAA,kBAA6D;IASjE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAY1CjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,6DAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAQtCrC,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAU,SAAA,mBAA2D;IAC3DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,mDAA4C;IACvEX,EADuE,CAAAY,YAAA,EAAI,EACrE;;;;;IAmDAZ,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IADJZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BExC,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IApEvE7C,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,gEAAA;MAAA,MAAAN,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2C,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IAGrCxC,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAGFV,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAU,SAAA,mBAAqC;IACrCV,EAAA,CAAAW,MAAA,YACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAoB,gDAAA,kBAAuE;IAMrElD,EADF,CAAAC,cAAA,cAA4B,kBAMzB;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,oEAAAC,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAb,UAAA,EAAAY,MAAA,CAA8B;IAAA,EAAC;IAGxCpD,EAAA,CAAAU,SAAA,oBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAoD,oEAAAF,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAf,UAAA,EAAAY,MAAA,CAA+B;IAAA,EAAC;IAGzCpD,EAAA,CAAAU,SAAA,oBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACpDZ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAI9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAA0B,kDAAA,mBAC6B;IAC/BxD,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA2B,sDAAA,uBAIC;IACHzD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IACxDX,EADwD,CAAAY,YAAA,EAAO,EACzD;IAIJZ,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAE,UAAA,mBAAAwD,oEAAAN,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,WAAA,CAAAnB,UAAA,EAAAY,MAAA,CAA4B;IAAA,EAAC;IAEtCpD,EAAA,CAAAU,SAAA,oBAA4C;IAC5CV,EAAA,CAAAW,MAAA,qBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAE,UAAA,mBAAA0D,oEAAAR,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,eAAA,CAAArB,UAAA,EAAAY,MAAA,CAAgC;IAAA,EAAC;IAE1CpD,EAAA,CAAAU,SAAA,oBAA0C;IAIlDV,EAHM,CAAAY,YAAA,EAAS,EACL,EACF,EACF;;;;;IAzFAZ,EAAA,CAAA8D,WAAA,UAAAxD,MAAA,CAAAyD,SAAA,OAA4B;IAM5B/D,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAgB,UAAA,CAAAwB,MAAA,IAAAC,GAAA,EAAAjE,EAAA,CAAAkE,aAAA,CAA6B,QAAA1B,UAAA,CAAAwB,MAAA,IAAAG,GAAA,IAAA3B,UAAA,CAAA4B,IAAA,CACgB;IAa7CpE,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAA+D,UAAA,CAAA7B,UAAA,CAAA8B,SAAA,gBACF;IAGMtE,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CxC,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,EAA2C;;IAIjCxE,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,8BAAgE;IAK1ExE,EAAA,CAAAqB,SAAA,EAA2C;;IASpBrB,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAAiC,KAAA,CAAmB;IACrBzE,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAA4B,IAAA,CAAkB;IAIbpE,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAkC,KAAA,EAAgC;IACrD1E,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAAkC,KAAA,CAAoE;IAQtD1E,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAA2C,GAAA,EAAc;IAKT3E,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAsC,kBAAA,MAAAE,UAAA,CAAAI,MAAA,CAAAgC,KAAA,MAA4B;;;;;IAwB9D5E,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAU,SAAA,mBAAgE;IAChEV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,2CAAoC;IAC/DX,EAD+D,CAAAY,YAAA,EAAI,EAC7D;;;;;;IAzHNZ,EAAA,CAAAC,cAAA,cACsE;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA2E,+DAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAyE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,+DAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2E,eAAA,EAAiB;IAAA,EAAC;IAGnEjF,EAAA,CAAAC,cAAA,iBAGgD;IADxCD,EAAA,CAAAE,UAAA,mBAAAgF,6DAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6E,SAAA,EAAW;IAAA,EAAC;IAE3BnF,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,iBAG4C;IADpCD,EAAA,CAAAE,UAAA,mBAAAkF,6DAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,SAAA,EAAW;IAAA,EAAC;IAE3BrF,EAAA,CAAAU,SAAA,kBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGPZ,EADF,CAAAC,cAAA,cAAqC,cAE2B;IAC5DD,EAAA,CAAA8B,UAAA,IAAAwD,0CAAA,oBAKC;IAwFPtF,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAyD,0CAAA,kBAAsF;IAKxFvF,EAAA,CAAAY,YAAA,EAAM,EAzHkE;;;;IAI5DZ,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAkF,SAAA,CAAuB;;IAOvBxF,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAmF,SAAA,CAAuB;;IAQxBzF,EAAA,CAAAqB,SAAA,GAAwD;IAAxDrB,EAAA,CAAA8D,WAAA,+BAAAxD,MAAA,CAAAoF,WAAA,SAAwD;IAErC1F,EAAA,CAAAqB,SAAA,EAAgB;IAAArB,EAAhB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAqF,WAAA,CAAgB,iBAAArF,MAAA,CAAAsF,gBAAA,CAAyB;IA+F/D5F,EAAA,CAAAqB,SAAA,EAAsD;IAAtDrB,EAAA,CAAAwB,UAAA,UAAAlB,MAAA,CAAAuF,SAAA,KAAAvF,MAAA,CAAA+B,KAAA,IAAA/B,MAAA,CAAAqF,WAAA,CAAAG,MAAA,OAAsD;;;ADlL9D,OAAM,MAAOC,oBAAoB;EAuB/BC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAAV,WAAW,GAAc,EAAE;IAC3B,KAAAE,SAAS,GAAG,IAAI;IAChB,KAAAxD,KAAK,GAAkB,IAAI;IAC3B,KAAAiE,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI1G,YAAY,EAAE;IAEvD;IACA,KAAA2G,YAAY,GAAG,CAAC;IAChB,KAAAf,WAAW,GAAG,CAAC;IACf,KAAA3B,SAAS,GAAG,GAAG;IACf,KAAA2C,YAAY,GAAG,CAAC;IAChB,KAAAC,QAAQ,GAAG,CAAC;IAEZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IAEvB;IACA,KAAArF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAAiF,QAAQ,GAAG,KAAK;EAQb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,YAAY,CAACc,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQP,oBAAoBA,CAAA;IAC1B,IAAI,CAACR,YAAY,CAACgB,GAAG,CACnB,IAAI,CAACvB,eAAe,CAACwB,YAAY,CAACC,SAAS,CAACC,QAAQ,IAAG;MACrD,IAAI,CAAChC,WAAW,GAAGgC,QAAQ;MAC3B,IAAI,CAAC9B,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC+B,iBAAiB,EAAE;MACxB,IAAI,CAACnB,YAAY,GAAG,CAAC;MACrB,IAAI,CAACoB,mBAAmB,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQZ,sBAAsBA,CAAA;IAC5B,IAAI,CAACT,YAAY,CAACgB,GAAG,CACnB,IAAI,CAACtB,aAAa,CAAC4B,cAAc,CAACJ,SAAS,CAACpB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcS,eAAeA,CAAA;IAAA,IAAAgB,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI;QACFD,KAAI,CAAClC,SAAS,GAAG,IAAI;QACrBkC,KAAI,CAAC1F,KAAK,GAAG,IAAI;QACjB,MAAM0F,KAAI,CAAC9B,eAAe,CAACc,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;OACjD,CAAC,OAAO1E,KAAK,EAAE;QACd4F,OAAO,CAAC5F,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD0F,KAAI,CAAC1F,KAAK,GAAG,6BAA6B;QAC1C0F,KAAI,CAAClC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA5C,cAAcA,CAACiF,OAAgB;IAC7B,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC1D,GAAG,CAAC,CAAC;EACjD;EAEMnB,aAAaA,CAAC6E,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAACnC,aAAa,CAACsC,WAAW,CAACN,OAAO,CAAC1D,GAAG,CAAC;QAChE,IAAI+D,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAAC5F,KAAK,CAAC,yBAAyB,EAAEkG,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOtG,KAAK,EAAE;QACd4F,OAAO,CAAC5F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMkB,cAAcA,CAAC2E,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAAC1D,GAAG,EAAE;QACrE,MAAMyE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC1C,aAAa,CAACkD,YAAY,CAAClB,OAAO,CAAC1D,GAAG,EAAE;UACjD6E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,iCAAiCT,OAAO,CAAC9D,IAAI,SAAS8D,OAAO,CAACzD,KAAK;SAC7E,CAAC;QAEFwD,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOrG,KAAK,EAAE;QACd4F,OAAO,CAAC5F,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMsB,WAAWA,CAACuE,OAAgB,EAAEE,KAAY;IAAA,IAAAkB,MAAA;IAAA,OAAAtB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMgB,MAAI,CAACnD,WAAW,CAACoD,SAAS,CAACrB,OAAO,CAAC1D,GAAG,EAAE,CAAC,CAAC;QAChDyD,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOrG,KAAK,EAAE;QACd4F,OAAO,CAAC5F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMwB,eAAeA,CAACqE,OAAgB,EAAEE,KAAY;IAAA,IAAAoB,MAAA;IAAA,OAAAxB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMkB,MAAI,CAACpD,eAAe,CAACqD,aAAa,CAACvB,OAAO,CAAC1D,GAAG,CAAC;QACrDyD,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOrG,KAAK,EAAE;QACd4F,OAAO,CAAC5F,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC2F,OAAgB;IACpC,IAAIA,OAAO,CAACxF,aAAa,IAAIwF,OAAO,CAACxF,aAAa,GAAGwF,OAAO,CAACxD,KAAK,EAAE;MAClE,OAAOgF,IAAI,CAACC,KAAK,CAAE,CAACzB,OAAO,CAACxF,aAAa,GAAGwF,OAAO,CAACxD,KAAK,IAAIwD,OAAO,CAACxF,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAACiC,KAAa;IACvB,OAAO,IAAIkF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAC;EAClB;EAEAL,UAAUA,CAACC,SAAe;IACxB,MAAM4F,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAAC7F,SAAS,CAAC;IACnC,MAAM+F,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,OAAO,CAACG,OAAO,EAAE,CAAC;IAC5D,MAAMC,QAAQ,GAAGd,IAAI,CAACe,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB;EAEApI,OAAOA,CAAA;IACL,IAAI,CAAC2E,eAAe,EAAE;EACxB;EAEA2D,SAASA,CAAA;IACP,IAAI,CAACrE,MAAM,CAAC8B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCwC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAc;KACtC,CAAC;EACJ;EAEArG,cAAcA,CAACsG,SAAiB;IAC9B,OAAO,IAAI,CAACvE,aAAa,CAACwE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAjF,gBAAgBA,CAACmF,KAAa,EAAE7C,OAAgB;IAC9C,OAAOA,OAAO,CAAC1D,GAAG;EACpB;EAEA;EACQ0C,gBAAgBA,CAAA;IACtB,IAAI,CAAC8D,wBAAwB,EAAE;IAC/B,IAAI,CAACpD,iBAAiB,EAAE;IACxBkB,MAAM,CAACmC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAACD,wBAAwB,EAAE,CAAC;EAC1E;EAEQA,wBAAwBA,CAAA;IAC9B,MAAME,cAAc,GAAGpC,MAAM,CAACqC,UAAU;IAExC,IAAID,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAImH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAImH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAAC2C,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;;IAGtB,IAAI,CAAC6D,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACjB,QAAQ,GAAG+C,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzF,WAAW,CAACG,MAAM,GAAG,IAAI,CAACY,YAAY,CAAC;EAC1E;EAEQmB,mBAAmBA,CAAA;IACzB,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACe,YAAY,IAAI,IAAI,CAAC1C,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;EAChE;EAEAsB,SAASA,CAAA;IACP,IAAI,IAAI,CAACoB,YAAY,GAAG,IAAI,CAACE,QAAQ,EAAE;MACrC,IAAI,CAACF,YAAY,EAAE;MACnB,IAAI,CAACoB,mBAAmB,EAAE;;EAE9B;EAEA1C,SAASA,CAAA;IACP,IAAI,IAAI,CAACsB,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACoB,mBAAmB,EAAE;;EAE9B;EAEQV,cAAcA,CAAA;IACpB,IAAI,CAACkE,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC7E,YAAY,IAAI,IAAI,CAACE,QAAQ,EAAE;QACtC,IAAI,CAACF,YAAY,GAAG,CAAC;OACtB,MAAM;QACL,IAAI,CAACA,YAAY,EAAE;;MAErB,IAAI,CAACoB,mBAAmB,EAAE;IAC5B,CAAC,EAAE,IAAI,CAACjB,cAAc,CAAC;EACzB;EAEQW,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC8D,iBAAiB,EAAE;MAC1BE,aAAa,CAAC,IAAI,CAACF,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAtG,cAAcA,CAAA;IACZ,IAAI,CAACwC,aAAa,EAAE;EACtB;EAEAtC,eAAeA,CAAA;IACb,IAAI,CAACkC,cAAc,EAAE;EACvB;EAEA,IAAI3B,SAASA,CAAA;IACX,OAAO,IAAI,CAACiB,YAAY,GAAG,CAAC;EAC9B;EAEA,IAAIhB,SAASA,CAAA;IACX,OAAO,IAAI,CAACgB,YAAY,GAAG,IAAI,CAACE,QAAQ;EAC1C;EAEA;EACAlG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACVmH,OAAO,CAACS,GAAG,CAAC,2CAA2C,CAAC;EAC1D;EAEA1H,YAAYA,CAAA;IACV,IAAIiI,SAAS,CAACuC,KAAK,EAAE;MACnBvC,SAAS,CAACuC,KAAK,CAAC;QACdC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,6CAA6C;QACnDzH,GAAG,EAAE6E,MAAM,CAACC,QAAQ,CAAC4C;OACtB,CAAC;KACH,MAAM;MACL1C,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAAC4C,IAAI,CAAC;MACnD1D,OAAO,CAACS,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAtH,eAAeA,CAAA;IACb6G,OAAO,CAACS,GAAG,CAAC,uCAAuC,CAAC;EACtD;EAEAhH,WAAWA,CAACkD,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEgH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAIhH,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEgH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOhH,KAAK,CAACiH,QAAQ,EAAE;EACzB;EAEQzE,iBAAiBA,CAAA;IACvB,IAAI,CAACP,QAAQ,GAAGiC,MAAM,CAACqC,UAAU,IAAI,GAAG;EAC1C;;;uBA/SWpF,oBAAoB,EAAA/F,EAAA,CAAA8L,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAhM,EAAA,CAAA8L,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAlM,EAAA,CAAA8L,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApM,EAAA,CAAA8L,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAtM,EAAA,CAAA8L,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBzG,oBAAoB;MAAA0G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3M,EAAA,CAAA4M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBjClN,EAAA,CAAAC,cAAA,aAAoC;UAElCD,EAAA,CAAA8B,UAAA,IAAAsL,mCAAA,kBAAoD;UAiChDpN,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAwD;UACxDV,EAAA,CAAAW,MAAA,qBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,+BAAwB;UACtDX,EADsD,CAAAY,YAAA,EAAI,EACpD;UACNZ,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAE,UAAA,mBAAAmN,sDAAA;YAAA,OAASF,GAAA,CAAAzC,SAAA,EAAW;UAAA,EAAC;UAChD1K,EAAA,CAAAW,MAAA,kBACA;UAAAX,EAAA,CAAAU,SAAA,mBAA4C;UAEhDV,EADE,CAAAY,YAAA,EAAS,EACL;UAkCNZ,EA/BA,CAAA8B,UAAA,KAAAwL,oCAAA,iBAAiD,KAAAC,oCAAA,kBAcQ,KAAAC,oCAAA,kBAUyB,KAAAC,oCAAA,kBAQZ;UAhFxEzN,EAAA,CAAAY,YAAA,EAAoC;;;UAEEZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAAtG,QAAA,CAAc;UA8C5C7G,EAAA,CAAAqB,SAAA,IAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAAtH,SAAA,CAAe;UAcf7F,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAA9K,KAAA,KAAA8K,GAAA,CAAAtH,SAAA,CAAyB;UAUzB7F,EAAA,CAAAqB,SAAA,EAAsD;UAAtDrB,EAAA,CAAAwB,UAAA,UAAA2L,GAAA,CAAAtH,SAAA,KAAAsH,GAAA,CAAA9K,KAAA,IAAA8K,GAAA,CAAAxH,WAAA,CAAAG,MAAA,OAAsD;UAOtD9F,EAAA,CAAAqB,SAAA,EAAoD;UAApDrB,EAAA,CAAAwB,UAAA,UAAA2L,GAAA,CAAAtH,SAAA,KAAAsH,GAAA,CAAA9K,KAAA,IAAA8K,GAAA,CAAAxH,WAAA,CAAAG,MAAA,KAAoD;;;qBDjEhDjG,YAAY,EAAA6N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7N,WAAW,EAAA8N,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}