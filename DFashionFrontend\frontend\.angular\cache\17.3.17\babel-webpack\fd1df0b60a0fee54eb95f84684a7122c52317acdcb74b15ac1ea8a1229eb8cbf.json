{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nfunction HomeComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"h1\", 14);\n    i0.ɵɵtext(3, \"DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTabMenu());\n    });\n    i0.ɵɵelement(8, \"ion-icon\", 19);\n    i0.ɵɵtemplate(9, HomeComponent_div_1_div_9_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSidebar());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasNotifications);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 36);\n  }\n}\nfunction HomeComponent_div_2_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 37);\n  }\n}\nfunction HomeComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_9_Template_div_click_0_listener() {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewStory(story_r5));\n    })(\"touchstart\", function HomeComponent_div_2_div_9_Template_div_touchstart_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchStart($event, story_r5));\n    })(\"touchend\", function HomeComponent_div_2_div_9_Template_div_touchend_0_listener($event) {\n      const story_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTouchEnd($event, story_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵelement(2, \"img\", 33);\n    i0.ɵɵtemplate(3, HomeComponent_div_2_div_9_div_3_Template, 1, 0, \"div\", 34)(4, HomeComponent_div_2_div_9_div_4_Template, 1, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-story\", story_r5.hasStory)(\"viewed\", story_r5.viewed)(\"touching\", story_r5.touching);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", story_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r5.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r5.hasStory && !story_r5.viewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.username);\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createStory());\n    });\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵelement(4, \"img\", 27);\n    i0.ɵɵelementStart(5, \"div\", 28);\n    i0.ɵɵelement(6, \"ion-icon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 30);\n    i0.ɵɵtext(8, \"Your story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, HomeComponent_div_2_div_9_Template, 7, 11, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.instagramStories)(\"ngForTrackBy\", ctx_r1.trackByStoryId);\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"h3\");\n    i0.ɵɵtext(3, \"Discover\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 42);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTabMenu());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"trending\"));\n    });\n    i0.ɵɵelementStart(7, \"div\", 45);\n    i0.ɵɵelement(8, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 47);\n    i0.ɵɵtext(10, \"Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 48);\n    i0.ɵɵtext(12, \"Hot products right now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"brands\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 49);\n    i0.ɵɵelement(15, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 47);\n    i0.ɵɵtext(17, \"Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 48);\n    i0.ɵɵtext(19, \"Top fashion brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"arrivals\"));\n    });\n    i0.ɵɵelementStart(21, \"div\", 51);\n    i0.ɵɵelement(22, \"ion-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 47);\n    i0.ɵɵtext(24, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 48);\n    i0.ɵɵtext(26, \"Latest arrivals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"suggested\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 53);\n    i0.ɵɵelement(29, \"ion-icon\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 47);\n    i0.ɵɵtext(31, \"For You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 48);\n    i0.ɵɵtext(33, \"Personalized picks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"influencers\"));\n    });\n    i0.ɵɵelementStart(35, \"div\", 55);\n    i0.ɵɵelement(36, \"ion-icon\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 47);\n    i0.ɵɵtext(38, \"Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 48);\n    i0.ɵɵtext(40, \"Top fashion creators\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_10_Template_div_click_41_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openSidebarTab(\"categories\"));\n    });\n    i0.ɵɵelementStart(42, \"div\", 57);\n    i0.ɵɵelement(43, \"ion-icon\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 47);\n    i0.ɵɵtext(45, \"Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 48);\n    i0.ɵɵtext(47, \"Browse by category\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isTabMenuOpen);\n  }\n}\nfunction HomeComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 61)(3, \"div\", 62);\n    i0.ɵɵelement(4, \"img\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 64)(6, \"h3\");\n    i0.ɵɵtext(7, \"Your Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"@username\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"ion-icon\", 42);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_11_Template_ion_icon_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebar());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 65);\n    i0.ɵɵelement(12, \"app-sidebar\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction HomeComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"app-trending-products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"app-featured-brands\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"app-new-arrivals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"app-suggested-for-you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"app-top-fashion-influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_12_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵelement(2, \"ion-icon\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", category_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r11.name);\n  }\n}\nfunction HomeComponent_div_12_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵtemplate(2, HomeComponent_div_12_div_11_div_2_Template, 5, 2, \"div\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction HomeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-icon\", 42);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_12_Template_ion_icon_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeSidebarContent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 68);\n    i0.ɵɵtemplate(6, HomeComponent_div_12_div_6_Template, 2, 0, \"div\", 69)(7, HomeComponent_div_12_div_7_Template, 2, 0, \"div\", 69)(8, HomeComponent_div_12_div_8_Template, 2, 0, \"div\", 69)(9, HomeComponent_div_12_div_9_Template, 2, 0, \"div\", 69)(10, HomeComponent_div_12_div_10_Template, 2, 0, \"div\", 69)(11, HomeComponent_div_12_div_11_Template, 3, 1, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSidebarContentOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentSidebarTitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"trending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"brands\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"arrivals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"suggested\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"influencers\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSidebarTab === \"categories\");\n  }\n}\nfunction HomeComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77);\n    i0.ɵɵelement(2, \"ion-icon\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79);\n    i0.ɵɵelement(4, \"ion-icon\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 79);\n    i0.ɵɵelement(6, \"ion-icon\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 79);\n    i0.ɵɵelement(8, \"ion-icon\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"div\", 83);\n    i0.ɵɵelement(11, \"img\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.isMobile = false;\n    this.isSidebarOpen = false;\n    this.isTabMenuOpen = false;\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.currentSidebarTitle = '';\n    this.hasNotifications = true; // Example notification state\n    this.window = window; // For template access\n    // TikTok-style interaction states\n    this.isLiked = false;\n    // Instagram Stories Data - Enhanced for responsive design and mobile app\n    this.instagramStories = [{\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    }, {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }, {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }];\n    // Categories Data\n    this.categories = [{\n      name: 'Women',\n      icon: 'woman'\n    }, {\n      name: 'Men',\n      icon: 'man'\n    }, {\n      name: 'Kids',\n      icon: 'happy'\n    }, {\n      name: 'Shoes',\n      icon: 'footsteps'\n    }, {\n      name: 'Bags',\n      icon: 'bag'\n    }, {\n      name: 'Accessories',\n      icon: 'watch'\n    }, {\n      name: 'Beauty',\n      icon: 'flower'\n    }, {\n      name: 'Sports',\n      icon: 'fitness'\n    }];\n    this.preventScroll = e => {\n      if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n        e.preventDefault();\n      }\n    };\n  }\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, {\n      passive: false\n    });\n  }\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n  onResize(event) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n  checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n  toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n  openSidebarTab(tabType) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n    // Set title based on tab type\n    const titles = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n  viewStory(story) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n  trackByStoryId(index, story) {\n    return story.id || index;\n  }\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event, story) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n  onStoryTouchEnd(event, story) {\n    story.touching = false;\n  }\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function HomeComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[1, \"home-container\"], [\"class\", \"mobile-header instagram-style\", 4, \"ngIf\"], [\"class\", \"instagram-stories-section\", 4, \"ngIf\"], [1, \"content-grid\"], [1, \"main-content\"], [1, \"desktop-sidebar\"], [\"class\", \"tab-menu-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"sidebar-overlay\", 3, \"active\", \"click\", 4, \"ngIf\"], [\"class\", \"instagram-tab-menu\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"mobile-sidebar\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"sidebar-content-modal\", 3, \"active\", 4, \"ngIf\"], [\"class\", \"instagram-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-header\", \"instagram-style\"], [1, \"header-left\"], [1, \"app-logo\"], [\"name\", \"chevron-down\", 1, \"logo-dropdown\"], [1, \"header-right\"], [\"name\", \"heart-outline\", 1, \"header-icon\"], [1, \"menu-icon-container\", 3, \"click\"], [\"name\", \"grid-outline\", 1, \"header-icon\", \"menu-icon\"], [\"class\", \"notification-dot\", 4, \"ngIf\"], [\"name\", \"menu-outline\", 1, \"header-icon\", \"menu-icon\"], [1, \"notification-dot\"], [1, \"instagram-stories-section\"], [1, \"stories-container\"], [1, \"story-item\", \"your-story\", 3, \"click\"], [1, \"story-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Your story\"], [1, \"add-story-btn\"], [\"name\", \"add\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", \"touchstart\", \"touchend\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"story-item\", 3, \"click\", \"touchstart\", \"touchend\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [\"class\", \"story-ring\", 4, \"ngIf\"], [\"class\", \"story-gradient-ring\", 4, \"ngIf\"], [1, \"story-ring\"], [1, \"story-gradient-ring\"], [1, \"tab-menu-overlay\", 3, \"click\"], [1, \"sidebar-overlay\", 3, \"click\"], [1, \"instagram-tab-menu\"], [1, \"tab-menu-header\"], [\"name\", \"close-outline\", 1, \"close-icon\", 3, \"click\"], [1, \"tab-menu-grid\"], [1, \"tab-item\", 3, \"click\"], [1, \"tab-icon\", \"trending\"], [\"name\", \"trending-up\"], [1, \"tab-label\"], [1, \"tab-tooltip\"], [1, \"tab-icon\", \"brands\"], [\"name\", \"diamond\"], [1, \"tab-icon\", \"arrivals\"], [\"name\", \"sparkles\"], [1, \"tab-icon\", \"suggested\"], [\"name\", \"heart\"], [1, \"tab-icon\", \"influencers\"], [\"name\", \"people\"], [1, \"tab-icon\", \"categories\"], [\"name\", \"grid\"], [1, \"mobile-sidebar\"], [1, \"sidebar-header\"], [1, \"user-profile\"], [1, \"profile-avatar\"], [\"src\", \"assets/images/default-avatar.svg\", \"alt\", \"Profile\"], [1, \"profile-info\"], [1, \"sidebar-content\"], [1, \"sidebar-content-modal\"], [1, \"modal-header\"], [1, \"modal-content\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\"], [1, \"category-icon\"], [3, \"name\"], [1, \"instagram-bottom-nav\"], [1, \"nav-item\", \"active\"], [\"name\", \"home\"], [1, \"nav-item\"], [\"name\", \"search\"], [\"name\", \"add-circle-outline\"], [\"name\", \"play-circle-outline\"], [1, \"profile-avatar-nav\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 12, 1, \"div\", 1)(2, HomeComponent_div_2_Template, 10, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-view-add-stories\")(6, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"app-sidebar\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HomeComponent_div_8_Template, 1, 2, \"div\", 6)(9, HomeComponent_div_9_Template, 1, 2, \"div\", 7)(10, HomeComponent_div_10_Template, 48, 2, \"div\", 8)(11, HomeComponent_div_11_Template, 13, 2, \"div\", 9)(12, HomeComponent_div_12_Template, 12, 9, \"div\", 10)(13, HomeComponent_div_13_Template, 12, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mobile-instagram\", ctx.isMobile)(\"sidebar-open\", ctx.isSidebarOpen)(\"mobile\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IonicModule, i2.IonIcon, ViewAddStoriesComponent, FeedComponent, SidebarComponent, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n  position: relative;\\n  background: #ffffff;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  color: #262626 !important;\\n  padding: 0 !important;\\n  min-height: 100vh !important;\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 60px;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 1001; \\n\\n  padding: 0 16px;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.mobile-header.instagram-style[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 400;\\n  color: #262626;\\n  margin: 0;\\n  font-family: \\\"Billabong\\\", cursive, -apple-system, BlinkMacSystemFont, sans-serif;\\n  letter-spacing: 0.5px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #262626;\\n  margin-top: 2px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  cursor: pointer;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  width: 8px;\\n  height: 8px;\\n  background: #ff3040;\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n\\n.instagram-stories-section[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  top: 60px;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-bottom: 1px solid #dbdbdb;\\n  z-index: 999;\\n  padding: 12px 0;\\n  height: 100px; \\n\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  \\n\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  overflow-y: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: 100%;\\n  align-items: center;\\n  min-width: max-content;\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 998; \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  overscroll-behavior-x: contain;\\n  scroll-snap-type: x proximity;\\n  \\n\\n  will-change: scroll-position;\\n  transform: translateZ(0); \\n\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 10px;\\n    scroll-snap-type: x mandatory;\\n    \\n\\n    touch-action: pan-x;\\n    -webkit-overflow-scrolling: touch;\\n    overscroll-behavior-x: contain;\\n    \\n\\n    contain: layout style paint;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 8px;\\n    \\n\\n    scroll-padding-left: 8px;\\n    scroll-padding-right: 8px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 6px;\\n    gap: 6px;\\n    \\n\\n    scroll-padding-left: 6px;\\n    scroll-padding-right: 6px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 6px;\\n  min-width: 70px;\\n  max-width: 70px;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n  scroll-snap-align: start;\\n  scroll-snap-stop: normal;\\n  position: relative;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n    max-width: 65px;\\n    gap: 5px;\\n    \\n\\n    padding: 4px;\\n    margin: -4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n    max-width: 60px;\\n    gap: 4px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 55px;\\n    max-width: 55px;\\n    gap: 3px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    min-width: 50px;\\n    max-width: 50px;\\n    gap: 2px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border: 2px solid #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: white;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n  position: relative;\\n  flex-shrink: 0;\\n  transition: all 0.2s ease;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%] {\\n  border: 2px solid transparent;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  padding: 2px;\\n  \\n\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed) {\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%] {\\n  border: 2px solid #c7c7c7;\\n  background: #c7c7c7;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_storyTouchFeedback 0.2s ease;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n  opacity: 0.8;\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -2;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_storyRingGradient 3s infinite;\\n  filter: blur(2px);\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  display: block;\\n  transition: transform 0.2s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 55px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n  font-weight: 400;\\n  margin-top: 4px;\\n  \\n\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  text-rendering: optimizeLegibility;\\n  \\n\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 65px;\\n    font-weight: 500; \\n\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 480px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 60px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 400px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    max-width: 55px;\\n    line-height: 1.1;\\n  }\\n}\\n@media (max-width: 320px) {\\n  .instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 7px;\\n    max-width: 50px;\\n    line-height: 1;\\n    font-weight: 600; \\n\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0.7);\\n  }\\n  70% {\\n    transform: scale(1.05) translateZ(0);\\n    box-shadow: 0 0 0 10px rgba(240, 148, 51, 0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyRingGradient {\\n  0% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  25% {\\n    background: linear-gradient(90deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  50% {\\n    background: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  75% {\\n    background: linear-gradient(180deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n  100% {\\n    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyTouchFeedback {\\n  0% {\\n    transform: scale(1) translateZ(0);\\n  }\\n  50% {\\n    transform: scale(0.95) translateZ(0);\\n  }\\n  100% {\\n    transform: scale(1) translateZ(0);\\n  }\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #ffffff;\\n  border-top: 1px solid #dbdbdb;\\n  justify-content: space-around;\\n  align-items: center;\\n  padding: 8px 0;\\n  z-index: 1000;\\n  height: 60px;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);\\n  padding-bottom: max(8px, env(safe-area-inset-bottom));\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  min-width: 44px;\\n  min-height: 44px;\\n  position: relative;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #262626;\\n  transform: scale(1.1);\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  transition: all 0.2s ease;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 1px solid #8e8e8e;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%] {\\n  border: 2px solid #262626;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 2px;\\n  right: 2px;\\n  background: #ff3040;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.tab-menu-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1500;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.tab-menu-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.instagram-tab-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: #000000;\\n  border-top-left-radius: 20px;\\n  border-top-right-radius: 20px;\\n  z-index: 1600;\\n  transform: translateY(100%);\\n  transition: transform 0.3s ease;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n.instagram-tab-menu.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px 16px;\\n  border-bottom: 1px solid #262626;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 24px;\\n  padding: 24px;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  padding: 16px;\\n  border-radius: 16px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  transform: scale(1.05);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4, #44a08d);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea, #fed6e3);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  text-align: center;\\n  line-height: 1.3;\\n}\\n\\n.sidebar-content-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: #000000;\\n  z-index: 1700;\\n  transform: translateX(100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n}\\n.sidebar-content-modal.active[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background: #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #262626;\\n  z-index: 10;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  background: #000000;\\n  color: white;\\n  min-height: calc(100vh - 80px);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     * {\\n  background-color: transparent !important;\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item {\\n  background: #1a1a1a !important;\\n  border: 1px solid #262626 !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark {\\n  color: white !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white {\\n  background: #1a1a1a !important;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n  padding: 24px;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 20px;\\n  background: #1a1a1a;\\n  border-radius: 16px;\\n  border: 1px solid #262626;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background: #262626;\\n  transform: scale(1.02);\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  background: #ffffff;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.desktop-sidebar[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.sidebar-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.65);\\n  z-index: 200;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.sidebar-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.mobile-sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -100%;\\n  width: 85%;\\n  max-width: 400px;\\n  height: 100%;\\n  background: #ffffff;\\n  z-index: 300;\\n  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.mobile-sidebar.active[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 16px;\\n  border-bottom: 1px solid #dbdbdb;\\n  background: #fafafa;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid #dbdbdb;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #262626;\\n  cursor: pointer;\\n  padding: 8px;\\n  margin: -8px;\\n  transition: color 0.2s ease;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px 0;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 100%;\\n    padding: 0 16px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    max-width: 768px;\\n    margin: 0 auto;\\n    padding: 0 24px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    padding-top: 80px;\\n    \\n\\n  }\\n}\\n@media (max-width: 1024px) and (min-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 80px 0 0 0;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  min-height: 100vh !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n  display: block !important;\\n  visibility: visible !important;\\n  opacity: 1 !important;\\n  width: 100% !important;\\n  height: 100px !important;\\n  padding: 8px 0 !important;\\n  background: #ffffff !important;\\n  border-bottom: 1px solid #dbdbdb !important;\\n  \\n\\n  transform: translateZ(0) !important;\\n  will-change: scroll-position !important;\\n  contain: layout style paint !important;\\n  \\n\\n  backdrop-filter: blur(10px) !important;\\n  -webkit-backdrop-filter: blur(10px) !important;\\n}\\n@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95) !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n  \\n\\n  grid-template-columns: 1fr !important;\\n  padding: 160px 0 60px 0 !important; \\n\\n  background: #ffffff !important;\\n  gap: 0 !important;\\n  margin: 0 !important;\\n  max-width: 100% !important;\\n  min-height: calc(100vh - 220px) !important;\\n  overflow-x: hidden !important;\\n  position: relative !important;\\n  z-index: 1 !important; \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n@media (min-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 16px 60px 16px !important; \\n\\n    max-width: 768px !important;\\n    margin: 0 auto !important; \\n\\n    gap: 16px !important;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 300px !important; \\n\\n    padding: 160px 24px 60px 24px !important;\\n    max-width: 1200px !important;\\n    gap: 32px !important;\\n  }\\n}\\n@media (min-width: 1200px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px !important; \\n\\n    padding: 160px 32px 60px 32px !important;\\n    max-width: 1400px !important;\\n    gap: 40px !important;\\n  }\\n}\\n@media (min-width: 1440px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 48px 60px 48px !important;\\n    max-width: 1600px !important;\\n    gap: 48px !important;\\n  }\\n}\\n.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-top: 1px solid #dbdbdb !important;\\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n    padding: 0 !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%] {\\n    background: #fafafa !important;\\n  }\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0;\\n    margin: 0;\\n    background: #fafafa;\\n  }\\n  .content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    background: white;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin: 0;\\n    padding: 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 60px !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    display: block !important;\\n    visibility: visible !important;\\n    opacity: 1 !important;\\n    width: 100% !important;\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n    background: #ffffff !important;\\n    border-bottom: 1px solid #dbdbdb !important;\\n  }\\n  .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    min-height: 100vh;\\n    padding: 0 !important;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 160px 0 60px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff !important;\\n    color: #262626 !important;\\n    gap: 0;\\n    padding: 0;\\n    width: 100% !important;\\n    max-width: 100% !important;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 90%;\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .sidebar-overlay[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.8);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .mobile-sidebar[_ngcontent-%COMP%] {\\n    width: 95%;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 480px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 400px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    overflow-x: hidden !important;\\n  }\\n}\\n@media (max-width: 480px) and (max-width: 360px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 160px 0 60px 0 !important;\\n    min-height: calc(100vh - 220px) !important;\\n  }\\n  .instagram-stories-section[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    padding: 8px 0 !important;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .desktop-sidebar[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .home-container[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    padding: 20px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n    padding: 0;\\n    margin: 0 auto;\\n    \\n\\n    grid-template-columns: 1fr 300px;\\n    gap: 32px;\\n    max-width: 1000px;\\n    \\n\\n    \\n\\n    \\n\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 350px;\\n    gap: 36px;\\n    max-width: 1200px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1200px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 400px;\\n    gap: 40px;\\n    max-width: 1400px;\\n  }\\n}\\n@media (min-width: 769px) and (min-width: 1440px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 450px;\\n    gap: 48px;\\n    max-width: 1600px;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    background: #ffffff;\\n    color: #262626;\\n  }\\n  .instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (min-width: 320px) and (max-width: 768px) {\\n  .home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n    padding: 5px 0 !important;\\n    background: #ffffff !important;\\n    gap: 0 !important;\\n    margin: 0 !important;\\n    max-width: 100% !important;\\n    min-height: calc(100vh - 220px) !important;\\n    overflow-x: hidden !important;\\n    position: relative !important;\\n    z-index: 1 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "ViewAddStoriesComponent", "FeedComponent", "SidebarComponent", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "SuggestedForYouComponent", "TopFashionInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_div_1_Template_div_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleTabMenu", "ɵɵtemplate", "HomeComponent_div_1_div_9_Template", "HomeComponent_div_1_Template_div_click_10_listener", "toggleSidebar", "ɵɵadvance", "ɵɵproperty", "hasNotifications", "HomeComponent_div_2_div_9_Template_div_click_0_listener", "story_r5", "_r4", "$implicit", "viewStory", "HomeComponent_div_2_div_9_Template_div_touchstart_0_listener", "$event", "onStoryTouchStart", "HomeComponent_div_2_div_9_Template_div_touchend_0_listener", "onStoryTouchEnd", "HomeComponent_div_2_div_9_div_3_Template", "HomeComponent_div_2_div_9_div_4_Template", "ɵɵclassProp", "hasStory", "viewed", "touching", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "HomeComponent_div_2_Template_div_click_2_listener", "_r3", "createStory", "HomeComponent_div_2_div_9_Template", "instagramStories", "trackByStoryId", "HomeComponent_div_8_Template_div_click_0_listener", "_r6", "closeTabMenu", "isTabMenuOpen", "HomeComponent_div_9_Template_div_click_0_listener", "_r7", "closeSidebar", "isSidebarOpen", "HomeComponent_div_10_Template_ion_icon_click_4_listener", "_r8", "HomeComponent_div_10_Template_div_click_6_listener", "openSidebarTab", "HomeComponent_div_10_Template_div_click_13_listener", "HomeComponent_div_10_Template_div_click_20_listener", "HomeComponent_div_10_Template_div_click_27_listener", "HomeComponent_div_10_Template_div_click_34_listener", "HomeComponent_div_10_Template_div_click_41_listener", "HomeComponent_div_11_Template_ion_icon_click_10_listener", "_r9", "category_r11", "icon", "name", "HomeComponent_div_12_div_11_div_2_Template", "categories", "HomeComponent_div_12_Template_ion_icon_click_4_listener", "_r10", "closeSidebarContent", "HomeComponent_div_12_div_6_Template", "HomeComponent_div_12_div_7_Template", "HomeComponent_div_12_div_8_Template", "HomeComponent_div_12_div_9_Template", "HomeComponent_div_12_div_10_Template", "HomeComponent_div_12_div_11_Template", "isSidebarContentOpen", "currentSidebarTitle", "currentSidebarTab", "HomeComponent", "constructor", "isMobile", "window", "isLiked", "id", "preventScroll", "e", "preventDefault", "ngOnInit", "checkScreenSize", "console", "log", "length", "document", "addEventListener", "passive", "ngOnDestroy", "removeEventListener", "onResize", "event", "width", "innerWidth", "userAgent", "navigator", "isMobileUserAgent", "test", "height", "innerHeight", "toggleBodyScroll", "body", "style", "overflow", "tabType", "titles", "toggleLike", "openComments", "shareContent", "share", "title", "text", "url", "location", "href", "openMusic", "story", "index", "vibrate", "onLikeClick", "onCommentClick", "onShareClick", "onBookmarkClick", "navigateToTrending", "navigateToNewArrivals", "navigateToOffers", "navigateToCategories", "navigateToWishlist", "navigateToCart", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_div_8_Template", "HomeComponent_div_9_Template", "HomeComponent_div_10_Template", "HomeComponent_div_11_Template", "HomeComponent_div_12_Template", "HomeComponent_div_13_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { TrendingProductsComponent } from '../../components/trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../../components/featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../../components/new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../../components/suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../../components/top-fashion-influencers/top-fashion-influencers.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IonicModule,\n    ViewAddStoriesComponent,\n    FeedComponent,\n    SidebarComponent,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent,\n    SuggestedForYouComponent,\n    TopFashionInfluencersComponent\n  ],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  isMobile = false;\n  isSidebarOpen = false;\n  isTabMenuOpen = false;\n  isSidebarContentOpen = false;\n  currentSidebarTab = '';\n  currentSidebarTitle = '';\n  hasNotifications = true; // Example notification state\n  window = window; // For template access\n\n  // TikTok-style interaction states\n  isLiked = false;\n\n  // Instagram Stories Data - Enhanced for responsive design and mobile app\n  instagramStories = [\n    {\n      id: 1,\n      username: 'zara',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 2,\n      username: 'nike',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 3,\n      username: 'adidas',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 4,\n      username: 'h&m',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 5,\n      username: 'uniqlo',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 6,\n      username: 'gucci',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: true,\n      touching: false\n    },\n    {\n      id: 7,\n      username: 'prada',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    },\n    {\n      id: 8,\n      username: 'versace',\n      avatar: '/assets/images/default-avatar.svg',\n      hasStory: true,\n      viewed: false,\n      touching: false\n    }\n  ];\n\n  // Categories Data\n  categories = [\n    { name: 'Women', icon: 'woman' },\n    { name: 'Men', icon: 'man' },\n    { name: 'Kids', icon: 'happy' },\n    { name: 'Shoes', icon: 'footsteps' },\n    { name: 'Bags', icon: 'bag' },\n    { name: 'Accessories', icon: 'watch' },\n    { name: 'Beauty', icon: 'flower' },\n    { name: 'Sports', icon: 'fitness' }\n  ];\n\n  constructor() {}\n\n  ngOnInit() {\n    this.checkScreenSize();\n    console.log('Home component initialized:', {\n      isMobile: this.isMobile,\n      instagramStories: this.instagramStories.length\n    });\n    // Prevent body scroll when sidebar is open\n    document.addEventListener('touchmove', this.preventScroll, { passive: false });\n  }\n\n  ngOnDestroy() {\n    document.removeEventListener('touchmove', this.preventScroll);\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any) {\n    this.checkScreenSize();\n    if (!this.isMobile && this.isSidebarOpen) {\n      this.closeSidebar();\n    }\n  }\n\n  private checkScreenSize() {\n    // More comprehensive mobile detection\n    const width = window.innerWidth;\n    const userAgent = navigator.userAgent;\n    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n    // Consider it mobile if width <= 768px OR if it's a mobile user agent\n    this.isMobile = width <= 768 || isMobileUserAgent;\n\n    console.log('Screen size check:', {\n      width: width,\n      height: window.innerHeight,\n      isMobile: this.isMobile,\n      isMobileUserAgent: isMobileUserAgent,\n      userAgent: userAgent\n    });\n  }\n\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeSidebar() {\n    this.isSidebarOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  private toggleBodyScroll() {\n    if (this.isSidebarOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n  }\n\n  private preventScroll = (e: TouchEvent) => {\n    if (this.isSidebarOpen || this.isTabMenuOpen || this.isSidebarContentOpen) {\n      e.preventDefault();\n    }\n  }\n\n  // Tab Menu Methods\n  toggleTabMenu() {\n    this.isTabMenuOpen = !this.isTabMenuOpen;\n    this.toggleBodyScroll();\n  }\n\n  closeTabMenu() {\n    this.isTabMenuOpen = false;\n    this.toggleBodyScroll();\n  }\n\n  openSidebarTab(tabType: string) {\n    this.currentSidebarTab = tabType;\n    this.isSidebarContentOpen = true;\n    this.isTabMenuOpen = false;\n\n    // Set title based on tab type\n    const titles: { [key: string]: string } = {\n      'trending': 'Trending Products',\n      'brands': 'Featured Brands',\n      'arrivals': 'New Arrivals',\n      'suggested': 'Suggested for You',\n      'influencers': 'Fashion Influencers',\n      'categories': 'Categories'\n    };\n\n    this.currentSidebarTitle = titles[tabType] || 'Discover';\n    this.toggleBodyScroll();\n  }\n\n  closeSidebarContent() {\n    this.isSidebarContentOpen = false;\n    this.currentSidebarTab = '';\n    this.toggleBodyScroll();\n  }\n\n  // TikTok-style interaction methods\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like functionality with backend\n    console.log('Like toggled:', this.isLiked);\n  }\n\n  openComments() {\n    // TODO: Implement comments modal/page\n    console.log('Opening comments...');\n  }\n\n  shareContent() {\n    // TODO: Implement share functionality\n    console.log('Sharing content...');\n    if (navigator.share) {\n      navigator.share({\n        title: 'DFashion',\n        text: 'Check out this amazing fashion content!',\n        url: window.location.href\n      });\n    }\n  }\n\n  openMusic() {\n    // TODO: Implement music/audio functionality\n    console.log('Opening music...');\n  }\n\n  // Stories functionality\n  createStory() {\n    console.log('Create story clicked');\n    // TODO: Implement story creation\n  }\n\n  viewStory(story: any) {\n    console.log('View story:', story);\n    // TODO: Implement story viewer\n  }\n\n  trackByStoryId(index: number, story: any): any {\n    return story.id || index;\n  }\n\n  // Enhanced touch interactions for mobile app\n  onStoryTouchStart(event: TouchEvent, story: any) {\n    story.touching = true;\n    // Add haptic feedback if available\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10);\n    }\n  }\n\n  onStoryTouchEnd(event: TouchEvent, story: any) {\n    story.touching = false;\n  }\n\n  // TikTok-style interaction methods\n  onLikeClick() {\n    this.isLiked = !this.isLiked;\n    console.log('Like clicked:', this.isLiked);\n    // TODO: Implement like functionality with backend\n  }\n\n  onCommentClick() {\n    console.log('Comment clicked');\n    // TODO: Implement comment functionality\n  }\n\n  onShareClick() {\n    console.log('Share clicked');\n    // TODO: Implement share functionality\n  }\n\n  onBookmarkClick() {\n    console.log('Bookmark clicked');\n    // TODO: Implement bookmark functionality\n  }\n\n  // Mobile quick actions navigation methods\n  navigateToTrending() {\n    console.log('Navigate to trending');\n    // TODO: Implement navigation to trending page\n  }\n\n  navigateToNewArrivals() {\n    console.log('Navigate to new arrivals');\n    // TODO: Implement navigation to new arrivals page\n  }\n\n  navigateToOffers() {\n    console.log('Navigate to offers');\n    // TODO: Implement navigation to offers page\n  }\n\n  navigateToCategories() {\n    console.log('Navigate to categories');\n    // TODO: Implement navigation to categories page\n  }\n\n  navigateToWishlist() {\n    console.log('Navigate to wishlist');\n    // TODO: Implement navigation to wishlist page\n  }\n\n  navigateToCart() {\n    console.log('Navigate to cart');\n    // TODO: Implement navigation to cart page\n  }\n}\n", "<div class=\"home-container\" [class.mobile-instagram]=\"isMobile\" [class.sidebar-open]=\"isSidebarOpen\" [class.mobile]=\"isMobile\">\n  <!-- Mobile Header with Instagram-like styling -->\n  <div class=\"mobile-header instagram-style\" *ngIf=\"isMobile\">\n    <div class=\"header-left\">\n      <h1 class=\"app-logo\">DFashion</h1>\n      <ion-icon name=\"chevron-down\" class=\"logo-dropdown\"></ion-icon>\n    </div>\n    <div class=\"header-right\">\n      <ion-icon name=\"heart-outline\" class=\"header-icon\"></ion-icon>\n      <div class=\"menu-icon-container\" (click)=\"toggleTabMenu()\">\n        <ion-icon name=\"grid-outline\" class=\"header-icon menu-icon\"></ion-icon>\n        <div class=\"notification-dot\" *ngIf=\"hasNotifications\">3</div>\n      </div>\n      <div class=\"menu-icon-container\" (click)=\"toggleSidebar()\">\n        <ion-icon name=\"menu-outline\" class=\"header-icon menu-icon\"></ion-icon>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Stories Section (Mobile Only) - Enhanced for 768px to 320px -->\n  <div class=\"instagram-stories-section\" *ngIf=\"isMobile\">\n    <div class=\"stories-container\">\n      <div class=\"story-item your-story\" (click)=\"createStory()\">\n        <div class=\"story-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Your story\">\n          <div class=\"add-story-btn\">\n            <ion-icon name=\"add\"></ion-icon>\n          </div>\n        </div>\n        <span class=\"story-username\">Your story</span>\n      </div>\n      <div class=\"story-item\" *ngFor=\"let story of instagramStories; trackBy: trackByStoryId\"\n           (click)=\"viewStory(story)\"\n           (touchstart)=\"onStoryTouchStart($event, story)\"\n           (touchend)=\"onStoryTouchEnd($event, story)\">\n        <div class=\"story-avatar\"\n             [class.has-story]=\"story.hasStory\"\n             [class.viewed]=\"story.viewed\"\n             [class.touching]=\"story.touching\">\n          <img [src]=\"story.avatar\" [alt]=\"story.username\" loading=\"lazy\">\n          <div class=\"story-ring\" *ngIf=\"story.hasStory && !story.viewed\"></div>\n          <div class=\"story-gradient-ring\" *ngIf=\"story.hasStory && !story.viewed\"></div>\n        </div>\n        <span class=\"story-username\">{{story.username}}</span>\n      </div>\n    </div>\n  </div>\n\n\n  <div class=\"content-grid\">\n    <!-- Main Feed -->\n    <div class=\"main-content\">\n      <!-- Desktop Stories Component (Hidden on Mobile) -->\n      <app-view-add-stories></app-view-add-stories>\n      <!-- Instagram-style Feed with Posts and Reels -->\n      <app-feed></app-feed>\n    </div>\n\n    <!-- Desktop Sidebar -->\n    <app-sidebar class=\"desktop-sidebar\"></app-sidebar>\n  </div>\n\n  <!-- Mobile Tab Menu Overlay -->\n  <div class=\"tab-menu-overlay\"\n       [class.active]=\"isTabMenuOpen\"\n       (click)=\"closeTabMenu()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Mobile Sidebar Overlay -->\n  <div class=\"sidebar-overlay\"\n       [class.active]=\"isSidebarOpen\"\n       (click)=\"closeSidebar()\"\n       *ngIf=\"isMobile\">\n  </div>\n\n  <!-- Instagram-Style Tab Menu -->\n  <div class=\"instagram-tab-menu\"\n       [class.active]=\"isTabMenuOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"tab-menu-header\">\n      <h3>Discover</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeTabMenu()\"></ion-icon>\n    </div>\n\n    <div class=\"tab-menu-grid\">\n      <!-- Trending Products Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('trending')\">\n        <div class=\"tab-icon trending\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Trending</span>\n        <div class=\"tab-tooltip\">Hot products right now</div>\n      </div>\n\n      <!-- Featured Brands Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('brands')\">\n        <div class=\"tab-icon brands\">\n          <ion-icon name=\"diamond\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Brands</span>\n        <div class=\"tab-tooltip\">Top fashion brands</div>\n      </div>\n\n      <!-- New Arrivals Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('arrivals')\">\n        <div class=\"tab-icon arrivals\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">New</span>\n        <div class=\"tab-tooltip\">Latest arrivals</div>\n      </div>\n\n      <!-- Suggested for You Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('suggested')\">\n        <div class=\"tab-icon suggested\">\n          <ion-icon name=\"heart\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">For You</span>\n        <div class=\"tab-tooltip\">Personalized picks</div>\n      </div>\n\n      <!-- Fashion Influencers Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('influencers')\">\n        <div class=\"tab-icon influencers\">\n          <ion-icon name=\"people\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Influencers</span>\n        <div class=\"tab-tooltip\">Top fashion creators</div>\n      </div>\n\n      <!-- Categories Tab -->\n      <div class=\"tab-item\" (click)=\"openSidebarTab('categories')\">\n        <div class=\"tab-icon categories\">\n          <ion-icon name=\"grid\"></ion-icon>\n        </div>\n        <span class=\"tab-label\">Categories</span>\n        <div class=\"tab-tooltip\">Browse by category</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <div class=\"mobile-sidebar\"\n       [class.active]=\"isSidebarOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"sidebar-header\">\n      <div class=\"user-profile\">\n        <div class=\"profile-avatar\">\n          <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>Your Profile</h3>\n          <p>&#64;username</p>\n        </div>\n      </div>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebar()\"></ion-icon>\n    </div>\n\n    <div class=\"sidebar-content\">\n      <app-sidebar></app-sidebar>\n    </div>\n  </div>\n\n  <!-- Sidebar Content Modal -->\n  <div class=\"sidebar-content-modal\"\n       [class.active]=\"isSidebarContentOpen\"\n       *ngIf=\"isMobile\">\n    <div class=\"modal-header\">\n      <h3>{{currentSidebarTitle}}</h3>\n      <ion-icon name=\"close-outline\" class=\"close-icon\" (click)=\"closeSidebarContent()\"></ion-icon>\n    </div>\n\n    <div class=\"modal-content\">\n      <!-- Trending Products Section -->\n      <div *ngIf=\"currentSidebarTab === 'trending'\" class=\"sidebar-section\">\n        <app-trending-products></app-trending-products>\n      </div>\n\n      <!-- Featured Brands Section -->\n      <div *ngIf=\"currentSidebarTab === 'brands'\" class=\"sidebar-section\">\n        <app-featured-brands></app-featured-brands>\n      </div>\n\n      <!-- New Arrivals Section -->\n      <div *ngIf=\"currentSidebarTab === 'arrivals'\" class=\"sidebar-section\">\n        <app-new-arrivals></app-new-arrivals>\n      </div>\n\n      <!-- Suggested for you -->\n      <div *ngIf=\"currentSidebarTab === 'suggested'\" class=\"sidebar-section\">\n        <app-suggested-for-you></app-suggested-for-you>\n      </div>\n\n      <!-- Top Fashion Influencers -->\n      <div *ngIf=\"currentSidebarTab === 'influencers'\" class=\"sidebar-section\">\n        <app-top-fashion-influencers></app-top-fashion-influencers>\n      </div>\n\n      <!-- Categories (placeholder) -->\n      <div *ngIf=\"currentSidebarTab === 'categories'\" class=\"sidebar-section\">\n        <div class=\"categories-grid\">\n          <div class=\"category-item\" *ngFor=\"let category of categories\">\n            <div class=\"category-icon\">\n              <ion-icon [name]=\"category.icon\"></ion-icon>\n            </div>\n            <span>{{category.name}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instagram Bottom Navigation (Mobile Only) -->\n  <div class=\"instagram-bottom-nav\" *ngIf=\"isMobile\">\n    <div class=\"nav-item active\">\n      <ion-icon name=\"home\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"search\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"add-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <ion-icon name=\"play-circle-outline\"></ion-icon>\n    </div>\n    <div class=\"nav-item\">\n      <div class=\"profile-avatar-nav\">\n        <img src=\"assets/images/default-avatar.svg\" alt=\"Profile\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,uBAAuB,QAAQ,4DAA4D;AACpG,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,8BAA8B,QAAQ,4EAA4E;;;;;;ICAnHC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPhEH,EAFJ,CAAAC,cAAA,cAA4D,cACjC,aACF;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAI,SAAA,mBAA+D;IACjEJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,mBAA8D;IAC9DJ,EAAA,CAAAC,cAAA,cAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAC,kDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACxDZ,EAAA,CAAAI,SAAA,mBAAuE;IACvEJ,EAAA,CAAAa,UAAA,IAAAC,kCAAA,kBAAuD;IACzDd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2D;IAA1BD,EAAA,CAAAK,UAAA,mBAAAU,mDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,aAAA,EAAe;IAAA,EAAC;IACxDhB,EAAA,CAAAI,SAAA,oBAAuE;IAG7EJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAN+BH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAU,gBAAA,CAAsB;;;;;IA6BnDnB,EAAA,CAAAI,SAAA,cAAsE;;;;;IACtEJ,EAAA,CAAAI,SAAA,cAA+E;;;;;;IAVnFJ,EAAA,CAAAC,cAAA,cAGiD;IAA5CD,EAFA,CAAAK,UAAA,mBAAAe,wDAAA;MAAA,MAAAC,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,SAAA,CAAAH,QAAA,CAAgB;IAAA,EAAC,wBAAAI,6DAAAC,MAAA;MAAA,MAAAL,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACZF,MAAA,CAAAkB,iBAAA,CAAAD,MAAA,EAAAL,QAAA,CAAgC;IAAA,EAAC,sBAAAO,2DAAAF,MAAA;MAAA,MAAAL,QAAA,GAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACnCF,MAAA,CAAAoB,eAAA,CAAAH,MAAA,EAAAL,QAAA,CAA8B;IAAA,EAAC;IAC9CrB,EAAA,CAAAC,cAAA,cAGuC;IACrCD,EAAA,CAAAI,SAAA,cAAgE;IAEhEJ,EADA,CAAAa,UAAA,IAAAiB,wCAAA,kBAAgE,IAAAC,wCAAA,kBACS;IAC3E/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EAClD;;;;IARCH,EAAA,CAAAiB,SAAA,EAAkC;IAElCjB,EAFA,CAAAgC,WAAA,cAAAX,QAAA,CAAAY,QAAA,CAAkC,WAAAZ,QAAA,CAAAa,MAAA,CACL,aAAAb,QAAA,CAAAc,QAAA,CACI;IAC/BnC,EAAA,CAAAiB,SAAA,EAAoB;IAACjB,EAArB,CAAAkB,UAAA,QAAAG,QAAA,CAAAe,MAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAoB,QAAAhB,QAAA,CAAAiB,QAAA,CAAuB;IACvBtC,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAkB,UAAA,SAAAG,QAAA,CAAAY,QAAA,KAAAZ,QAAA,CAAAa,MAAA,CAAqC;IAC5BlC,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAkB,UAAA,SAAAG,QAAA,CAAAY,QAAA,KAAAZ,QAAA,CAAAa,MAAA,CAAqC;IAE5ClC,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAuC,iBAAA,CAAAlB,QAAA,CAAAiB,QAAA,CAAkB;;;;;;IArBjDtC,EAFJ,CAAAC,cAAA,cAAwD,cACvB,cAC8B;IAAxBD,EAAA,CAAAK,UAAA,mBAAAmC,kDAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiC,WAAA,EAAa;IAAA,EAAC;IACxD1C,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,cAA6D;IAC7DJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAI,SAAA,mBAAgC;IAEpCJ,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAa,UAAA,IAAA8B,kCAAA,mBAGiD;IAYrD3C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfwCH,EAAA,CAAAiB,SAAA,GAAqB;IAAAjB,EAArB,CAAAkB,UAAA,YAAAT,MAAA,CAAAmC,gBAAA,CAAqB,iBAAAnC,MAAA,CAAAoC,cAAA,CAAuB;;;;;;IAgC1F7C,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAAyC,kDAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,YAAA,EAAc;IAAA,EAAC;IAE7BhD,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAwC,aAAA,CAA8B;;;;;;IAMnCjD,EAAA,CAAAC,cAAA,cAGsB;IADjBD,EAAA,CAAAK,UAAA,mBAAA6C,kDAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAE7BpD,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAA4C,aAAA,CAA8B;;;;;;IAU/BrD,EAJJ,CAAAC,cAAA,cAEsB,cACS,SACvB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,mBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAAiD,wDAAA;MAAAtD,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,YAAA,EAAc;IAAA,EAAC;IAC5EhD,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAIJH,EAFF,CAAAC,cAAA,cAA2B,cAEkC;IAArCD,EAAA,CAAAK,UAAA,mBAAAmD,mDAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDzD,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,mBAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EACjD;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAAnCD,EAAA,CAAAK,UAAA,mBAAAqD,oDAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACtDzD,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,oBAAoC;IACtCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IAArCD,EAAA,CAAAK,UAAA,mBAAAsD,oDAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACxDzD,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,oBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IAGNH,EAAA,CAAAC,cAAA,eAA4D;IAAtCD,EAAA,CAAAK,UAAA,mBAAAuD,oDAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,WAAW,CAAC;IAAA,EAAC;IACzDzD,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAI,SAAA,oBAAkC;IACpCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAA8D;IAAxCD,EAAA,CAAAK,UAAA,mBAAAwD,oDAAA;MAAA7D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,aAAa,CAAC;IAAA,EAAC;IAC3DzD,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAI,SAAA,oBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;IAGNH,EAAA,CAAAC,cAAA,eAA6D;IAAvCD,EAAA,CAAAK,UAAA,mBAAAyD,oDAAA;MAAA9D,EAAA,CAAAO,aAAA,CAAAgD,GAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAe,YAAY,CAAC;IAAA,EAAC;IAC1DzD,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAI,SAAA,oBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAGjDF,EAHiD,CAAAG,YAAA,EAAM,EAC7C,EACF,EACF;;;;IA9DDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAwC,aAAA,CAA8B;;;;;;IAsE7BjD,EALN,CAAAC,cAAA,cAEsB,cACQ,cACA,cACI;IAC1BD,EAAA,CAAAI,SAAA,cAA0D;IAC5DJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gBAAa;IAEpBF,EAFoB,CAAAG,YAAA,EAAI,EAChB,EACF;IACNH,EAAA,CAAAC,cAAA,oBAA2E;IAAzBD,EAAA,CAAAK,UAAA,mBAAA0D,yDAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAC5EpD,EAD6E,CAAAG,YAAA,EAAW,EAClF;IAENH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAI,SAAA,mBAA2B;IAE/BJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlBDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAA4C,aAAA,CAA8B;;;;;IA+B/BrD,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,SAAA,0BAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,uBAAqC;IACvCJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,SAAA,4BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,kCAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAMAH,EADF,CAAAC,cAAA,cAA+D,cAClC;IACzBD,EAAA,CAAAI,SAAA,mBAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;;;;IAHQH,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,SAAA+C,YAAA,CAAAC,IAAA,CAAsB;IAE5BlE,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAuC,iBAAA,CAAA0B,YAAA,CAAAE,IAAA,CAAiB;;;;;IAL3BnE,EADF,CAAAC,cAAA,cAAwE,cACzC;IAC3BD,EAAA,CAAAa,UAAA,IAAAuD,0CAAA,kBAA+D;IAOnEpE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAP8CH,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,YAAAT,MAAA,CAAA4D,UAAA,CAAa;;;;;;IAjCjErE,EAJJ,CAAAC,cAAA,cAEsB,cACM,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,mBAAkF;IAAhCD,EAAA,CAAAK,UAAA,mBAAAiE,wDAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+D,mBAAA,EAAqB;IAAA,EAAC;IACnFxE,EADoF,CAAAG,YAAA,EAAW,EACzF;IAENH,EAAA,CAAAC,cAAA,cAA2B;IA2BzBD,EAzBA,CAAAa,UAAA,IAAA4D,mCAAA,kBAAsE,IAAAC,mCAAA,kBAKF,IAAAC,mCAAA,kBAKE,IAAAC,mCAAA,kBAKC,KAAAC,oCAAA,kBAKE,KAAAC,oCAAA,kBAKD;IAW5E9E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7CDH,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAsE,oBAAA,CAAqC;IAGlC/E,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAuE,mBAAA,CAAuB;IAMrBhF,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,gBAAsC;IAKtCjF,EAAA,CAAAiB,SAAA,EAAoC;IAApCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,cAAoC;IAKpCjF,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,gBAAsC;IAKtCjF,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,iBAAuC;IAKvCjF,EAAA,CAAAiB,SAAA,EAAyC;IAAzCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,mBAAyC;IAKzCjF,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAwE,iBAAA,kBAAwC;;;;;IAehDjF,EADF,CAAAC,cAAA,cAAmD,cACpB;IAC3BD,EAAA,CAAAI,SAAA,mBAAiC;IACnCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,mBAAgD;IAClDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,eACY;IAC9BD,EAAA,CAAAI,SAAA,eAA0D;IAGhEJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;ADzMR,OAAM,MAAO+E,aAAa;EA6FxBC,YAAA;IA5FA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAA/B,aAAa,GAAG,KAAK;IACrB,KAAAJ,aAAa,GAAG,KAAK;IACrB,KAAA8B,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,iBAAiB,GAAG,EAAE;IACtB,KAAAD,mBAAmB,GAAG,EAAE;IACxB,KAAA7D,gBAAgB,GAAG,IAAI,CAAC,CAAC;IACzB,KAAAkE,MAAM,GAAGA,MAAM,CAAC,CAAC;IAEjB;IACA,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAA1C,gBAAgB,GAAG,CACjB;MACE2C,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,MAAM;MAChBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,MAAM;MAChBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,QAAQ;MAClBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,KAAK;MACfF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,QAAQ;MAClBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,OAAO;MACjBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,OAAO;MACjBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEoD,EAAE,EAAE,CAAC;MACLjD,QAAQ,EAAE,SAAS;MACnBF,MAAM,EAAE,mCAAmC;MAC3CH,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,CACF;IAED;IACA,KAAAkC,UAAU,GAAG,CACX;MAAEF,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAO,CAAE,EAChC;MAAEC,IAAI,EAAE,KAAK;MAAED,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAO,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAW,CAAE,EACpC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAO,CAAE,EACtC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAQ,CAAE,EAClC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAS,CAAE,CACpC;IA8DO,KAAAsB,aAAa,GAAIC,CAAa,IAAI;MACxC,IAAI,IAAI,CAACpC,aAAa,IAAI,IAAI,CAACJ,aAAa,IAAI,IAAI,CAAC8B,oBAAoB,EAAE;QACzEU,CAAC,CAACC,cAAc,EAAE;;IAEtB,CAAC;EAhEc;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;MACzCV,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBxC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACmD;KACzC,CAAC;IACF;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACT,aAAa,EAAE;MAAEU,OAAO,EAAE;IAAK,CAAE,CAAC;EAChF;EAEAC,WAAWA,CAAA;IACTH,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACZ,aAAa,CAAC;EAC/D;EAGAa,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACV,eAAe,EAAE;IACtB,IAAI,CAAC,IAAI,CAACR,QAAQ,IAAI,IAAI,CAAC/B,aAAa,EAAE;MACxC,IAAI,CAACD,YAAY,EAAE;;EAEvB;EAEQwC,eAAeA,CAAA;IACrB;IACA,MAAMW,KAAK,GAAGlB,MAAM,CAACmB,UAAU;IAC/B,MAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,iBAAiB,GAAG,gEAAgE,CAACC,IAAI,CAACH,SAAS,CAAC;IAE1G;IACA,IAAI,CAACrB,QAAQ,GAAGmB,KAAK,IAAI,GAAG,IAAII,iBAAiB;IAEjDd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChCS,KAAK,EAAEA,KAAK;MACZM,MAAM,EAAExB,MAAM,CAACyB,WAAW;MAC1B1B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBuB,iBAAiB,EAAEA,iBAAiB;MACpCF,SAAS,EAAEA;KACZ,CAAC;EACJ;EAEAzF,aAAaA,CAAA;IACX,IAAI,CAACqC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC0D,gBAAgB,EAAE;EACzB;EAEA3D,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC0D,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtB2C,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;KACxC,MAAM;MACLlB,QAAQ,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;;EAErC;EAQA;EACAtG,aAAaA,CAAA;IACX,IAAI,CAACqC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC8D,gBAAgB,EAAE;EACzB;EAEA/D,YAAYA,CAAA;IACV,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC8D,gBAAgB,EAAE;EACzB;EAEAtD,cAAcA,CAAC0D,OAAe;IAC5B,IAAI,CAAClC,iBAAiB,GAAGkC,OAAO;IAChC,IAAI,CAACpC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC9B,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMmE,MAAM,GAA8B;MACxC,UAAU,EAAE,mBAAmB;MAC/B,QAAQ,EAAE,iBAAiB;MAC3B,UAAU,EAAE,cAAc;MAC1B,WAAW,EAAE,mBAAmB;MAChC,aAAa,EAAE,qBAAqB;MACpC,YAAY,EAAE;KACf;IAED,IAAI,CAACpC,mBAAmB,GAAGoC,MAAM,CAACD,OAAO,CAAC,IAAI,UAAU;IACxD,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEAvC,mBAAmBA,CAAA;IACjB,IAAI,CAACO,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACE,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC8B,gBAAgB,EAAE;EACzB;EAEA;EACAM,UAAUA,CAAA;IACR,IAAI,CAAC/B,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;IACAO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,OAAO,CAAC;EAC5C;EAEAgC,YAAYA,CAAA;IACV;IACAzB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEAyB,YAAYA,CAAA;IACV;IACA1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAIY,SAAS,CAACc,KAAK,EAAE;MACnBd,SAAS,CAACc,KAAK,CAAC;QACdC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAEtC,MAAM,CAACuC,QAAQ,CAACC;OACtB,CAAC;;EAEN;EAEAC,SAASA,CAAA;IACP;IACAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA;EACApD,WAAWA,CAAA;IACTmD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAtE,SAASA,CAACuG,KAAU;IAClBlC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiC,KAAK,CAAC;IACjC;EACF;EAEAlF,cAAcA,CAACmF,KAAa,EAAED,KAAU;IACtC,OAAOA,KAAK,CAACxC,EAAE,IAAIyC,KAAK;EAC1B;EAEA;EACArG,iBAAiBA,CAAC2E,KAAiB,EAAEyB,KAAU;IAC7CA,KAAK,CAAC5F,QAAQ,GAAG,IAAI;IACrB;IACA,IAAI,SAAS,IAAIuE,SAAS,EAAE;MAC1BA,SAAS,CAACuB,OAAO,CAAC,EAAE,CAAC;;EAEzB;EAEApG,eAAeA,CAACyE,KAAiB,EAAEyB,KAAU;IAC3CA,KAAK,CAAC5F,QAAQ,GAAG,KAAK;EACxB;EAEA;EACA+F,WAAWA,CAAA;IACT,IAAI,CAAC5C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5BO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,OAAO,CAAC;IAC1C;EACF;EAEA6C,cAAcA,CAAA;IACZtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;EACF;EAEAsC,YAAYA,CAAA;IACVvC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B;EACF;EAEAuC,eAAeA,CAAA;IACbxC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACAwC,kBAAkBA,CAAA;IAChBzC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEAyC,qBAAqBA,CAAA;IACnB1C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEA0C,gBAAgBA,CAAA;IACd3C,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;EACF;EAEA2C,oBAAoBA,CAAA;IAClB5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;EAEA4C,kBAAkBA,CAAA;IAChB7C,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;EACF;EAEA6C,cAAcA,CAAA;IACZ9C,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;;;uBA/SWZ,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAA0D,SAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAb/I,EAAA,CAAAK,UAAA,oBAAA4I,wCAAAvH,MAAA;YAAA,OAAAsH,GAAA,CAAA3C,QAAA,CAAA3E,MAAA,CAAgB;UAAA,UAAA1B,EAAA,CAAAkJ,eAAA,CAAH;;;;;;;;;;UC/B1BlJ,EAAA,CAAAC,cAAA,aAA+H;UAoB7HD,EAlBA,CAAAa,UAAA,IAAAsI,4BAAA,kBAA4D,IAAAC,4BAAA,kBAkBJ;UA+BtDpJ,EAFF,CAAAC,cAAA,aAA0B,aAEE;UAIxBD,EAFA,CAAAI,SAAA,2BAA6C,eAExB;UACvBJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAI,SAAA,qBAAmD;UACrDJ,EAAA,CAAAG,YAAA,EAAM;UA0JNH,EAvJA,CAAAa,UAAA,IAAAwI,4BAAA,iBAGsB,IAAAC,4BAAA,iBAOA,KAAAC,6BAAA,kBAMA,KAAAC,6BAAA,kBAkEA,KAAAC,6BAAA,mBAsBA,KAAAC,6BAAA,mBA+C6B;UAmBrD1J,EAAA,CAAAG,YAAA,EAAM;;;UAzO+FH,EAAzE,CAAAgC,WAAA,qBAAAgH,GAAA,CAAA5D,QAAA,CAAmC,iBAAA4D,GAAA,CAAA3F,aAAA,CAAqC,WAAA2F,GAAA,CAAA5D,QAAA,CAA0B;UAEhFpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAkBlBpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UA8ChDpF,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAOdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAMdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAkEdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UAsBdpF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;UA+CepF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA8H,GAAA,CAAA5D,QAAA,CAAc;;;qBDrM/C9F,YAAY,EAAAqK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZtK,WAAW,EAAAuK,EAAA,CAAAC,OAAA,EACXvK,uBAAuB,EACvBC,aAAa,EACbC,gBAAgB,EAChBC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB,EACpBC,wBAAwB,EACxBC,8BAA8B;MAAAiK,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}