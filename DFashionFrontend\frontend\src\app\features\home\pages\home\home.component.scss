.home-container {
  padding: 20px 0;
  min-height: calc(100vh - 60px);
  position: relative;
  background: #ffffff;

  &.mobile-instagram {
    background: #ffffff !important;
    color: #262626 !important;
    padding: 0 !important;
    min-height: 100vh !important;
  }
}

// Mobile Header (Instagram-like)
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #dbdbdb;
  z-index: 1001; /* Higher than stories section */
  padding: 0 16px;
  align-items: center;
  justify-content: space-between;

  &.instagram-style {
    background: #ffffff;
    border-bottom: 1px solid #dbdbdb;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .app-logo {
      font-size: 28px;
      font-weight: 400;
      color: #262626;
      margin: 0;
      font-family: 'Billabong', cursive, -apple-system, BlinkMacSystemFont, sans-serif;
      letter-spacing: 0.5px;
    }

    .logo-dropdown {
      font-size: 16px;
      color: #262626;
      margin-top: 2px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-icon {
      font-size: 24px;
      color: #262626;
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 8px;
      border-radius: 50%;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .menu-icon-container {
      position: relative;
      cursor: pointer;

      .notification-dot {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        background: #ff3040;
        border-radius: 50%;
        border: 2px solid #ffffff;
      }
    }
  }
}

// Instagram Stories Section
.instagram-stories-section {
  display: none;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background: #ffffff;
  border-bottom: 1px solid #dbdbdb;
  z-index: 999;
  padding: 12px 0;
  height: 100px; /* Fixed height for stories section */
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* Enhanced mobile app styling */
  @supports (backdrop-filter: blur(10px)) {
    background: rgba(255, 255, 255, 0.95);
  }

  .stories-container {
    display: flex;
    gap: 12px;
    padding: 0 16px;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    height: 100%;
    align-items: center;
    min-width: max-content;
    scroll-behavior: smooth;
    position: relative;
    z-index: 998; /* Below header but above content */

    /* Enhanced touch scrolling for mobile app */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-x: contain;
    scroll-snap-type: x proximity;

    /* Mobile app optimizations */
    will-change: scroll-position;
    transform: translateZ(0); /* Hardware acceleration */

    &::-webkit-scrollbar {
      display: none;
    }

    /* Enhanced responsive adjustments for 768px to 320px */
    @media (min-width: 320px) and (max-width: 768px) {
      padding: 0 12px;
      gap: 10px;
      scroll-snap-type: x mandatory;

      /* Optimize for touch interactions */
      touch-action: pan-x;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior-x: contain;

      /* Mobile app performance */
      contain: layout style paint;
    }

    @media (min-width: 320px) and (max-width: 480px) {
      padding: 0 8px;
      gap: 8px;

      /* Smaller screens optimization */
      scroll-padding-left: 8px;
      scroll-padding-right: 8px;
    }

    @media (min-width: 320px) and (max-width: 400px) {
      padding: 0 6px;
      gap: 6px;

      /* Very small screens */
      scroll-padding-left: 6px;
      scroll-padding-right: 6px;
    }

    .story-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      min-width: 70px;
      max-width: 70px;
      cursor: pointer;
      flex-shrink: 0;
      transition: transform 0.2s ease;
      scroll-snap-align: start;
      scroll-snap-stop: normal;
      position: relative;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      /* Enhanced responsive sizing for 768px to 320px */
      @media (min-width: 320px) and (max-width: 768px) {
        min-width: 65px;
        max-width: 65px;
        gap: 5px;

        /* Improve touch target size */
        padding: 4px;
        margin: -4px;
      }

      @media (min-width: 320px) and (max-width: 480px) {
        min-width: 60px;
        max-width: 60px;
        gap: 4px;
      }

      @media (min-width: 320px) and (max-width: 400px) {
        min-width: 55px;
        max-width: 55px;
        gap: 3px;
      }

      @media (max-width: 320px) {
        min-width: 50px;
        max-width: 50px;
        gap: 2px;
      }

      &.your-story {
        .story-avatar {
          position: relative;

          .add-story-btn {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: #0095f6;
            border: 2px solid #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            ion-icon {
              font-size: 12px;
              color: white;
            }
          }
        }
      }

      .story-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #dbdbdb;
        position: relative;
        flex-shrink: 0;
        transition: all 0.2s ease;

        &.has-story {
          border: 2px solid transparent;
          background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
          padding: 2px;

          img {
            border-radius: 50%;
            border: 2px solid #ffffff;
          }

          /* Pulse animation for unviewed stories */
          &:not(.viewed) {
            animation: storyPulse 2s infinite;
          }
        }

        &.viewed {
          border: 2px solid #c7c7c7;
          background: #c7c7c7;
        }

        &.touching {
          animation: storyTouchFeedback 0.2s ease;
        }

        .story-ring {
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
          z-index: -1;
          opacity: 0.8;
        }

        .story-gradient-ring {
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          border-radius: 50%;
          background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
          z-index: -2;
          opacity: 0.3;
          animation: storyRingGradient 3s infinite;
          filter: blur(2px);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
          display: block;
          transition: transform 0.2s ease;
          position: relative;
          z-index: 1;
        }

        /* Enhanced responsive sizing for 768px to 320px */
        @media (min-width: 320px) and (max-width: 768px) {
          width: 55px;
          height: 55px;
        }

        @media (min-width: 320px) and (max-width: 480px) {
          width: 50px;
          height: 50px;
        }

        @media (min-width: 320px) and (max-width: 400px) {
          width: 45px;
          height: 45px;
        }

        @media (max-width: 320px) {
          width: 40px;
          height: 40px;
        }
      }

      .story-username {
        font-size: 11px;
        color: #262626;
        text-align: center;
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
        font-weight: 400;
        margin-top: 4px;

        /* Mobile app text rendering */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;

        /* Enhanced responsive sizing for 768px to 320px */
        @media (min-width: 320px) and (max-width: 768px) {
          font-size: 10px;
          max-width: 65px;
          font-weight: 500; /* Slightly bolder for mobile readability */
        }

        @media (min-width: 320px) and (max-width: 480px) {
          font-size: 9px;
          max-width: 60px;
          line-height: 1.1;
        }

        @media (min-width: 320px) and (max-width: 400px) {
          font-size: 8px;
          max-width: 55px;
          line-height: 1.1;
        }

        @media (max-width: 320px) {
          font-size: 7px;
          max-width: 50px;
          line-height: 1.0;
          font-weight: 600; /* Bold for very small screens */
        }
      }
    }
  }
}

// Enhanced Animations for Stories - Mobile App Optimized
@keyframes storyPulse {
  0% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0.7);
  }
  70% {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 0 0 10px rgba(240, 148, 51, 0);
  }
  100% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 0 0 0 rgba(240, 148, 51, 0);
  }
}

// Story ring gradient animation
@keyframes storyRingGradient {
  0% {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
  25% {
    background: linear-gradient(90deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
  50% {
    background: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
  75% {
    background: linear-gradient(180deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
  100% {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
}

// Mobile app touch feedback
@keyframes storyTouchFeedback {
  0% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(0.95) translateZ(0);
  }
  100% {
    transform: scale(1) translateZ(0);
  }
}

// Instagram Bottom Navigation
.instagram-bottom-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid #dbdbdb;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0;
  z-index: 1000;
  height: 60px;
  box-shadow: 0 -1px 3px rgba(0,0,0,0.1);

  // Safe area support for iOS
  padding-bottom: max(8px, env(safe-area-inset-bottom));

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-width: 44px;
    min-height: 44px;
    position: relative;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.active {
      ion-icon {
        color: #262626;
        transform: scale(1.1);
      }
    }

    ion-icon {
      font-size: 24px;
      color: #8e8e8e;
      transition: all 0.2s ease;
    }

    .profile-avatar-nav {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid #8e8e8e;

      &.active {
        border: 2px solid #262626;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // Badge for notifications/counts
    .nav-badge {
      position: absolute;
      top: 2px;
      right: 2px;
      background: #ff3040;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 16px;
      text-align: center;
      line-height: 1.2;
    }
  }
}

// Instagram-Style Tab Menu
.tab-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

.instagram-tab-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #000000;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  z-index: 1600;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 70vh;
  overflow-y: auto;

  &.active {
    transform: translateY(0);
  }

  .tab-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #262626;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: white;
      margin: 0;
    }

    .close-icon {
      font-size: 24px;
      color: #8e8e8e;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }
  }

  .tab-menu-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    padding: 24px;

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      padding: 16px;
      border-radius: 16px;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
        transform: scale(1.05);
      }

      .tab-icon {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        ion-icon {
          font-size: 24px;
          color: white;
        }

        &.trending {
          background: linear-gradient(135deg, #ff6b6b, #ff8e53);
        }

        &.brands {
          background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        &.arrivals {
          background: linear-gradient(135deg, #a8edea, #fed6e3);

          ion-icon {
            color: #333;
          }
        }

        &.suggested {
          background: linear-gradient(135deg, #ff9a9e, #fecfef);

          ion-icon {
            color: #333;
          }
        }

        &.influencers {
          background: linear-gradient(135deg, #667eea, #764ba2);
        }

        &.categories {
          background: linear-gradient(135deg, #f093fb, #f5576c);
        }
      }

      .tab-label {
        font-size: 14px;
        font-weight: 600;
        color: white;
        text-align: center;
      }

      .tab-tooltip {
        font-size: 12px;
        color: #8e8e8e;
        text-align: center;
        line-height: 1.3;
      }
    }
  }
}

// Sidebar Content Modal
.sidebar-content-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  z-index: 1700;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;

  &.active {
    transform: translateX(0);
  }

  .modal-header {
    position: sticky;
    top: 0;
    background: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #262626;
    z-index: 10;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: white;
      margin: 0;
    }

    .close-icon {
      font-size: 24px;
      color: #8e8e8e;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }
  }

  .modal-content {
    padding: 0;

    .sidebar-section {
      background: #000000;
      color: white;
      min-height: calc(100vh - 80px);

      // Override any white backgrounds in sidebar components
      ::ng-deep {
        * {
          background-color: transparent !important;
          color: white !important;
        }

        .card, .section, .item {
          background: #1a1a1a !important;
          border: 1px solid #262626 !important;
        }

        .text-dark {
          color: white !important;
        }

        .bg-white {
          background: #1a1a1a !important;
        }
      }
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      padding: 24px;

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 20px;
        background: #1a1a1a;
        border-radius: 16px;
        border: 1px solid #262626;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #262626;
          transform: scale(1.02);
        }

        .category-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea, #764ba2);
          display: flex;
          align-items: center;
          justify-content: center;

          ion-icon {
            font-size: 24px;
            color: white;
          }
        }

        span {
          font-size: 14px;
          font-weight: 600;
          color: white;
          text-align: center;
        }
      }
    }
  }
}

// Web Layout: Stories Section + Two Column Layout
.web-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px 20px 20px;
  min-height: calc(100vh - 100px);

  @media (min-width: 769px) {
    display: block;
  }

  @media (max-width: 768px) {
    display: none;
  }
}

// Stories Section Container (Full Width)
.stories-section-container {
  width: 100%;
  margin-bottom: 20px;
}

// Two Column Layout
.two-column-layout {
  display: flex;
  gap: 20px;
}

// Post Section (Left Column)
.post-section {
  flex: 1;
}

// Sidebar Section (Right Column)
.sidebar-section {
  flex: 1;

  // Ensure sidebar is visible on desktop
  @media (min-width: 1025px) {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

// Mobile Layout: Original Layout
.mobile-layout {
  @media (min-width: 769px) {
    display: none;
  }

  @media (max-width: 768px) {
    display: block;
  }
}

// Content Grid (Mobile)
.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
  background: #ffffff;
  padding: 0 20px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.desktop-sidebar {
  display: block;
}

// Mobile Sidebar Overlay
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  z-index: 200;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Mobile Sidebar
.mobile-sidebar {
  position: fixed;
  top: 0;
  right: -100%;
  width: 85%;
  max-width: 400px;
  height: 100%;
  background: #ffffff;
  z-index: 300;
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  &.active {
    right: 0;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px;
    border-bottom: 1px solid #dbdbdb;
    background: #fafafa;

    .user-profile {
      display: flex;
      align-items: center;
      gap: 12px;

      .profile-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #dbdbdb;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .profile-info {
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #8e8e8e;
        }
      }
    }

    .close-icon {
      font-size: 24px;
      color: #262626;
      cursor: pointer;
      padding: 8px;
      margin: -8px;
      transition: color 0.2s ease;

      &:hover {
        color: #8e8e8e;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    // Custom scrollbar for webkit browsers
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}

@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    max-width: 100%;
    padding: 0 16px;

    /* Tablet specific adjustments */
    @media (min-width: 768px) {
      max-width: 768px;
      margin: 0 auto;
      padding: 0 24px;
    }
  }

  .desktop-sidebar {
    display: none;
  }

  // Override for desktop screens - show sidebar on larger screens
  @media (min-width: 1025px) {
    .desktop-sidebar {
      display: block !important;
    }
  }

  .mobile-header {
    display: flex;
  }

  .home-container {
    padding-top: 80px; // Account for fixed header

    /* Tablet specific adjustments */
    @media (min-width: 768px) {
      padding: 80px 0 0 0;
    }
  }
}

// Force mobile layout when mobile-instagram class is present
.home-container.mobile-instagram {
  background: #ffffff !important;
  min-height: 100vh !important;

  .mobile-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 60px !important;
    background: #ffffff !important;
    border-bottom: 1px solid #dbdbdb !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  }

  .instagram-stories-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100px !important;
    padding: 8px 0 !important;
    background: #ffffff !important;
    border-bottom: 1px solid #dbdbdb !important;

    /* Mobile app optimizations */
    transform: translateZ(0) !important;
    will-change: scroll-position !important;
    contain: layout style paint !important;

    /* Enhanced backdrop for mobile */
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;

    @supports (backdrop-filter: blur(10px)) {
      background: rgba(255, 255, 255, 0.95) !important;
    }
  }

  .content-grid {
    /* Mobile-first approach */
    grid-template-columns: 1fr !important;
    padding: 160px 0 60px 0 !important; /* Account for header (60px) + stories (100px) + bottom nav (60px) */
    background: #ffffff !important;
    gap: 0 !important;
    margin: 0 !important;
    max-width: 100% !important;
    min-height: calc(100vh - 220px) !important;
    overflow-x: hidden !important;
    position: relative !important;
    z-index: 1 !important; /* Ensure content is below fixed elements */

    /* Tablet responsive (768px and up) */
    @media (min-width: 768px) {
      padding: 160px 16px 60px 16px !important; /* Add side padding for tablets */
      max-width: 768px !important;
      margin: 0 auto !important; /* Center content */
      gap: 16px !important;
    }

    /* Desktop responsive (1024px and up) */
    @media (min-width: 1024px) {
      grid-template-columns: 1fr 300px !important; /* Two column layout */
      padding: 160px 24px 60px 24px !important;
      max-width: 1200px !important;
      gap: 32px !important;
    }

    /* Large desktop (1200px and up) */
    @media (min-width: 1200px) {
      grid-template-columns: 1fr 400px !important; /* Wider sidebar */
      padding: 160px 32px 60px 32px !important;
      max-width: 1400px !important;
      gap: 40px !important;
    }

    /* Extra large screens (1440px and up) */
    @media (min-width: 1440px) {
      padding: 160px 48px 60px 48px !important;
      max-width: 1600px !important;
      gap: 48px !important;
    }
  }

  .mobile-bottom-nav {
    background: #ffffff !important;
    border-top: 1px solid #dbdbdb !important;
    box-shadow: 0 -1px 3px rgba(0,0,0,0.1) !important;
  }
}

// Enhanced Mobile Design Improvements - Web Responsive (320px to 768px)
@media (min-width: 320px) and (max-width: 768px) {
  .home-container {
    background: #fafafa !important;
    padding: 0 !important;

    &.mobile-instagram {
      background: #fafafa !important;

      .content-grid {
        grid-template-columns: 1fr !important;
        padding: 5px 0 !important;
        background: #ffffff !important;
        gap: 0 !important;
        margin: 0 !important;
        max-width: 100% !important;
        min-height: calc(100vh - 220px) !important;
        overflow-x: hidden !important;
        position: relative !important;
        z-index: 1 !important;
      }
    }
  }

  .content-grid {
    padding: 0;
    margin: 0;
    background: #fafafa;

    .main-content {
      background: white;
      border-radius: 0;
      box-shadow: none;
      margin: 0;
      padding: 0;
    }
  }

  .mobile-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 60px !important;
  }

  .instagram-stories-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100px !important;
    padding: 8px 0 !important;
    background: #ffffff !important;
    border-bottom: 1px solid #dbdbdb !important;
  }

  // Show stories component on mobile (now responsive)
  app-view-add-stories {
    display: block !important;
    width: 100% !important;
    padding: 12px 0 !important;
    background: #ffffff !important;
    border-bottom: 1px solid #dbdbdb !important;
  }

  .instagram-bottom-nav {
    display: flex !important;
  }

  .desktop-sidebar {
    display: none !important;
  }

  // Override for desktop screens - show sidebar on larger screens
  @media (min-width: 1025px) {
    .desktop-sidebar {
      display: block !important;
    }
  }

  .home-container {
    background: #ffffff !important;
    min-height: 100vh;
    padding: 0 !important;
  }

  .content-grid {
    grid-template-columns: 1fr !important;
    padding: 160px 0 60px 0 !important; // Account for header (60px) + stories (100px) + bottom nav (60px)
    background: #ffffff !important;
    gap: 0 !important;
    margin: 0 !important;
    max-width: 100% !important;
    min-height: calc(100vh - 220px) !important;
    overflow-x: hidden !important;
  }

  .main-content {
    background: #ffffff !important;
    color: #262626 !important;
    gap: 0;
    padding: 0 8px 40px 8px; // Add bottom padding to prevent button cutoff
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box; // Ensure padding doesn't affect width
    overflow: visible !important; // Ensure content isn't clipped
  }

  .mobile-sidebar {
    width: 90%;
    background: #ffffff;
    color: #262626;
  }

  .sidebar-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

@media (max-width: 480px) {
  .mobile-sidebar {
    width: 95%;
  }

  .mobile-header {
    .header-right {
      gap: 12px;
    }
  }

  // Extra small screens (phones in portrait)
  @media (max-width: 480px) {
    .content-grid {
      padding: 160px 0 60px 0 !important;
      min-height: calc(100vh - 220px) !important;
    }

    .instagram-stories-section {
      height: 100px !important;
      padding: 8px 0 !important;
    }

    // Show stories component on mobile (now responsive)
    app-view-add-stories {
      display: block !important;
      width: 100% !important;
      padding: 8px 0 !important;
    }
  }

  // Small screens (400px - common mobile testing width)
  @media (max-width: 400px) {
    .content-grid {
      padding: 160px 0 60px 0 !important;
      min-height: calc(100vh - 220px) !important;
    }

    .instagram-stories-section {
      height: 100px !important;
      padding: 8px 0 !important;
    }

    // Show stories component on mobile (now responsive)
    app-view-add-stories {
      display: block !important;
      width: 100% !important;
      padding: 8px 0 !important;
    }

    .main-content {
      width: 100% !important;
      max-width: 100% !important;
      overflow-x: hidden !important;
    }
  }

  // Very small screens (360px and below)
  @media (max-width: 360px) {
    .content-grid {
      padding: 160px 0 80px 0 !important; // Increased bottom padding
      min-height: calc(100vh - 240px) !important;
    }

    .main-content {
      padding: 0 4px 50px 4px !important; // Extra bottom padding for buttons
      overflow: visible !important;
    }

    .instagram-stories-section {
      height: 100px !important;
      padding: 8px 0 !important;
    }

    // Show stories component on mobile (now responsive)
    app-view-add-stories {
      display: block !important;
      width: 100% !important;
      padding: 8px 0 !important;
    }
  }

  // Critical E-commerce Mobile Range (425px to 320px)
  @media (max-width: 425px) and (min-width: 320px) {
    .content-grid {
      padding: 160px 0 100px 0 !important; // Maximum bottom padding
      min-height: calc(100vh - 260px) !important;
    }

    .main-content {
      padding: 0 6px 60px 6px !important; // Generous bottom padding
      overflow: visible !important;
      position: relative;
    }

    // Ensure feed component has enough space
    app-feed {
      padding-bottom: 40px !important;
      overflow: visible !important;
    }
  }

  // Ultra small screens (320px)
  @media (max-width: 320px) {
    .content-grid {
      padding: 160px 0 120px 0 !important; // Maximum bottom padding
      min-height: calc(100vh - 280px) !important;
    }

    .main-content {
      padding: 0 2px 70px 2px !important; // Maximum bottom padding
      overflow: visible !important;
    }

    app-feed {
      padding-bottom: 50px !important;
      overflow: visible !important;
    }
  }
}

// Desktop Responsive Design
@media (min-width: 769px) {
  .mobile-header,
  .instagram-stories-section,
  .instagram-bottom-nav {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  .desktop-sidebar {
    display: block;
  }

  .mobile-sidebar,
  .sidebar-overlay {
    display: none !important;
  }

  .home-container {
    background: #ffffff;
    padding: 20px 20px 0 20px; // Remove top padding to eliminate blank space
  }

  .content-grid {
    background: #ffffff;
    color: #262626;
    padding: 0;
    margin: 0 auto;
    padding-top: 0; // Remove any top padding

    /* Responsive grid layout for desktop */
    grid-template-columns: 1fr 300px;
    gap: 32px;
    max-width: 1000px;

    /* Medium desktop (1024px and up) */
    @media (min-width: 1024px) {
      grid-template-columns: 1fr 350px;
      gap: 36px;
      max-width: 1200px;
    }

    /* Large desktop (1200px and up) */
    @media (min-width: 1200px) {
      grid-template-columns: 1fr 400px;
      gap: 40px;
      max-width: 1400px;
    }

    /* Extra large screens (1440px and up) */
    @media (min-width: 1440px) {
      grid-template-columns: 1fr 450px;
      gap: 48px;
      max-width: 1600px;
    }
  }

  .main-content {
    background: #ffffff;
    color: #262626;
  }

  .instagram-tab-menu,
  .tab-menu-overlay,
  .sidebar-content-modal {
    display: none !important;
  }
}

// High-specificity override for mobile-instagram content-grid (320px to 768px)
// This ensures maximum CSS specificity for web responsive design
@media (min-width: 320px) and (max-width: 768px) {
  .home-container.mobile-instagram .content-grid {
    grid-template-columns: 1fr !important;
    padding: 5px 0 !important;
    background: #ffffff !important;
    gap: 0 !important;
    margin: 0 !important;
    max-width: 100% !important;
    min-height: calc(100vh - 220px) !important;
    overflow-x: hidden !important;
    position: relative !important;
    z-index: 1 !important;
  }
}